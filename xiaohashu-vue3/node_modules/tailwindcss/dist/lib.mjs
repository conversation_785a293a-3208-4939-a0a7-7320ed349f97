import{a as Be,b as j,c as I,d as S,e as Ne,f as oe,g as be,h as Je}from"./chunk-V2K3XTS4.mjs";import{a as qe}from"./chunk-AZANAYY2.mjs";import{a as ee,b as ye,c as we,d as Me,e as M,f as F,g as O,h as W,i as l,j as Le,k as Z,l as D,m as _,n as We,o as te,p as J}from"./chunk-XOUGALJ2.mjs";var Ge="4.0.6";function Se(t){return{kind:"word",value:t}}function ir(t,r){return{kind:"function",value:t,nodes:r}}function or(t){return{kind:"separator",value:t}}function Y(t,r,i=null){for(let e=0;e<t.length;e++){let o=t[e],c=!1,s=0,m=r(o,{parent:i,replaceWith(p){c=!0,Array.isArray(p)?p.length===0?(t.splice(e,1),s=0):p.length===1?(t[e]=p[0],s=1):(t.splice(e,1,...p),s=p.length):t[e]=p}})??0;if(c){m===0?e--:e+=s-1;continue}if(m===2)return 2;if(m!==1&&o.kind==="function"&&Y(o.nodes,r,o)===2)return 2}}function B(t){let r="";for(let i of t)switch(i.kind){case"word":case"separator":{r+=i.value;break}case"function":r+=i.value+"("+B(i.nodes)+")"}return r}var He=92,lr=41,Ze=58,Ye=44,ar=34,Xe=61,Qe=62,et=60,tt=10,sr=40,ur=39,rt=47,nt=32,it=9;function z(t){t=t.replaceAll(`\r
`,`
`);let r=[],i=[],e=null,o="",c;for(let s=0;s<t.length;s++){let m=t.charCodeAt(s);switch(m){case He:{o+=t[s]+t[s+1],s++;break}case Ze:case Ye:case Xe:case Qe:case et:case tt:case rt:case nt:case it:{if(o.length>0){let h=Se(o);e?e.nodes.push(h):r.push(h),o=""}let p=s,u=s+1;for(;u<t.length&&(c=t.charCodeAt(u),!(c!==Ze&&c!==Ye&&c!==Xe&&c!==Qe&&c!==et&&c!==tt&&c!==rt&&c!==nt&&c!==it));u++);s=u-1;let d=or(t.slice(p,u));e?e.nodes.push(d):r.push(d);break}case ur:case ar:{let p=s;for(let u=s+1;u<t.length;u++)if(c=t.charCodeAt(u),c===He)u+=1;else if(c===m){s=u;break}o+=t.slice(p,s+1);break}case sr:{let p=ir(o,[]);o="",e?e.nodes.push(p):r.push(p),i.push(p),e=p;break}case lr:{let p=i.pop();if(o.length>0){let u=Se(o);p.nodes.push(u),o=""}i.length>0?e=i[i.length-1]:e=null;break}default:o+=String.fromCharCode(m)}}return o.length>0&&r.push(Se(o)),r}function X(t){if(t.indexOf("(")===-1)return le(t);let r=z(t);return Ee(r),t=B(r),t=Be(t),t}function le(t,r=!1){let i="";for(let e=0;e<t.length;e++){let o=t[e];o==="\\"&&t[e+1]==="_"?(i+="_",e+=1):o==="_"&&!r?i+=" ":i+=o}return i}function Ee(t){for(let r of t)switch(r.kind){case"function":{if(r.value==="url"||r.value.endsWith("_url")){r.value=le(r.value);break}if(r.value==="var"||r.value.endsWith("_var")||r.value==="theme"||r.value.endsWith("_theme")){r.value=le(r.value);for(let i=0;i<r.nodes.length;i++){if(i==0&&r.nodes[i].kind==="word"){r.nodes[i].value=le(r.nodes[i].value,!0);continue}Ee([r.nodes[i]])}break}r.value=le(r.value),Ee(r.nodes);break}case"separator":case"word":{r.value=le(r.value);break}default:fr(r)}}function fr(t){throw new Error(`Unexpected value: ${t}`)}var cr=58,ot=45,lt=97,at=122;function*st(t,r){let i=j(t,":");if(r.theme.prefix){if(i.length===1||i[0]!==r.theme.prefix)return null;i.shift()}let e=i.pop(),o=[];for(let h=i.length-1;h>=0;--h){let g=r.parseVariant(i[h]);if(g===null)return;o.push(g)}let c=!1;e[e.length-1]==="!"?(c=!0,e=e.slice(0,-1)):e[0]==="!"&&(c=!0,e=e.slice(1)),r.utilities.has(e,"static")&&!e.includes("[")&&(yield{kind:"static",root:e,variants:o,important:c,raw:t});let[s,m=null,p]=j(e,"/");if(p)return;let u=m===null?null:Ke(m);if(m!==null&&u===null)return;if(s[0]==="["){if(s[s.length-1]!=="]")return;let h=s.charCodeAt(1);if(h!==ot&&!(h>=lt&&h<=at))return;s=s.slice(1,-1);let g=s.indexOf(":");if(g===-1||g===0||g===s.length-1)return;let y=s.slice(0,g),w=X(s.slice(g+1));yield{kind:"arbitrary",property:y,value:w,modifier:u,variants:o,important:c,raw:t};return}let d;if(s[s.length-1]==="]"){let h=s.indexOf("-[");if(h===-1)return;let g=s.slice(0,h);if(!r.utilities.has(g,"functional"))return;let y=s.slice(h+1);d=[[g,y]]}else if(s[s.length-1]===")"){let h=s.indexOf("-(");if(h===-1)return;let g=s.slice(0,h);if(!r.utilities.has(g,"functional"))return;let y=s.slice(h+2,-1),w=j(y,":"),v=null;if(w.length===2&&(v=w[0],y=w[1]),y[0]!=="-"&&y[1]!=="-")return;d=[[g,v===null?`[var(${y})]`:`[${v}:var(${y})]`]]}else d=ft(s,h=>r.utilities.has(h,"functional"));for(let[h,g]of d){let y={kind:"functional",root:h,modifier:u,value:null,variants:o,important:c,raw:t};if(g===null){yield y;continue}{let w=g.indexOf("[");if(w!==-1){if(g[g.length-1]!=="]")return;let x=X(g.slice(w+1,-1)),T="";for(let N=0;N<x.length;N++){let R=x.charCodeAt(N);if(R===cr){T=x.slice(0,N),x=x.slice(N+1);break}if(!(R===ot||R>=lt&&R<=at))break}if(x.length===0||x.trim().length===0)continue;y.value={kind:"arbitrary",dataType:T||null,value:x}}else{let x=m===null||y.modifier?.kind==="arbitrary"?null:`${g}/${m}`;y.value={kind:"named",value:g,fraction:x}}}yield y}}function Ke(t){if(t[0]==="["&&t[t.length-1]==="]"){let r=X(t.slice(1,-1));return r.length===0||r.trim().length===0?null:{kind:"arbitrary",value:r}}if(t[0]==="("&&t[t.length-1]===")"){let r=X(t.slice(1,-1));return r.length===0||r.trim().length===0||r[0]!=="-"&&r[1]!=="-"?null:{kind:"arbitrary",value:`var(${r})`}}return{kind:"named",value:t}}function ut(t,r){if(t[0]==="["&&t[t.length-1]==="]"){if(t[1]==="@"&&t.includes("&"))return null;let i=X(t.slice(1,-1));if(i.length===0||i.trim().length===0)return null;let e=i[0]===">"||i[0]==="+"||i[0]==="~";return!e&&i[0]!=="@"&&!i.includes("&")&&(i=`&:is(${i})`),{kind:"arbitrary",selector:i,relative:e}}{let[i,e=null,o]=j(t,"/");if(o)return null;let c=ft(i,s=>r.variants.has(s));for(let[s,m]of c)switch(r.variants.kind(s)){case"static":return m!==null||e!==null?null:{kind:"static",root:s};case"functional":{let p=e===null?null:Ke(e);if(e!==null&&p===null)return null;if(m===null)return{kind:"functional",root:s,modifier:p,value:null};if(m[m.length-1]==="]"){if(m[0]!=="[")continue;let u=X(m.slice(1,-1));return u.length===0||u.trim().length===0?null:{kind:"functional",root:s,modifier:p,value:{kind:"arbitrary",value:u}}}if(m[m.length-1]===")"){if(m[0]!=="(")continue;let u=X(m.slice(1,-1));return u.length===0||u.trim().length===0||u[0]!=="-"&&u[1]!=="-"?null:{kind:"functional",root:s,modifier:p,value:{kind:"arbitrary",value:`var(${u})`}}}return{kind:"functional",root:s,modifier:p,value:{kind:"named",value:m}}}case"compound":{if(m===null)return null;let p=r.parseVariant(m);if(p===null||!r.variants.compoundsWith(s,p))return null;let u=e===null?null:Ke(e);return e!==null&&u===null?null:{kind:"compound",root:s,modifier:u,variant:p}}}}return null}function*ft(t,r){r(t)&&(yield[t,null]);let i=t.lastIndexOf("-");if(i===-1){t[0]==="@"&&r("@")&&(yield["@",t.slice(1)]);return}do{let e=t.slice(0,i);if(r(e)){let o=[e,t.slice(i+1)];if(o[1]==="")break;yield o}i=t.lastIndexOf("-",i-1)}while(i>0)}function re(t,r,i){if(t===r)return 0;let e=t.indexOf("("),o=r.indexOf("("),c=e===-1?t.replace(/[\d.]+/g,""):t.slice(0,e),s=o===-1?r.replace(/[\d.]+/g,""):r.slice(0,o),m=(c===s?0:c<s?-1:1)||(i==="asc"?parseInt(t)-parseInt(r):parseInt(r)-parseInt(t));return Number.isNaN(m)?t<r?-1:1:m}var dr=new Set(["inset","inherit","initial","revert","unset"]),ct=/^-?(\d+|\.\d+)(.*?)$/g;function ne(t,r){return j(t,",").map(e=>{e=e.trim();let o=j(e," ").filter(u=>u.trim()!==""),c=null,s=null,m=null;for(let u of o)dr.has(u)||(ct.test(u)?(s===null?s=u:m===null&&(m=u),ct.lastIndex=0):c===null&&(c=u));if(s===null||m===null)return e;let p=r(c??"currentcolor");return c!==null?e.replace(c,p):`${e} ${p}`}).join(", ")}var gr=/^-?[a-z][a-zA-Z0-9/%._-]*$/,mr=/^-?[a-z][a-zA-Z0-9/%._-]*-\*$/,Re=class{utilities=new M(()=>[]);completions=new Map;static(r,i){this.utilities.get(r).push({kind:"static",compileFn:i})}functional(r,i,e){this.utilities.get(r).push({kind:"functional",compileFn:i,options:e})}has(r,i){return this.utilities.has(r)&&this.utilities.get(r).some(e=>e.kind===i)}get(r){return this.utilities.has(r)?this.utilities.get(r):[]}getCompletions(r){return this.completions.get(r)?.()??[]}suggest(r,i){this.completions.set(r,i)}keys(r){let i=[];for(let[e,o]of this.utilities.entries())for(let c of o)if(c.kind===r){i.push(e);break}return i}};function V(t,r,i){return O("@property",t,[l("syntax",i?`"${i}"`:'"*"'),l("inherits","false"),...r?[l("initial-value",r)]:[]])}function G(t,r){if(r===null)return t;let i=Number(r);return Number.isNaN(i)||(r=`${i*100}%`),`color-mix(in oklab, ${t} ${r}, transparent)`}function L(t,r,i){if(!r)return t;if(r.kind==="arbitrary")return G(t,r.value);let e=i.resolve(r.value,["--opacity"]);return e?G(t,e):be(r.value)?G(t,`${r.value}%`):null}function q(t,r,i){let e=null;switch(t.value.value){case"inherit":{e="inherit";break}case"transparent":{e="transparent";break}case"current":{e="currentColor";break}default:{e=r.resolve(t.value.value,i);break}}return e?L(e,t.modifier,r):null}function pt(t){let r=new Re;function i(n,a){function*f(k){for(let b of t.keysInNamespaces(k))yield b.replaceAll("_",".")}r.suggest(n,()=>{let k=[];for(let b of a()){if(typeof b=="string"){k.push({values:[b],modifiers:[]});continue}let $=[...b.values??[],...f(b.valueThemeKeys??[])],K=[...b.modifiers??[],...f(b.modifierThemeKeys??[])];b.hasDefaultValue&&$.unshift(null),k.push({supportsNegative:b.supportsNegative,values:$,modifiers:K})}return k})}function e(n,a){r.static(n,()=>a.map(f=>typeof f=="function"?f():l(f[0],f[1])))}function o(n,a){function f({negative:k}){return b=>{let $=null;if(b.value)if(b.value.kind==="arbitrary"){if(b.modifier)return;$=b.value.value}else{if($=t.resolve(b.value.fraction??b.value.value,a.themeKeys??[]),$===null&&a.supportsFractions&&b.value.fraction){let[K,C]=j(b.value.fraction,"/");if(!S(K)||!S(C))return;$=`calc(${b.value.fraction} * 100%)`}if($===null&&k&&a.handleNegativeBareValue){if($=a.handleNegativeBareValue(b.value),!$?.includes("/")&&b.modifier)return;if($!==null)return a.handle($)}if($===null&&a.handleBareValue&&($=a.handleBareValue(b.value),!$?.includes("/")&&b.modifier))return}else{if(b.modifier)return;$=a.defaultValue!==void 0?a.defaultValue:t.resolve(null,a.themeKeys??[])}if($!==null)return a.handle(k?`calc(${$} * -1)`:$)}}a.supportsNegative&&r.functional(`-${n}`,f({negative:!0})),r.functional(n,f({negative:!1})),i(n,()=>[{supportsNegative:a.supportsNegative,valueThemeKeys:a.themeKeys??[],hasDefaultValue:a.defaultValue!==void 0&&a.defaultValue!==null}])}function c(n,a){r.functional(n,f=>{if(!f.value)return;let k=null;if(f.value.kind==="arbitrary"?(k=f.value.value,k=L(k,f.modifier,t)):k=q(f,t,a.themeKeys),k!==null)return a.handle(k)}),i(n,()=>[{values:["current","inherit","transparent"],valueThemeKeys:a.themeKeys,modifiers:Array.from({length:21},(f,k)=>`${k*5}`)}])}function s(n,a,f,{supportsNegative:k=!1,supportsFractions:b=!1}={}){k&&r.static(`-${n}-px`,()=>f("-1px")),r.static(`${n}-px`,()=>f("1px")),o(n,{themeKeys:a,supportsFractions:b,supportsNegative:k,defaultValue:null,handleBareValue:({value:$})=>{let K=t.resolve(null,["--spacing"]);return!K||!oe($)?null:`calc(${K} * ${$})`},handleNegativeBareValue:({value:$})=>{let K=t.resolve(null,["--spacing"]);return!K||!oe($)?null:`calc(${K} * -${$})`},handle:f}),i(n,()=>[{values:t.get(["--spacing"])?["0","0.5","1","1.5","2","2.5","3","3.5","4","5","6","7","8","9","10","11","12","14","16","20","24","28","32","36","40","44","48","52","56","60","64","72","80","96"]:[],supportsNegative:k,valueThemeKeys:a}])}e("sr-only",[["position","absolute"],["width","1px"],["height","1px"],["padding","0"],["margin","-1px"],["overflow","hidden"],["clip","rect(0, 0, 0, 0)"],["white-space","nowrap"],["border-width","0"]]),e("not-sr-only",[["position","static"],["width","auto"],["height","auto"],["padding","0"],["margin","0"],["overflow","visible"],["clip","auto"],["white-space","normal"]]),e("pointer-events-none",[["pointer-events","none"]]),e("pointer-events-auto",[["pointer-events","auto"]]),e("visible",[["visibility","visible"]]),e("invisible",[["visibility","hidden"]]),e("collapse",[["visibility","collapse"]]),e("static",[["position","static"]]),e("fixed",[["position","fixed"]]),e("absolute",[["position","absolute"]]),e("relative",[["position","relative"]]),e("sticky",[["position","sticky"]]);for(let[n,a]of[["inset","inset"],["inset-x","inset-inline"],["inset-y","inset-block"],["start","inset-inline-start"],["end","inset-inline-end"],["top","top"],["right","right"],["bottom","bottom"],["left","left"]])e(`${n}-auto`,[[a,"auto"]]),e(`${n}-full`,[[a,"100%"]]),e(`-${n}-full`,[[a,"-100%"]]),s(n,["--inset","--spacing"],f=>[l(a,f)],{supportsNegative:!0,supportsFractions:!0});e("isolate",[["isolation","isolate"]]),e("isolation-auto",[["isolation","auto"]]),e("z-auto",[["z-index","auto"]]),o("z",{supportsNegative:!0,handleBareValue:({value:n})=>S(n)?n:null,themeKeys:["--z-index"],handle:n=>[l("z-index",n)]}),i("z",()=>[{supportsNegative:!0,values:["0","10","20","30","40","50"],valueThemeKeys:["--z-index"]}]),e("order-first",[["order","-9999"]]),e("order-last",[["order","9999"]]),e("order-none",[["order","0"]]),o("order",{supportsNegative:!0,handleBareValue:({value:n})=>S(n)?n:null,themeKeys:["--order"],handle:n=>[l("order",n)]}),i("order",()=>[{supportsNegative:!0,values:Array.from({length:12},(n,a)=>`${a+1}`),valueThemeKeys:["--order"]}]),e("col-auto",[["grid-column","auto"]]),o("col",{themeKeys:["--grid-column"],handle:n=>[l("grid-column",n)]}),e("col-span-full",[["grid-column","1 / -1"]]),o("col-span",{handleBareValue:({value:n})=>S(n)?n:null,handle:n=>[l("grid-column",`span ${n} / span ${n}`)]}),e("col-start-auto",[["grid-column-start","auto"]]),o("col-start",{supportsNegative:!0,handleBareValue:({value:n})=>S(n)?n:null,themeKeys:["--grid-column-start"],handle:n=>[l("grid-column-start",n)]}),e("col-end-auto",[["grid-column-end","auto"]]),o("col-end",{supportsNegative:!0,handleBareValue:({value:n})=>S(n)?n:null,themeKeys:["--grid-column-end"],handle:n=>[l("grid-column-end",n)]}),i("col-span",()=>[{values:Array.from({length:12},(n,a)=>`${a+1}`),valueThemeKeys:[]}]),i("col-start",()=>[{supportsNegative:!0,values:Array.from({length:13},(n,a)=>`${a+1}`),valueThemeKeys:["--grid-column-start"]}]),i("col-end",()=>[{supportsNegative:!0,values:Array.from({length:13},(n,a)=>`${a+1}`),valueThemeKeys:["--grid-column-end"]}]),e("row-auto",[["grid-row","auto"]]),o("row",{themeKeys:["--grid-row"],handle:n=>[l("grid-row",n)]}),e("row-span-full",[["grid-row","1 / -1"]]),o("row-span",{themeKeys:[],handleBareValue:({value:n})=>S(n)?n:null,handle:n=>[l("grid-row",`span ${n} / span ${n}`)]}),e("row-start-auto",[["grid-row-start","auto"]]),o("row-start",{supportsNegative:!0,handleBareValue:({value:n})=>S(n)?n:null,themeKeys:["--grid-row-start"],handle:n=>[l("grid-row-start",n)]}),e("row-end-auto",[["grid-row-end","auto"]]),o("row-end",{supportsNegative:!0,handleBareValue:({value:n})=>S(n)?n:null,themeKeys:["--grid-row-end"],handle:n=>[l("grid-row-end",n)]}),i("row-span",()=>[{values:Array.from({length:12},(n,a)=>`${a+1}`),valueThemeKeys:[]}]),i("row-start",()=>[{supportsNegative:!0,values:Array.from({length:13},(n,a)=>`${a+1}`),valueThemeKeys:["--grid-row-start"]}]),i("row-end",()=>[{supportsNegative:!0,values:Array.from({length:13},(n,a)=>`${a+1}`),valueThemeKeys:["--grid-row-end"]}]),e("float-start",[["float","inline-start"]]),e("float-end",[["float","inline-end"]]),e("float-right",[["float","right"]]),e("float-left",[["float","left"]]),e("float-none",[["float","none"]]),e("clear-start",[["clear","inline-start"]]),e("clear-end",[["clear","inline-end"]]),e("clear-right",[["clear","right"]]),e("clear-left",[["clear","left"]]),e("clear-both",[["clear","both"]]),e("clear-none",[["clear","none"]]);for(let[n,a]of[["m","margin"],["mx","margin-inline"],["my","margin-block"],["ms","margin-inline-start"],["me","margin-inline-end"],["mt","margin-top"],["mr","margin-right"],["mb","margin-bottom"],["ml","margin-left"]])e(`${n}-auto`,[[a,"auto"]]),s(n,["--margin","--spacing"],f=>[l(a,f)],{supportsNegative:!0});e("box-border",[["box-sizing","border-box"]]),e("box-content",[["box-sizing","content-box"]]),e("line-clamp-none",[["overflow","visible"],["display","block"],["-webkit-box-orient","horizontal"],["-webkit-line-clamp","unset"]]),o("line-clamp",{themeKeys:["--line-clamp"],handleBareValue:({value:n})=>S(n)?n:null,handle:n=>[l("overflow","hidden"),l("display","-webkit-box"),l("-webkit-box-orient","vertical"),l("-webkit-line-clamp",n)]}),i("line-clamp",()=>[{values:["1","2","3","4","5","6"],valueThemeKeys:["--line-clamp"]}]),e("block",[["display","block"]]),e("inline-block",[["display","inline-block"]]),e("inline",[["display","inline"]]),e("hidden",[["display","none"]]),e("inline-flex",[["display","inline-flex"]]),e("table",[["display","table"]]),e("inline-table",[["display","inline-table"]]),e("table-caption",[["display","table-caption"]]),e("table-cell",[["display","table-cell"]]),e("table-column",[["display","table-column"]]),e("table-column-group",[["display","table-column-group"]]),e("table-footer-group",[["display","table-footer-group"]]),e("table-header-group",[["display","table-header-group"]]),e("table-row-group",[["display","table-row-group"]]),e("table-row",[["display","table-row"]]),e("flow-root",[["display","flow-root"]]),e("flex",[["display","flex"]]),e("grid",[["display","grid"]]),e("inline-grid",[["display","inline-grid"]]),e("contents",[["display","contents"]]),e("list-item",[["display","list-item"]]),e("field-sizing-content",[["field-sizing","content"]]),e("field-sizing-fixed",[["field-sizing","fixed"]]),e("aspect-auto",[["aspect-ratio","auto"]]),e("aspect-square",[["aspect-ratio","1 / 1"]]),o("aspect",{themeKeys:["--aspect"],handleBareValue:({fraction:n})=>{if(n===null)return null;let[a,f]=j(n,"/");return!S(a)||!S(f)?null:n},handle:n=>[l("aspect-ratio",n)]});for(let[n,a]of[["auto","auto"],["full","100%"],["svw","100svw"],["lvw","100lvw"],["dvw","100dvw"],["svh","100svh"],["lvh","100lvh"],["dvh","100dvh"],["min","min-content"],["max","max-content"],["fit","fit-content"]])e(`size-${n}`,[["--tw-sort","size"],["width",a],["height",a]]),e(`w-${n}`,[["width",a]]),e(`min-w-${n}`,[["min-width",a]]),e(`max-w-${n}`,[["max-width",a]]),e(`h-${n}`,[["height",a]]),e(`min-h-${n}`,[["min-height",a]]),e(`max-h-${n}`,[["max-height",a]]);e("w-screen",[["width","100vw"]]),e("min-w-screen",[["min-width","100vw"]]),e("max-w-screen",[["max-width","100vw"]]),e("h-screen",[["height","100vh"]]),e("min-h-screen",[["min-height","100vh"]]),e("max-h-screen",[["max-height","100vh"]]),e("max-w-none",[["max-width","none"]]),e("max-h-none",[["max-height","none"]]),s("size",["--size","--spacing"],n=>[l("--tw-sort","size"),l("width",n),l("height",n)],{supportsFractions:!0});for(let[n,a,f]of[["w",["--width","--spacing","--container"],"width"],["min-w",["--min-width","--spacing","--container"],"min-width"],["max-w",["--max-width","--spacing","--container"],"max-width"],["h",["--height","--spacing"],"height"],["min-h",["--min-height","--height","--spacing"],"min-height"],["max-h",["--max-height","--height","--spacing"],"max-height"]])s(n,a,k=>[l(f,k)],{supportsFractions:!0});r.static("container",()=>{let n=[...t.namespace("--breakpoint").values()];n.sort((f,k)=>re(f,k,"asc"));let a=[l("--tw-sort","--tw-container-component"),l("width","100%")];for(let f of n)a.push(O("@media",`(width >= ${f})`,[l("max-width",f)]));return a}),e("flex-auto",[["flex","auto"]]),e("flex-initial",[["flex","0 auto"]]),e("flex-none",[["flex","none"]]),r.functional("flex",n=>{if(n.value){if(n.value.kind==="arbitrary")return n.modifier?void 0:[l("flex",n.value.value)];if(n.value.fraction){let[a,f]=j(n.value.fraction,"/");return!S(a)||!S(f)?void 0:[l("flex",`calc(${n.value.fraction} * 100%)`)]}if(S(n.value.value))return n.modifier?void 0:[l("flex",n.value.value)]}}),o("shrink",{defaultValue:"1",handleBareValue:({value:n})=>S(n)?n:null,handle:n=>[l("flex-shrink",n)]}),o("grow",{defaultValue:"1",handleBareValue:({value:n})=>S(n)?n:null,handle:n=>[l("flex-grow",n)]}),i("shrink",()=>[{values:["0"],valueThemeKeys:[],hasDefaultValue:!0}]),i("grow",()=>[{values:["0"],valueThemeKeys:[],hasDefaultValue:!0}]),e("basis-auto",[["flex-basis","auto"]]),e("basis-full",[["flex-basis","100%"]]),s("basis",["--flex-basis","--spacing","--container"],n=>[l("flex-basis",n)],{supportsFractions:!0}),e("table-auto",[["table-layout","auto"]]),e("table-fixed",[["table-layout","fixed"]]),e("caption-top",[["caption-side","top"]]),e("caption-bottom",[["caption-side","bottom"]]),e("border-collapse",[["border-collapse","collapse"]]),e("border-separate",[["border-collapse","separate"]]);let m=()=>D([V("--tw-border-spacing-x","0","<length>"),V("--tw-border-spacing-y","0","<length>")]);s("border-spacing",["--border-spacing","--spacing"],n=>[m(),l("--tw-border-spacing-x",n),l("--tw-border-spacing-y",n),l("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),s("border-spacing-x",["--border-spacing","--spacing"],n=>[m(),l("--tw-border-spacing-x",n),l("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),s("border-spacing-y",["--border-spacing","--spacing"],n=>[m(),l("--tw-border-spacing-y",n),l("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),e("origin-center",[["transform-origin","center"]]),e("origin-top",[["transform-origin","top"]]),e("origin-top-right",[["transform-origin","top right"]]),e("origin-right",[["transform-origin","right"]]),e("origin-bottom-right",[["transform-origin","bottom right"]]),e("origin-bottom",[["transform-origin","bottom"]]),e("origin-bottom-left",[["transform-origin","bottom left"]]),e("origin-left",[["transform-origin","left"]]),e("origin-top-left",[["transform-origin","top left"]]),o("origin",{themeKeys:["--transform-origin"],handle:n=>[l("transform-origin",n)]}),e("perspective-origin-center",[["perspective-origin","center"]]),e("perspective-origin-top",[["perspective-origin","top"]]),e("perspective-origin-top-right",[["perspective-origin","top right"]]),e("perspective-origin-right",[["perspective-origin","right"]]),e("perspective-origin-bottom-right",[["perspective-origin","bottom right"]]),e("perspective-origin-bottom",[["perspective-origin","bottom"]]),e("perspective-origin-bottom-left",[["perspective-origin","bottom left"]]),e("perspective-origin-left",[["perspective-origin","left"]]),e("perspective-origin-top-left",[["perspective-origin","top left"]]),o("perspective-origin",{themeKeys:["--perspective-origin"],handle:n=>[l("perspective-origin",n)]}),e("perspective-none",[["perspective","none"]]),o("perspective",{themeKeys:["--perspective"],handle:n=>[l("perspective",n)]});let p=()=>D([V("--tw-translate-x","0"),V("--tw-translate-y","0"),V("--tw-translate-z","0")]);e("translate-none",[["translate","none"]]),e("-translate-full",[p,["--tw-translate-x","-100%"],["--tw-translate-y","-100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),e("translate-full",[p,["--tw-translate-x","100%"],["--tw-translate-y","100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),s("translate",["--translate","--spacing"],n=>[p(),l("--tw-translate-x",n),l("--tw-translate-y",n),l("translate","var(--tw-translate-x) var(--tw-translate-y)")],{supportsNegative:!0,supportsFractions:!0});for(let n of["x","y"])e(`-translate-${n}-full`,[p,[`--tw-translate-${n}`,"-100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),e(`translate-${n}-full`,[p,[`--tw-translate-${n}`,"100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),s(`translate-${n}`,["--translate","--spacing"],a=>[p(),l(`--tw-translate-${n}`,a),l("translate","var(--tw-translate-x) var(--tw-translate-y)")],{supportsNegative:!0,supportsFractions:!0});s("translate-z",["--translate","--spacing"],n=>[p(),l("--tw-translate-z",n),l("translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)")],{supportsNegative:!0}),e("-translate-z-px",[p,["--tw-translate-z","-1px"],["translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)"]]),e("translate-z-px",[p,["--tw-translate-z","1px"],["translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)"]]),e("translate-3d",[p,["translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)"]]);let u=()=>D([V("--tw-scale-x","1"),V("--tw-scale-y","1"),V("--tw-scale-z","1")]);e("scale-none",[["scale","none"]]);function d({negative:n}){return a=>{if(!a.value||a.modifier)return;let f;return a.value.kind==="arbitrary"?(f=a.value.value,[l("scale",f)]):(f=t.resolve(a.value.value,["--scale"]),!f&&S(a.value.value)&&(f=`${a.value.value}%`),f?(f=n?`calc(${f} * -1)`:f,[u(),l("--tw-scale-x",f),l("--tw-scale-y",f),l("--tw-scale-z",f),l("scale","var(--tw-scale-x) var(--tw-scale-y)")]):void 0)}}r.functional("-scale",d({negative:!0})),r.functional("scale",d({negative:!1})),i("scale",()=>[{supportsNegative:!0,values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--scale"]}]);for(let n of["x","y","z"])o(`scale-${n}`,{supportsNegative:!0,themeKeys:["--scale"],handleBareValue:({value:a})=>S(a)?`${a}%`:null,handle:a=>[u(),l(`--tw-scale-${n}`,a),l("scale",`var(--tw-scale-x) var(--tw-scale-y)${n==="z"?" var(--tw-scale-z)":""}`)]}),i(`scale-${n}`,()=>[{supportsNegative:!0,values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--scale"]}]);e("scale-3d",[u,["scale","var(--tw-scale-x) var(--tw-scale-y) var(--tw-scale-z)"]]),e("rotate-none",[["rotate","none"]]);function h({negative:n}){return a=>{if(!a.value||a.modifier)return;let f;if(a.value.kind==="arbitrary"){f=a.value.value;let k=a.value.dataType??I(f,["angle","vector"]);if(k==="vector")return[l("rotate",`${f} var(--tw-rotate)`)];if(k!=="angle")return[l("rotate",f)]}else if(f=t.resolve(a.value.value,["--rotate"]),!f&&S(a.value.value)&&(f=`${a.value.value}deg`),!f)return;return[l("rotate",n?`calc(${f} * -1)`:f)]}}r.functional("-rotate",h({negative:!0})),r.functional("rotate",h({negative:!1})),i("rotate",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"],valueThemeKeys:["--rotate"]}]);{let n=["var(--tw-rotate-x)","var(--tw-rotate-y)","var(--tw-rotate-z)","var(--tw-skew-x)","var(--tw-skew-y)"].join(" "),a=()=>D([V("--tw-rotate-x","rotateX(0)"),V("--tw-rotate-y","rotateY(0)"),V("--tw-rotate-z","rotateZ(0)"),V("--tw-skew-x","skewX(0)"),V("--tw-skew-y","skewY(0)")]);for(let f of["x","y","z"])o(`rotate-${f}`,{supportsNegative:!0,themeKeys:["--rotate"],handleBareValue:({value:k})=>S(k)?`${k}deg`:null,handle:k=>[a(),l(`--tw-rotate-${f}`,`rotate${f.toUpperCase()}(${k})`),l("transform",n)]}),i(`rotate-${f}`,()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"],valueThemeKeys:["--rotate"]}]);o("skew",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:f})=>S(f)?`${f}deg`:null,handle:f=>[a(),l("--tw-skew-x",`skewX(${f})`),l("--tw-skew-y",`skewY(${f})`),l("transform",n)]}),o("skew-x",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:f})=>S(f)?`${f}deg`:null,handle:f=>[a(),l("--tw-skew-x",`skewX(${f})`),l("transform",n)]}),o("skew-y",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:f})=>S(f)?`${f}deg`:null,handle:f=>[a(),l("--tw-skew-y",`skewY(${f})`),l("transform",n)]}),i("skew",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),i("skew-x",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),i("skew-y",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),r.functional("transform",f=>{if(f.modifier)return;let k=null;if(f.value?f.value.kind==="arbitrary"&&(k=f.value.value):k=n,k!==null)return[a(),l("transform",k)]}),i("transform",()=>[{hasDefaultValue:!0}]),e("transform-cpu",[["transform",n]]),e("transform-gpu",[["transform",`translateZ(0) ${n}`]]),e("transform-none",[["transform","none"]])}e("transform-flat",[["transform-style","flat"]]),e("transform-3d",[["transform-style","preserve-3d"]]),e("transform-content",[["transform-box","content-box"]]),e("transform-border",[["transform-box","border-box"]]),e("transform-fill",[["transform-box","fill-box"]]),e("transform-stroke",[["transform-box","stroke-box"]]),e("transform-view",[["transform-box","view-box"]]),e("backface-visible",[["backface-visibility","visible"]]),e("backface-hidden",[["backface-visibility","hidden"]]);for(let n of["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out"])e(`cursor-${n}`,[["cursor",n]]);o("cursor",{themeKeys:["--cursor"],handle:n=>[l("cursor",n)]});for(let n of["auto","none","manipulation"])e(`touch-${n}`,[["touch-action",n]]);let g=()=>D([V("--tw-pan-x"),V("--tw-pan-y"),V("--tw-pinch-zoom")]);for(let n of["x","left","right"])e(`touch-pan-${n}`,[g,["--tw-pan-x",`pan-${n}`],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);for(let n of["y","up","down"])e(`touch-pan-${n}`,[g,["--tw-pan-y",`pan-${n}`],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);e("touch-pinch-zoom",[g,["--tw-pinch-zoom","pinch-zoom"],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);for(let n of["none","text","all","auto"])e(`select-${n}`,[["-webkit-user-select",n],["user-select",n]]);e("resize-none",[["resize","none"]]),e("resize-x",[["resize","horizontal"]]),e("resize-y",[["resize","vertical"]]),e("resize",[["resize","both"]]),e("snap-none",[["scroll-snap-type","none"]]);let y=()=>D([V("--tw-scroll-snap-strictness","proximity","*")]);for(let n of["x","y","both"])e(`snap-${n}`,[y,["scroll-snap-type",`${n} var(--tw-scroll-snap-strictness)`]]);e("snap-mandatory",[y,["--tw-scroll-snap-strictness","mandatory"]]),e("snap-proximity",[y,["--tw-scroll-snap-strictness","proximity"]]),e("snap-align-none",[["scroll-snap-align","none"]]),e("snap-start",[["scroll-snap-align","start"]]),e("snap-end",[["scroll-snap-align","end"]]),e("snap-center",[["scroll-snap-align","center"]]),e("snap-normal",[["scroll-snap-stop","normal"]]),e("snap-always",[["scroll-snap-stop","always"]]);for(let[n,a]of[["scroll-m","scroll-margin"],["scroll-mx","scroll-margin-inline"],["scroll-my","scroll-margin-block"],["scroll-ms","scroll-margin-inline-start"],["scroll-me","scroll-margin-inline-end"],["scroll-mt","scroll-margin-top"],["scroll-mr","scroll-margin-right"],["scroll-mb","scroll-margin-bottom"],["scroll-ml","scroll-margin-left"]])s(n,["--scroll-margin","--spacing"],f=>[l(a,f)],{supportsNegative:!0});for(let[n,a]of[["scroll-p","scroll-padding"],["scroll-px","scroll-padding-inline"],["scroll-py","scroll-padding-block"],["scroll-ps","scroll-padding-inline-start"],["scroll-pe","scroll-padding-inline-end"],["scroll-pt","scroll-padding-top"],["scroll-pr","scroll-padding-right"],["scroll-pb","scroll-padding-bottom"],["scroll-pl","scroll-padding-left"]])s(n,["--scroll-padding","--spacing"],f=>[l(a,f)]);e("list-inside",[["list-style-position","inside"]]),e("list-outside",[["list-style-position","outside"]]),e("list-none",[["list-style-type","none"]]),e("list-disc",[["list-style-type","disc"]]),e("list-decimal",[["list-style-type","decimal"]]),o("list",{themeKeys:["--list-style-type"],handle:n=>[l("list-style-type",n)]}),e("list-image-none",[["list-style-image","none"]]),o("list-image",{themeKeys:["--list-style-image"],handle:n=>[l("list-style-image",n)]}),e("appearance-none",[["appearance","none"]]),e("appearance-auto",[["appearance","auto"]]),e("scheme-normal",[["color-scheme","normal"]]),e("scheme-dark",[["color-scheme","dark"]]),e("scheme-light",[["color-scheme","light"]]),e("scheme-light-dark",[["color-scheme","light dark"]]),e("scheme-only-dark",[["color-scheme","only dark"]]),e("scheme-only-light",[["color-scheme","only light"]]),e("columns-auto",[["columns","auto"]]),o("columns",{themeKeys:["--columns","--container"],handleBareValue:({value:n})=>S(n)?n:null,handle:n=>[l("columns",n)]}),i("columns",()=>[{values:Array.from({length:12},(n,a)=>`${a+1}`),valueThemeKeys:["--columns","--container"]}]);for(let n of["auto","avoid","all","avoid-page","page","left","right","column"])e(`break-before-${n}`,[["break-before",n]]);for(let n of["auto","avoid","avoid-page","avoid-column"])e(`break-inside-${n}`,[["break-inside",n]]);for(let n of["auto","avoid","all","avoid-page","page","left","right","column"])e(`break-after-${n}`,[["break-after",n]]);e("grid-flow-row",[["grid-auto-flow","row"]]),e("grid-flow-col",[["grid-auto-flow","column"]]),e("grid-flow-dense",[["grid-auto-flow","dense"]]),e("grid-flow-row-dense",[["grid-auto-flow","row dense"]]),e("grid-flow-col-dense",[["grid-auto-flow","column dense"]]),e("auto-cols-auto",[["grid-auto-columns","auto"]]),e("auto-cols-min",[["grid-auto-columns","min-content"]]),e("auto-cols-max",[["grid-auto-columns","max-content"]]),e("auto-cols-fr",[["grid-auto-columns","minmax(0, 1fr)"]]),o("auto-cols",{themeKeys:["--grid-auto-columns"],handle:n=>[l("grid-auto-columns",n)]}),e("auto-rows-auto",[["grid-auto-rows","auto"]]),e("auto-rows-min",[["grid-auto-rows","min-content"]]),e("auto-rows-max",[["grid-auto-rows","max-content"]]),e("auto-rows-fr",[["grid-auto-rows","minmax(0, 1fr)"]]),o("auto-rows",{themeKeys:["--grid-auto-rows"],handle:n=>[l("grid-auto-rows",n)]}),e("grid-cols-none",[["grid-template-columns","none"]]),e("grid-cols-subgrid",[["grid-template-columns","subgrid"]]),o("grid-cols",{themeKeys:["--grid-template-columns"],handleBareValue:({value:n})=>Ne(n)?`repeat(${n}, minmax(0, 1fr))`:null,handle:n=>[l("grid-template-columns",n)]}),e("grid-rows-none",[["grid-template-rows","none"]]),e("grid-rows-subgrid",[["grid-template-rows","subgrid"]]),o("grid-rows",{themeKeys:["--grid-template-rows"],handleBareValue:({value:n})=>Ne(n)?`repeat(${n}, minmax(0, 1fr))`:null,handle:n=>[l("grid-template-rows",n)]}),i("grid-cols",()=>[{values:Array.from({length:12},(n,a)=>`${a+1}`),valueThemeKeys:["--grid-template-columns"]}]),i("grid-rows",()=>[{values:Array.from({length:12},(n,a)=>`${a+1}`),valueThemeKeys:["--grid-template-rows"]}]),e("flex-row",[["flex-direction","row"]]),e("flex-row-reverse",[["flex-direction","row-reverse"]]),e("flex-col",[["flex-direction","column"]]),e("flex-col-reverse",[["flex-direction","column-reverse"]]),e("flex-wrap",[["flex-wrap","wrap"]]),e("flex-nowrap",[["flex-wrap","nowrap"]]),e("flex-wrap-reverse",[["flex-wrap","wrap-reverse"]]),e("place-content-center",[["place-content","center"]]),e("place-content-start",[["place-content","start"]]),e("place-content-end",[["place-content","end"]]),e("place-content-between",[["place-content","space-between"]]),e("place-content-around",[["place-content","space-around"]]),e("place-content-evenly",[["place-content","space-evenly"]]),e("place-content-baseline",[["place-content","baseline"]]),e("place-content-stretch",[["place-content","stretch"]]),e("place-items-center",[["place-items","center"]]),e("place-items-start",[["place-items","start"]]),e("place-items-end",[["place-items","end"]]),e("place-items-baseline",[["place-items","baseline"]]),e("place-items-stretch",[["place-items","stretch"]]),e("content-normal",[["align-content","normal"]]),e("content-center",[["align-content","center"]]),e("content-start",[["align-content","flex-start"]]),e("content-end",[["align-content","flex-end"]]),e("content-between",[["align-content","space-between"]]),e("content-around",[["align-content","space-around"]]),e("content-evenly",[["align-content","space-evenly"]]),e("content-baseline",[["align-content","baseline"]]),e("content-stretch",[["align-content","stretch"]]),e("items-center",[["align-items","center"]]),e("items-start",[["align-items","flex-start"]]),e("items-end",[["align-items","flex-end"]]),e("items-baseline",[["align-items","baseline"]]),e("items-stretch",[["align-items","stretch"]]),e("justify-normal",[["justify-content","normal"]]),e("justify-center",[["justify-content","center"]]),e("justify-start",[["justify-content","flex-start"]]),e("justify-end",[["justify-content","flex-end"]]),e("justify-between",[["justify-content","space-between"]]),e("justify-around",[["justify-content","space-around"]]),e("justify-evenly",[["justify-content","space-evenly"]]),e("justify-baseline",[["justify-content","baseline"]]),e("justify-stretch",[["justify-content","stretch"]]),e("justify-items-normal",[["justify-items","normal"]]),e("justify-items-center",[["justify-items","center"]]),e("justify-items-start",[["justify-items","start"]]),e("justify-items-end",[["justify-items","end"]]),e("justify-items-stretch",[["justify-items","stretch"]]),s("gap",["--gap","--spacing"],n=>[l("gap",n)]),s("gap-x",["--gap","--spacing"],n=>[l("column-gap",n)]),s("gap-y",["--gap","--spacing"],n=>[l("row-gap",n)]),s("space-x",["--space","--spacing"],n=>[D([V("--tw-space-x-reverse","0")]),F(":where(& > :not(:last-child))",[l("--tw-sort","row-gap"),l("--tw-space-x-reverse","0"),l("margin-inline-start",`calc(${n} * var(--tw-space-x-reverse))`),l("margin-inline-end",`calc(${n} * calc(1 - var(--tw-space-x-reverse)))`)])],{supportsNegative:!0}),s("space-y",["--space","--spacing"],n=>[D([V("--tw-space-y-reverse","0")]),F(":where(& > :not(:last-child))",[l("--tw-sort","column-gap"),l("--tw-space-y-reverse","0"),l("margin-block-start",`calc(${n} * var(--tw-space-y-reverse))`),l("margin-block-end",`calc(${n} * calc(1 - var(--tw-space-y-reverse)))`)])],{supportsNegative:!0}),e("space-x-reverse",[()=>D([V("--tw-space-x-reverse","0")]),()=>F(":where(& > :not(:last-child))",[l("--tw-sort","row-gap"),l("--tw-space-x-reverse","1")])]),e("space-y-reverse",[()=>D([V("--tw-space-y-reverse","0")]),()=>F(":where(& > :not(:last-child))",[l("--tw-sort","column-gap"),l("--tw-space-y-reverse","1")])]),e("accent-auto",[["accent-color","auto"]]),c("accent",{themeKeys:["--accent-color","--color"],handle:n=>[l("accent-color",n)]}),c("caret",{themeKeys:["--caret-color","--color"],handle:n=>[l("caret-color",n)]}),c("divide",{themeKeys:["--divide-color","--color"],handle:n=>[F(":where(& > :not(:last-child))",[l("--tw-sort","divide-color"),l("border-color",n)])]}),e("place-self-auto",[["place-self","auto"]]),e("place-self-start",[["place-self","start"]]),e("place-self-end",[["place-self","end"]]),e("place-self-center",[["place-self","center"]]),e("place-self-stretch",[["place-self","stretch"]]),e("self-auto",[["align-self","auto"]]),e("self-start",[["align-self","flex-start"]]),e("self-end",[["align-self","flex-end"]]),e("self-center",[["align-self","center"]]),e("self-stretch",[["align-self","stretch"]]),e("self-baseline",[["align-self","baseline"]]),e("justify-self-auto",[["justify-self","auto"]]),e("justify-self-start",[["justify-self","flex-start"]]),e("justify-self-end",[["justify-self","flex-end"]]),e("justify-self-center",[["justify-self","center"]]),e("justify-self-stretch",[["justify-self","stretch"]]);for(let n of["auto","hidden","clip","visible","scroll"])e(`overflow-${n}`,[["overflow",n]]),e(`overflow-x-${n}`,[["overflow-x",n]]),e(`overflow-y-${n}`,[["overflow-y",n]]);for(let n of["auto","contain","none"])e(`overscroll-${n}`,[["overscroll-behavior",n]]),e(`overscroll-x-${n}`,[["overscroll-behavior-x",n]]),e(`overscroll-y-${n}`,[["overscroll-behavior-y",n]]);e("scroll-auto",[["scroll-behavior","auto"]]),e("scroll-smooth",[["scroll-behavior","smooth"]]),e("truncate",[["overflow","hidden"],["text-overflow","ellipsis"],["white-space","nowrap"]]),e("text-ellipsis",[["text-overflow","ellipsis"]]),e("text-clip",[["text-overflow","clip"]]),e("hyphens-none",[["-webkit-hyphens","none"],["hyphens","none"]]),e("hyphens-manual",[["-webkit-hyphens","manual"],["hyphens","manual"]]),e("hyphens-auto",[["-webkit-hyphens","auto"],["hyphens","auto"]]),e("whitespace-normal",[["white-space","normal"]]),e("whitespace-nowrap",[["white-space","nowrap"]]),e("whitespace-pre",[["white-space","pre"]]),e("whitespace-pre-line",[["white-space","pre-line"]]),e("whitespace-pre-wrap",[["white-space","pre-wrap"]]),e("whitespace-break-spaces",[["white-space","break-spaces"]]),e("text-wrap",[["text-wrap","wrap"]]),e("text-nowrap",[["text-wrap","nowrap"]]),e("text-balance",[["text-wrap","balance"]]),e("text-pretty",[["text-wrap","pretty"]]),e("break-normal",[["overflow-wrap","normal"],["word-break","normal"]]),e("break-words",[["overflow-wrap","break-word"]]),e("break-all",[["word-break","break-all"]]),e("break-keep",[["word-break","keep-all"]]);for(let[n,a]of[["rounded",["border-radius"]],["rounded-s",["border-start-start-radius","border-end-start-radius"]],["rounded-e",["border-start-end-radius","border-end-end-radius"]],["rounded-t",["border-top-left-radius","border-top-right-radius"]],["rounded-r",["border-top-right-radius","border-bottom-right-radius"]],["rounded-b",["border-bottom-right-radius","border-bottom-left-radius"]],["rounded-l",["border-top-left-radius","border-bottom-left-radius"]],["rounded-ss",["border-start-start-radius"]],["rounded-se",["border-start-end-radius"]],["rounded-ee",["border-end-end-radius"]],["rounded-es",["border-end-start-radius"]],["rounded-tl",["border-top-left-radius"]],["rounded-tr",["border-top-right-radius"]],["rounded-br",["border-bottom-right-radius"]],["rounded-bl",["border-bottom-left-radius"]]])e(`${n}-none`,a.map(f=>[f,"0"])),e(`${n}-full`,a.map(f=>[f,"calc(infinity * 1px)"])),o(n,{themeKeys:["--radius"],handle:f=>a.map(k=>l(k,f))});e("border-solid",[["--tw-border-style","solid"],["border-style","solid"]]),e("border-dashed",[["--tw-border-style","dashed"],["border-style","dashed"]]),e("border-dotted",[["--tw-border-style","dotted"],["border-style","dotted"]]),e("border-double",[["--tw-border-style","double"],["border-style","double"]]),e("border-hidden",[["--tw-border-style","hidden"],["border-style","hidden"]]),e("border-none",[["--tw-border-style","none"],["border-style","none"]]);{let a=function(f,k){r.functional(f,b=>{if(!b.value){if(b.modifier)return;let $=t.get(["--default-border-width"])??"1px",K=k.width($);return K?[n(),...K]:void 0}if(b.value.kind==="arbitrary"){let $=b.value.value;switch(b.value.dataType??I($,["color","line-width","length"])){case"line-width":case"length":{if(b.modifier)return;let C=k.width($);return C?[n(),...C]:void 0}default:return $=L($,b.modifier,t),$===null?void 0:k.color($)}}{let $=q(b,t,["--border-color","--color"]);if($)return k.color($)}{if(b.modifier)return;let $=t.resolve(b.value.value,["--border-width"]);if($){let K=k.width($);return K?[n(),...K]:void 0}if(S(b.value.value)){let K=k.width(`${b.value.value}px`);return K?[n(),...K]:void 0}}}),i(f,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--border-color","--color"],modifiers:Array.from({length:21},(b,$)=>`${$*5}`),hasDefaultValue:!0},{values:["0","2","4","8"],valueThemeKeys:["--border-width"]}])};var x=a;let n=()=>D([V("--tw-border-style","solid")]);a("border",{width:f=>[l("border-style","var(--tw-border-style)"),l("border-width",f)],color:f=>[l("border-color",f)]}),a("border-x",{width:f=>[l("border-inline-style","var(--tw-border-style)"),l("border-inline-width",f)],color:f=>[l("border-inline-color",f)]}),a("border-y",{width:f=>[l("border-block-style","var(--tw-border-style)"),l("border-block-width",f)],color:f=>[l("border-block-color",f)]}),a("border-s",{width:f=>[l("border-inline-start-style","var(--tw-border-style)"),l("border-inline-start-width",f)],color:f=>[l("border-inline-start-color",f)]}),a("border-e",{width:f=>[l("border-inline-end-style","var(--tw-border-style)"),l("border-inline-end-width",f)],color:f=>[l("border-inline-end-color",f)]}),a("border-t",{width:f=>[l("border-top-style","var(--tw-border-style)"),l("border-top-width",f)],color:f=>[l("border-top-color",f)]}),a("border-r",{width:f=>[l("border-right-style","var(--tw-border-style)"),l("border-right-width",f)],color:f=>[l("border-right-color",f)]}),a("border-b",{width:f=>[l("border-bottom-style","var(--tw-border-style)"),l("border-bottom-width",f)],color:f=>[l("border-bottom-color",f)]}),a("border-l",{width:f=>[l("border-left-style","var(--tw-border-style)"),l("border-left-width",f)],color:f=>[l("border-left-color",f)]}),o("divide-x",{defaultValue:t.get(["--default-border-width"])??"1px",themeKeys:["--divide-width","--border-width"],handleBareValue:({value:f})=>S(f)?`${f}px`:null,handle:f=>[D([V("--tw-divide-x-reverse","0")]),F(":where(& > :not(:last-child))",[l("--tw-sort","divide-x-width"),n(),l("--tw-divide-x-reverse","0"),l("border-inline-style","var(--tw-border-style)"),l("border-inline-start-width",`calc(${f} * var(--tw-divide-x-reverse))`),l("border-inline-end-width",`calc(${f} * calc(1 - var(--tw-divide-x-reverse)))`)])]}),o("divide-y",{defaultValue:t.get(["--default-border-width"])??"1px",themeKeys:["--divide-width","--border-width"],handleBareValue:({value:f})=>S(f)?`${f}px`:null,handle:f=>[D([V("--tw-divide-y-reverse","0")]),F(":where(& > :not(:last-child))",[l("--tw-sort","divide-y-width"),n(),l("--tw-divide-y-reverse","0"),l("border-bottom-style","var(--tw-border-style)"),l("border-top-style","var(--tw-border-style)"),l("border-top-width",`calc(${f} * var(--tw-divide-y-reverse))`),l("border-bottom-width",`calc(${f} * calc(1 - var(--tw-divide-y-reverse)))`)])]}),i("divide-x",()=>[{values:["0","2","4","8"],valueThemeKeys:["--divide-width","--border-width"],hasDefaultValue:!0}]),i("divide-y",()=>[{values:["0","2","4","8"],valueThemeKeys:["--divide-width","--border-width"],hasDefaultValue:!0}]),e("divide-x-reverse",[()=>D([V("--tw-divide-x-reverse","0")]),()=>F(":where(& > :not(:last-child))",[l("--tw-divide-x-reverse","1")])]),e("divide-y-reverse",[()=>D([V("--tw-divide-y-reverse","0")]),()=>F(":where(& > :not(:last-child))",[l("--tw-divide-y-reverse","1")])]);for(let f of["solid","dashed","dotted","double","none"])e(`divide-${f}`,[()=>F(":where(& > :not(:last-child))",[l("--tw-sort","divide-style"),l("--tw-border-style",f),l("border-style",f)])])}e("bg-auto",[["background-size","auto"]]),e("bg-cover",[["background-size","cover"]]),e("bg-contain",[["background-size","contain"]]),e("bg-fixed",[["background-attachment","fixed"]]),e("bg-local",[["background-attachment","local"]]),e("bg-scroll",[["background-attachment","scroll"]]),e("bg-center",[["background-position","center"]]),e("bg-top",[["background-position","top"]]),e("bg-right-top",[["background-position","right top"]]),e("bg-right",[["background-position","right"]]),e("bg-right-bottom",[["background-position","right bottom"]]),e("bg-bottom",[["background-position","bottom"]]),e("bg-left-bottom",[["background-position","left bottom"]]),e("bg-left",[["background-position","left"]]),e("bg-left-top",[["background-position","left top"]]),e("bg-repeat",[["background-repeat","repeat"]]),e("bg-no-repeat",[["background-repeat","no-repeat"]]),e("bg-repeat-x",[["background-repeat","repeat-x"]]),e("bg-repeat-y",[["background-repeat","repeat-y"]]),e("bg-repeat-round",[["background-repeat","round"]]),e("bg-repeat-space",[["background-repeat","space"]]),e("bg-none",[["background-image","none"]]);{let f=function($){let K="in oklab";if($?.kind==="named")switch($.value){case"longer":case"shorter":case"increasing":case"decreasing":K=`in oklch ${$.value} hue`;break;default:K=`in ${$.value}`}else $?.kind==="arbitrary"&&(K=$.value);return K},k=function({negative:$}){return K=>{if(!K.value)return;if(K.value.kind==="arbitrary"){if(K.modifier)return;let U=K.value.value;switch(K.value.dataType??I(U,["angle"])){case"angle":return U=$?`calc(${U} * -1)`:`${U}`,[l("--tw-gradient-position",U),l("background-image",`linear-gradient(var(--tw-gradient-stops,${U}))`)];default:return $?void 0:[l("--tw-gradient-position",U),l("background-image",`linear-gradient(var(--tw-gradient-stops,${U}))`)]}}let C=K.value.value;if(!$&&a.has(C))C=a.get(C);else if(S(C))C=$?`calc(${C}deg * -1)`:`${C}deg`;else return;let A=f(K.modifier);return[l("--tw-gradient-position",`${C} ${A}`),l("background-image","linear-gradient(var(--tw-gradient-stops))")]}},b=function({negative:$}){return K=>{if(K.value?.kind==="arbitrary"){if(K.modifier)return;let U=K.value.value;return[l("--tw-gradient-position",U),l("background-image",`conic-gradient(var(--tw-gradient-stops,${U}))`)]}let C=f(K.modifier);if(!K.value)return[l("--tw-gradient-position",C),l("background-image","conic-gradient(var(--tw-gradient-stops))")];let A=K.value.value;if(S(A))return A=$?`calc(${A} * -1)`:`${A}deg`,[l("--tw-gradient-position",`from ${A} ${C}`),l("background-image","conic-gradient(var(--tw-gradient-stops))")]}};var T=f,N=k,R=b;let n=["oklab","oklch","srgb","hsl","longer","shorter","increasing","decreasing"],a=new Map([["to-t","to top"],["to-tr","to top right"],["to-r","to right"],["to-br","to bottom right"],["to-b","to bottom"],["to-bl","to bottom left"],["to-l","to left"],["to-tl","to top left"]]);r.functional("-bg-linear",k({negative:!0})),r.functional("bg-linear",k({negative:!1})),i("bg-linear",()=>[{values:[...a.keys()],modifiers:n},{values:["0","30","60","90","120","150","180","210","240","270","300","330"],supportsNegative:!0,modifiers:n}]),r.functional("-bg-conic",b({negative:!0})),r.functional("bg-conic",b({negative:!1})),i("bg-conic",()=>[{hasDefaultValue:!0,modifiers:n},{values:["0","30","60","90","120","150","180","210","240","270","300","330"],supportsNegative:!0,modifiers:n}]),r.functional("bg-radial",$=>{if(!$.value){let K=f($.modifier);return[l("--tw-gradient-position",K),l("background-image","radial-gradient(var(--tw-gradient-stops))")]}if($.value.kind==="arbitrary"){if($.modifier)return;let K=$.value.value;return[l("--tw-gradient-position",K),l("background-image",`radial-gradient(var(--tw-gradient-stops,${K}))`)]}}),i("bg-radial",()=>[{hasDefaultValue:!0,modifiers:n}])}r.functional("bg",n=>{if(n.value){if(n.value.kind==="arbitrary"){let a=n.value.value;switch(n.value.dataType??I(a,["image","color","percentage","position","bg-size","length","url"])){case"percentage":case"position":return n.modifier?void 0:[l("background-position",a)];case"bg-size":case"length":case"size":return n.modifier?void 0:[l("background-size",a)];case"image":case"url":return n.modifier?void 0:[l("background-image",a)];default:return a=L(a,n.modifier,t),a===null?void 0:[l("background-color",a)]}}{let a=q(n,t,["--background-color","--color"]);if(a)return[l("background-color",a)]}{if(n.modifier)return;let a=t.resolve(n.value.value,["--background-image"]);if(a)return[l("background-image",a)]}}}),i("bg",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(n,a)=>`${a*5}`)},{values:[],valueThemeKeys:["--background-image"]}]);let w=()=>D([V("--tw-gradient-position"),V("--tw-gradient-from","#0000","<color>"),V("--tw-gradient-via","#0000","<color>"),V("--tw-gradient-to","#0000","<color>"),V("--tw-gradient-stops"),V("--tw-gradient-via-stops"),V("--tw-gradient-from-position","0%","<length-percentage>"),V("--tw-gradient-via-position","50%","<length-percentage>"),V("--tw-gradient-to-position","100%","<length-percentage>")]);function v(n,a){r.functional(n,f=>{if(f.value){if(f.value.kind==="arbitrary"){let k=f.value.value;switch(f.value.dataType??I(k,["color","length","percentage"])){case"length":case"percentage":return f.modifier?void 0:a.position(k);default:return k=L(k,f.modifier,t),k===null?void 0:a.color(k)}}{let k=q(f,t,["--background-color","--color"]);if(k)return a.color(k)}{if(f.modifier)return;let k=t.resolve(f.value.value,["--gradient-color-stop-positions"]);if(k)return a.position(k);if(f.value.value[f.value.value.length-1]==="%"&&S(f.value.value.slice(0,-1)))return a.position(f.value.value)}}}),i(n,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(f,k)=>`${k*5}`)},{values:Array.from({length:21},(f,k)=>`${k*5}%`),valueThemeKeys:["--gradient-color-stop-positions"]}])}v("from",{color:n=>[w(),l("--tw-sort","--tw-gradient-from"),l("--tw-gradient-from",n),l("--tw-gradient-stops","var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")],position:n=>[w(),l("--tw-gradient-from-position",n)]}),e("via-none",[["--tw-gradient-via-stops","initial"]]),v("via",{color:n=>[w(),l("--tw-sort","--tw-gradient-via"),l("--tw-gradient-via",n),l("--tw-gradient-via-stops","var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position)"),l("--tw-gradient-stops","var(--tw-gradient-via-stops)")],position:n=>[w(),l("--tw-gradient-via-position",n)]}),v("to",{color:n=>[w(),l("--tw-sort","--tw-gradient-to"),l("--tw-gradient-to",n),l("--tw-gradient-stops","var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")],position:n=>[w(),l("--tw-gradient-to-position",n)]}),e("box-decoration-slice",[["-webkit-box-decoration-break","slice"],["box-decoration-break","slice"]]),e("box-decoration-clone",[["-webkit-box-decoration-break","clone"],["box-decoration-break","clone"]]),e("bg-clip-text",[["background-clip","text"]]),e("bg-clip-border",[["background-clip","border-box"]]),e("bg-clip-padding",[["background-clip","padding-box"]]),e("bg-clip-content",[["background-clip","content-box"]]),e("bg-origin-border",[["background-origin","border-box"]]),e("bg-origin-padding",[["background-origin","padding-box"]]),e("bg-origin-content",[["background-origin","content-box"]]);for(let n of["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"])e(`bg-blend-${n}`,[["background-blend-mode",n]]),e(`mix-blend-${n}`,[["mix-blend-mode",n]]);e("mix-blend-plus-darker",[["mix-blend-mode","plus-darker"]]),e("mix-blend-plus-lighter",[["mix-blend-mode","plus-lighter"]]),e("fill-none",[["fill","none"]]),r.functional("fill",n=>{if(!n.value)return;if(n.value.kind==="arbitrary"){let f=L(n.value.value,n.modifier,t);return f===null?void 0:[l("fill",f)]}let a=q(n,t,["--fill","--color"]);if(a)return[l("fill",a)]}),i("fill",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--fill","--color"],modifiers:Array.from({length:21},(n,a)=>`${a*5}`)}]),e("stroke-none",[["stroke","none"]]),r.functional("stroke",n=>{if(n.value){if(n.value.kind==="arbitrary"){let a=n.value.value;switch(n.value.dataType??I(a,["color","number","length","percentage"])){case"number":case"length":case"percentage":return n.modifier?void 0:[l("stroke-width",a)];default:return a=L(n.value.value,n.modifier,t),a===null?void 0:[l("stroke",a)]}}{let a=q(n,t,["--stroke","--color"]);if(a)return[l("stroke",a)]}{let a=t.resolve(n.value.value,["--stroke-width"]);if(a)return[l("stroke-width",a)];if(S(n.value.value))return[l("stroke-width",n.value.value)]}}}),i("stroke",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--stroke","--color"],modifiers:Array.from({length:21},(n,a)=>`${a*5}`)},{values:["0","1","2","3"],valueThemeKeys:["--stroke-width"]}]),e("object-contain",[["object-fit","contain"]]),e("object-cover",[["object-fit","cover"]]),e("object-fill",[["object-fit","fill"]]),e("object-none",[["object-fit","none"]]),e("object-scale-down",[["object-fit","scale-down"]]),e("object-bottom",[["object-position","bottom"]]),e("object-center",[["object-position","center"]]),e("object-left",[["object-position","left"]]),e("object-left-bottom",[["object-position","left bottom"]]),e("object-left-top",[["object-position","left top"]]),e("object-right",[["object-position","right"]]),e("object-right-bottom",[["object-position","right bottom"]]),e("object-right-top",[["object-position","right top"]]),e("object-top",[["object-position","top"]]),o("object",{themeKeys:["--object-position"],handle:n=>[l("object-position",n)]});for(let[n,a]of[["p","padding"],["px","padding-inline"],["py","padding-block"],["ps","padding-inline-start"],["pe","padding-inline-end"],["pt","padding-top"],["pr","padding-right"],["pb","padding-bottom"],["pl","padding-left"]])s(n,["--padding","--spacing"],f=>[l(a,f)]);e("text-left",[["text-align","left"]]),e("text-center",[["text-align","center"]]),e("text-right",[["text-align","right"]]),e("text-justify",[["text-align","justify"]]),e("text-start",[["text-align","start"]]),e("text-end",[["text-align","end"]]),s("indent",["--text-indent","--spacing"],n=>[l("text-indent",n)],{supportsNegative:!0}),e("align-baseline",[["vertical-align","baseline"]]),e("align-top",[["vertical-align","top"]]),e("align-middle",[["vertical-align","middle"]]),e("align-bottom",[["vertical-align","bottom"]]),e("align-text-top",[["vertical-align","text-top"]]),e("align-text-bottom",[["vertical-align","text-bottom"]]),e("align-sub",[["vertical-align","sub"]]),e("align-super",[["vertical-align","super"]]),o("align",{themeKeys:[],handle:n=>[l("vertical-align",n)]}),r.functional("font",n=>{if(!(!n.value||n.modifier)){if(n.value.kind==="arbitrary"){let a=n.value.value;switch(n.value.dataType??I(a,["number","generic-name","family-name"])){case"generic-name":case"family-name":return[l("font-family",a)];default:return[D([V("--tw-font-weight")]),l("--tw-font-weight",a),l("font-weight",a)]}}{let a=t.resolveWith(n.value.value,["--font"],["--font-feature-settings","--font-variation-settings"]);if(a){let[f,k={}]=a;return[l("font-family",f),l("font-feature-settings",k["--font-feature-settings"]),l("font-variation-settings",k["--font-variation-settings"])]}}{let a=t.resolve(n.value.value,["--font-weight"]);if(a)return[D([V("--tw-font-weight")]),l("--tw-font-weight",a),l("font-weight",a)]}}}),i("font",()=>[{values:[],valueThemeKeys:["--font"]},{values:[],valueThemeKeys:["--font-weight"]}]),e("uppercase",[["text-transform","uppercase"]]),e("lowercase",[["text-transform","lowercase"]]),e("capitalize",[["text-transform","capitalize"]]),e("normal-case",[["text-transform","none"]]),e("italic",[["font-style","italic"]]),e("not-italic",[["font-style","normal"]]),e("underline",[["text-decoration-line","underline"]]),e("overline",[["text-decoration-line","overline"]]),e("line-through",[["text-decoration-line","line-through"]]),e("no-underline",[["text-decoration-line","none"]]),e("font-stretch-normal",[["font-stretch","normal"]]),e("font-stretch-ultra-condensed",[["font-stretch","ultra-condensed"]]),e("font-stretch-extra-condensed",[["font-stretch","extra-condensed"]]),e("font-stretch-condensed",[["font-stretch","condensed"]]),e("font-stretch-semi-condensed",[["font-stretch","semi-condensed"]]),e("font-stretch-semi-expanded",[["font-stretch","semi-expanded"]]),e("font-stretch-expanded",[["font-stretch","expanded"]]),e("font-stretch-extra-expanded",[["font-stretch","extra-expanded"]]),e("font-stretch-ultra-expanded",[["font-stretch","ultra-expanded"]]),o("font-stretch",{handleBareValue:({value:n})=>{if(!n.endsWith("%"))return null;let a=Number(n.slice(0,-1));return!S(a)||Number.isNaN(a)||a<50||a>200?null:n},handle:n=>[l("font-stretch",n)]}),i("font-stretch",()=>[{values:["50%","75%","90%","95%","100%","105%","110%","125%","150%","200%"]}]),c("placeholder",{themeKeys:["--background-color","--color"],handle:n=>[F("&::placeholder",[l("--tw-sort","placeholder-color"),l("color",n)])]}),e("decoration-solid",[["text-decoration-style","solid"]]),e("decoration-double",[["text-decoration-style","double"]]),e("decoration-dotted",[["text-decoration-style","dotted"]]),e("decoration-dashed",[["text-decoration-style","dashed"]]),e("decoration-wavy",[["text-decoration-style","wavy"]]),e("decoration-auto",[["text-decoration-thickness","auto"]]),e("decoration-from-font",[["text-decoration-thickness","from-font"]]),r.functional("decoration",n=>{if(n.value){if(n.value.kind==="arbitrary"){let a=n.value.value;switch(n.value.dataType??I(a,["color","length","percentage"])){case"length":case"percentage":return n.modifier?void 0:[l("text-decoration-thickness",a)];default:return a=L(a,n.modifier,t),a===null?void 0:[l("text-decoration-color",a)]}}{let a=t.resolve(n.value.value,["--text-decoration-thickness"]);if(a)return n.modifier?void 0:[l("text-decoration-thickness",a)];if(S(n.value.value))return n.modifier?void 0:[l("text-decoration-thickness",`${n.value.value}px`)]}{let a=q(n,t,["--text-decoration-color","--color"]);if(a)return[l("text-decoration-color",a)]}}}),i("decoration",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-decoration-color","--color"],modifiers:Array.from({length:21},(n,a)=>`${a*5}`)},{values:["0","1","2"],valueThemeKeys:["--text-decoration-thickness"]}]),e("animate-none",[["animation","none"]]),o("animate",{themeKeys:["--animate"],handle:n=>[l("animation",n)]});{let n=["var(--tw-blur,)","var(--tw-brightness,)","var(--tw-contrast,)","var(--tw-grayscale,)","var(--tw-hue-rotate,)","var(--tw-invert,)","var(--tw-saturate,)","var(--tw-sepia,)","var(--tw-drop-shadow,)"].join(" "),a=["var(--tw-backdrop-blur,)","var(--tw-backdrop-brightness,)","var(--tw-backdrop-contrast,)","var(--tw-backdrop-grayscale,)","var(--tw-backdrop-hue-rotate,)","var(--tw-backdrop-invert,)","var(--tw-backdrop-opacity,)","var(--tw-backdrop-saturate,)","var(--tw-backdrop-sepia,)"].join(" "),f=()=>D([V("--tw-blur"),V("--tw-brightness"),V("--tw-contrast"),V("--tw-grayscale"),V("--tw-hue-rotate"),V("--tw-invert"),V("--tw-opacity"),V("--tw-saturate"),V("--tw-sepia")]),k=()=>D([V("--tw-backdrop-blur"),V("--tw-backdrop-brightness"),V("--tw-backdrop-contrast"),V("--tw-backdrop-grayscale"),V("--tw-backdrop-hue-rotate"),V("--tw-backdrop-invert"),V("--tw-backdrop-opacity"),V("--tw-backdrop-saturate"),V("--tw-backdrop-sepia")]);r.functional("filter",b=>{if(!b.modifier){if(b.value===null)return[f(),l("filter",n)];if(b.value.kind==="arbitrary")return[l("filter",b.value.value)];switch(b.value.value){case"none":return[l("filter","none")]}}}),r.functional("backdrop-filter",b=>{if(!b.modifier){if(b.value===null)return[k(),l("-webkit-backdrop-filter",a),l("backdrop-filter",a)];if(b.value.kind==="arbitrary")return[l("-webkit-backdrop-filter",b.value.value),l("backdrop-filter",b.value.value)];switch(b.value.value){case"none":return[l("-webkit-backdrop-filter","none"),l("backdrop-filter","none")]}}}),o("blur",{themeKeys:["--blur"],handle:b=>[f(),l("--tw-blur",`blur(${b})`),l("filter",n)]}),e("blur-none",[f,["--tw-blur"," "],["filter",n]]),o("backdrop-blur",{themeKeys:["--backdrop-blur","--blur"],handle:b=>[k(),l("--tw-backdrop-blur",`blur(${b})`),l("-webkit-backdrop-filter",a),l("backdrop-filter",a)]}),e("backdrop-blur-none",[k,["--tw-backdrop-blur"," "],["-webkit-backdrop-filter",a],["backdrop-filter",a]]),o("brightness",{themeKeys:["--brightness"],handleBareValue:({value:b})=>S(b)?`${b}%`:null,handle:b=>[f(),l("--tw-brightness",`brightness(${b})`),l("filter",n)]}),o("backdrop-brightness",{themeKeys:["--backdrop-brightness","--brightness"],handleBareValue:({value:b})=>S(b)?`${b}%`:null,handle:b=>[k(),l("--tw-backdrop-brightness",`brightness(${b})`),l("-webkit-backdrop-filter",a),l("backdrop-filter",a)]}),i("brightness",()=>[{values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--brightness"]}]),i("backdrop-brightness",()=>[{values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--backdrop-brightness","--brightness"]}]),o("contrast",{themeKeys:["--contrast"],handleBareValue:({value:b})=>S(b)?`${b}%`:null,handle:b=>[f(),l("--tw-contrast",`contrast(${b})`),l("filter",n)]}),o("backdrop-contrast",{themeKeys:["--backdrop-contrast","--contrast"],handleBareValue:({value:b})=>S(b)?`${b}%`:null,handle:b=>[k(),l("--tw-backdrop-contrast",`contrast(${b})`),l("-webkit-backdrop-filter",a),l("backdrop-filter",a)]}),i("contrast",()=>[{values:["0","50","75","100","125","150","200"],valueThemeKeys:["--contrast"]}]),i("backdrop-contrast",()=>[{values:["0","50","75","100","125","150","200"],valueThemeKeys:["--backdrop-contrast","--contrast"]}]),o("grayscale",{themeKeys:["--grayscale"],handleBareValue:({value:b})=>S(b)?`${b}%`:null,defaultValue:"100%",handle:b=>[f(),l("--tw-grayscale",`grayscale(${b})`),l("filter",n)]}),o("backdrop-grayscale",{themeKeys:["--backdrop-grayscale","--grayscale"],handleBareValue:({value:b})=>S(b)?`${b}%`:null,defaultValue:"100%",handle:b=>[k(),l("--tw-backdrop-grayscale",`grayscale(${b})`),l("-webkit-backdrop-filter",a),l("backdrop-filter",a)]}),i("grayscale",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--grayscale"],hasDefaultValue:!0}]),i("backdrop-grayscale",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--backdrop-grayscale","--grayscale"],hasDefaultValue:!0}]),o("hue-rotate",{supportsNegative:!0,themeKeys:["--hue-rotate"],handleBareValue:({value:b})=>S(b)?`${b}deg`:null,handle:b=>[f(),l("--tw-hue-rotate",`hue-rotate(${b})`),l("filter",n)]}),o("backdrop-hue-rotate",{supportsNegative:!0,themeKeys:["--backdrop-hue-rotate","--hue-rotate"],handleBareValue:({value:b})=>S(b)?`${b}deg`:null,handle:b=>[k(),l("--tw-backdrop-hue-rotate",`hue-rotate(${b})`),l("-webkit-backdrop-filter",a),l("backdrop-filter",a)]}),i("hue-rotate",()=>[{values:["0","15","30","60","90","180"],valueThemeKeys:["--hue-rotate"]}]),i("backdrop-hue-rotate",()=>[{values:["0","15","30","60","90","180"],valueThemeKeys:["--backdrop-hue-rotate","--hue-rotate"]}]),o("invert",{themeKeys:["--invert"],handleBareValue:({value:b})=>S(b)?`${b}%`:null,defaultValue:"100%",handle:b=>[f(),l("--tw-invert",`invert(${b})`),l("filter",n)]}),o("backdrop-invert",{themeKeys:["--backdrop-invert","--invert"],handleBareValue:({value:b})=>S(b)?`${b}%`:null,defaultValue:"100%",handle:b=>[k(),l("--tw-backdrop-invert",`invert(${b})`),l("-webkit-backdrop-filter",a),l("backdrop-filter",a)]}),i("invert",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--invert"],hasDefaultValue:!0}]),i("backdrop-invert",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--backdrop-invert","--invert"],hasDefaultValue:!0}]),o("saturate",{themeKeys:["--saturate"],handleBareValue:({value:b})=>S(b)?`${b}%`:null,handle:b=>[f(),l("--tw-saturate",`saturate(${b})`),l("filter",n)]}),o("backdrop-saturate",{themeKeys:["--backdrop-saturate","--saturate"],handleBareValue:({value:b})=>S(b)?`${b}%`:null,handle:b=>[k(),l("--tw-backdrop-saturate",`saturate(${b})`),l("-webkit-backdrop-filter",a),l("backdrop-filter",a)]}),i("saturate",()=>[{values:["0","50","100","150","200"],valueThemeKeys:["--saturate"]}]),i("backdrop-saturate",()=>[{values:["0","50","100","150","200"],valueThemeKeys:["--backdrop-saturate","--saturate"]}]),o("sepia",{themeKeys:["--sepia"],handleBareValue:({value:b})=>S(b)?`${b}%`:null,defaultValue:"100%",handle:b=>[f(),l("--tw-sepia",`sepia(${b})`),l("filter",n)]}),o("backdrop-sepia",{themeKeys:["--backdrop-sepia","--sepia"],handleBareValue:({value:b})=>S(b)?`${b}%`:null,defaultValue:"100%",handle:b=>[k(),l("--tw-backdrop-sepia",`sepia(${b})`),l("-webkit-backdrop-filter",a),l("backdrop-filter",a)]}),i("sepia",()=>[{values:["0","50","100"],valueThemeKeys:["--sepia"],hasDefaultValue:!0}]),i("backdrop-sepia",()=>[{values:["0","50","100"],valueThemeKeys:["--backdrop-sepia","--sepia"],hasDefaultValue:!0}]),e("drop-shadow-none",[f,["--tw-drop-shadow"," "],["filter",n]]),o("drop-shadow",{themeKeys:["--drop-shadow"],handle:b=>[f(),l("--tw-drop-shadow",j(b,",").map($=>`drop-shadow(${$})`).join(" ")),l("filter",n)]}),o("backdrop-opacity",{themeKeys:["--backdrop-opacity","--opacity"],handleBareValue:({value:b})=>be(b)?`${b}%`:null,handle:b=>[k(),l("--tw-backdrop-opacity",`opacity(${b})`),l("-webkit-backdrop-filter",a),l("backdrop-filter",a)]}),i("backdrop-opacity",()=>[{values:Array.from({length:21},(b,$)=>`${$*5}`),valueThemeKeys:["--backdrop-opacity","--opacity"]}])}{let n=`var(--tw-ease, ${t.resolve(null,["--default-transition-timing-function"])??"ease"})`,a=`var(--tw-duration, ${t.resolve(null,["--default-transition-duration"])??"0s"})`;e("transition-none",[["transition-property","none"]]),e("transition-all",[["transition-property","all"],["transition-timing-function",n],["transition-duration",a]]),e("transition-colors",[["transition-property","color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to"],["transition-timing-function",n],["transition-duration",a]]),e("transition-opacity",[["transition-property","opacity"],["transition-timing-function",n],["transition-duration",a]]),e("transition-shadow",[["transition-property","box-shadow"],["transition-timing-function",n],["transition-duration",a]]),e("transition-transform",[["transition-property","transform, translate, scale, rotate"],["transition-timing-function",n],["transition-duration",a]]),o("transition",{defaultValue:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter",themeKeys:["--transition-property"],handle:f=>[l("transition-property",f),l("transition-timing-function",n),l("transition-duration",a)]}),e("transition-discrete",[["transition-behavior","allow-discrete"]]),e("transition-normal",[["transition-behavior","normal"]]),o("delay",{handleBareValue:({value:f})=>S(f)?`${f}ms`:null,themeKeys:["--transition-delay"],handle:f=>[l("transition-delay",f)]});{let f=()=>D([V("--tw-duration")]);e("duration-initial",[f,["--tw-duration","initial"]]),r.functional("duration",k=>{if(k.modifier||!k.value)return;let b=null;if(k.value.kind==="arbitrary"?b=k.value.value:(b=t.resolve(k.value.fraction??k.value.value,["--transition-duration"]),b===null&&S(k.value.value)&&(b=`${k.value.value}ms`)),b!==null)return[f(),l("--tw-duration",b),l("transition-duration",b)]})}i("delay",()=>[{values:["75","100","150","200","300","500","700","1000"],valueThemeKeys:["--transition-delay"]}]),i("duration",()=>[{values:["75","100","150","200","300","500","700","1000"],valueThemeKeys:["--transition-duration"]}])}{let n=()=>D([V("--tw-ease")]);e("ease-initial",[n,["--tw-ease","initial"]]),e("ease-linear",[n,["--tw-ease","linear"],["transition-timing-function","linear"]]),o("ease",{themeKeys:["--ease"],handle:a=>[n(),l("--tw-ease",a),l("transition-timing-function",a)]})}e("will-change-auto",[["will-change","auto"]]),e("will-change-scroll",[["will-change","scroll-position"]]),e("will-change-contents",[["will-change","contents"]]),e("will-change-transform",[["will-change","transform"]]),o("will-change",{themeKeys:[],handle:n=>[l("will-change",n)]}),e("content-none",[["--tw-content","none"],["content","none"]]),o("content",{themeKeys:[],handle:n=>[D([V("--tw-content",'""')]),l("--tw-content",n),l("content","var(--tw-content)")]});{let n="var(--tw-contain-size,) var(--tw-contain-layout,) var(--tw-contain-paint,) var(--tw-contain-style,)",a=()=>D([V("--tw-contain-size"),V("--tw-contain-layout"),V("--tw-contain-paint"),V("--tw-contain-style")]);e("contain-none",[["contain","none"]]),e("contain-content",[["contain","content"]]),e("contain-strict",[["contain","strict"]]),e("contain-size",[a,["--tw-contain-size","size"],["contain",n]]),e("contain-inline-size",[a,["--tw-contain-size","inline-size"],["contain",n]]),e("contain-layout",[a,["--tw-contain-layout","layout"],["contain",n]]),e("contain-paint",[a,["--tw-contain-paint","paint"],["contain",n]]),e("contain-style",[a,["--tw-contain-style","style"],["contain",n]]),o("contain",{themeKeys:[],handle:f=>[l("contain",f)]})}e("forced-color-adjust-none",[["forced-color-adjust","none"]]),e("forced-color-adjust-auto",[["forced-color-adjust","auto"]]),e("leading-none",[()=>D([V("--tw-leading")]),["--tw-leading","1"],["line-height","1"]]),s("leading",["--leading","--spacing"],n=>[D([V("--tw-leading")]),l("--tw-leading",n),l("line-height",n)]),o("tracking",{supportsNegative:!0,themeKeys:["--tracking"],handle:n=>[D([V("--tw-tracking")]),l("--tw-tracking",n),l("letter-spacing",n)]}),e("antialiased",[["-webkit-font-smoothing","antialiased"],["-moz-osx-font-smoothing","grayscale"]]),e("subpixel-antialiased",[["-webkit-font-smoothing","auto"],["-moz-osx-font-smoothing","auto"]]);{let n="var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,)",a=()=>D([V("--tw-ordinal"),V("--tw-slashed-zero"),V("--tw-numeric-figure"),V("--tw-numeric-spacing"),V("--tw-numeric-fraction")]);e("normal-nums",[["font-variant-numeric","normal"]]),e("ordinal",[a,["--tw-ordinal","ordinal"],["font-variant-numeric",n]]),e("slashed-zero",[a,["--tw-slashed-zero","slashed-zero"],["font-variant-numeric",n]]),e("lining-nums",[a,["--tw-numeric-figure","lining-nums"],["font-variant-numeric",n]]),e("oldstyle-nums",[a,["--tw-numeric-figure","oldstyle-nums"],["font-variant-numeric",n]]),e("proportional-nums",[a,["--tw-numeric-spacing","proportional-nums"],["font-variant-numeric",n]]),e("tabular-nums",[a,["--tw-numeric-spacing","tabular-nums"],["font-variant-numeric",n]]),e("diagonal-fractions",[a,["--tw-numeric-fraction","diagonal-fractions"],["font-variant-numeric",n]]),e("stacked-fractions",[a,["--tw-numeric-fraction","stacked-fractions"],["font-variant-numeric",n]])}{let n=()=>D([V("--tw-outline-style","solid")]);r.static("outline-hidden",()=>[l("outline-style","none"),O("@media","(forced-colors: active)",[l("outline","2px solid transparent"),l("outline-offset","2px")])]),e("outline-none",[["--tw-outline-style","none"],["outline-style","none"]]),e("outline-solid",[["--tw-outline-style","solid"],["outline-style","solid"]]),e("outline-dashed",[["--tw-outline-style","dashed"],["outline-style","dashed"]]),e("outline-dotted",[["--tw-outline-style","dotted"],["outline-style","dotted"]]),e("outline-double",[["--tw-outline-style","double"],["outline-style","double"]]),r.functional("outline",a=>{if(a.value===null)return a.modifier?void 0:[n(),l("outline-style","var(--tw-outline-style)"),l("outline-width","1px")];if(a.value.kind==="arbitrary"){let f=a.value.value;switch(a.value.dataType??I(f,["color","length","number","percentage"])){case"length":case"number":case"percentage":return a.modifier?void 0:[n(),l("outline-style","var(--tw-outline-style)"),l("outline-width",f)];default:return f=L(f,a.modifier,t),f===null?void 0:[l("outline-color",f)]}}{let f=q(a,t,["--outline-color","--color"]);if(f)return[l("outline-color",f)]}{if(a.modifier)return;let f=t.resolve(a.value.value,["--outline-width"]);if(f)return[n(),l("outline-style","var(--tw-outline-style)"),l("outline-width",f)];if(S(a.value.value))return[n(),l("outline-style","var(--tw-outline-style)"),l("outline-width",`${a.value.value}px`)]}}),i("outline",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--outline-color","--color"],modifiers:Array.from({length:21},(a,f)=>`${f*5}`),hasDefaultValue:!0},{values:["0","1","2","4","8"],valueThemeKeys:["--outline-width"]}]),o("outline-offset",{supportsNegative:!0,themeKeys:["--outline-offset"],handleBareValue:({value:a})=>S(a)?`${a}px`:null,handle:a=>[l("outline-offset",a)]}),i("outline-offset",()=>[{supportsNegative:!0,values:["0","1","2","4","8"],valueThemeKeys:["--outline-offset"]}])}o("opacity",{themeKeys:["--opacity"],handleBareValue:({value:n})=>be(n)?`${n}%`:null,handle:n=>[l("opacity",n)]}),i("opacity",()=>[{values:Array.from({length:21},(n,a)=>`${a*5}`),valueThemeKeys:["--opacity"]}]),e("underline-offset-auto",[["text-underline-offset","auto"]]),o("underline-offset",{supportsNegative:!0,themeKeys:["--text-underline-offset"],handleBareValue:({value:n})=>S(n)?`${n}px`:null,handle:n=>[l("text-underline-offset",n)]}),i("underline-offset",()=>[{supportsNegative:!0,values:["0","1","2","4","8"],valueThemeKeys:["--text-underline-offset"]}]),r.functional("text",n=>{if(n.value){if(n.value.kind==="arbitrary"){let a=n.value.value;switch(n.value.dataType??I(a,["color","length","percentage","absolute-size","relative-size"])){case"size":case"length":case"percentage":case"absolute-size":case"relative-size":{if(n.modifier){let k=n.modifier.kind==="arbitrary"?n.modifier.value:t.resolve(n.modifier.value,["--leading"]);if(!k&&oe(n.modifier.value)){let b=t.resolve(null,["--spacing"]);if(!b)return null;k=`calc(${b} * ${n.modifier.value})`}return!k&&n.modifier.value==="none"&&(k="1"),k?[l("font-size",a),l("line-height",k)]:null}return[l("font-size",a)]}default:return a=L(a,n.modifier,t),a===null?void 0:[l("color",a)]}}{let a=q(n,t,["--text-color","--color"]);if(a)return[l("color",a)]}{let a=t.resolveWith(n.value.value,["--text"],["--line-height","--letter-spacing","--font-weight"]);if(a){let[f,k={}]=Array.isArray(a)?a:[a];if(n.modifier){let b=n.modifier.kind==="arbitrary"?n.modifier.value:t.resolve(n.modifier.value,["--leading"]);if(!b&&oe(n.modifier.value)){let K=t.resolve(null,["--spacing"]);if(!K)return null;b=`calc(${K} * ${n.modifier.value})`}if(!b&&n.modifier.value==="none"&&(b="1"),!b)return null;let $=[l("font-size",f)];return b&&$.push(l("line-height",b)),$}return typeof k=="string"?[l("font-size",f),l("line-height",k)]:[l("font-size",f),l("line-height",k["--line-height"]?`var(--tw-leading, ${k["--line-height"]})`:void 0),l("letter-spacing",k["--letter-spacing"]?`var(--tw-tracking, ${k["--letter-spacing"]})`:void 0),l("font-weight",k["--font-weight"]?`var(--tw-font-weight, ${k["--font-weight"]})`:void 0)]}}}}),i("text",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-color","--color"],modifiers:Array.from({length:21},(n,a)=>`${a*5}`)},{values:[],valueThemeKeys:["--text"],modifiers:[],modifierThemeKeys:["--leading"]}]);{let b=function(C){return`var(--tw-ring-inset,) 0 0 0 calc(${C} + var(--tw-ring-offset-width)) var(--tw-ring-color, ${k})`},$=function(C){return`inset 0 0 0 ${C} var(--tw-inset-ring-color, currentColor)`};var E=b,P=$;let n=["var(--tw-inset-shadow)","var(--tw-inset-ring-shadow)","var(--tw-ring-offset-shadow)","var(--tw-ring-shadow)","var(--tw-shadow)"].join(", "),a="0 0 #0000",f=()=>D([V("--tw-shadow",a),V("--tw-shadow-color"),V("--tw-inset-shadow",a),V("--tw-inset-shadow-color"),V("--tw-ring-color"),V("--tw-ring-shadow",a),V("--tw-inset-ring-color"),V("--tw-inset-ring-shadow",a),V("--tw-ring-inset"),V("--tw-ring-offset-width","0px","<length>"),V("--tw-ring-offset-color","#fff"),V("--tw-ring-offset-shadow",a)]);e("shadow-initial",[f,["--tw-shadow-color","initial"]]),r.functional("shadow",C=>{if(!C.value){let A=t.get(["--shadow"]);return A===null?void 0:[f(),l("--tw-shadow",ne(A,U=>`var(--tw-shadow-color, ${U})`)),l("box-shadow",n)]}if(C.value.kind==="arbitrary"){let A=C.value.value;switch(C.value.dataType??I(A,["color"])){case"color":return A=L(A,C.modifier,t),A===null?void 0:[f(),l("--tw-shadow-color",A)];default:return[f(),l("--tw-shadow",ne(A,ve=>`var(--tw-shadow-color, ${ve})`)),l("box-shadow",n)]}}switch(C.value.value){case"none":return C.modifier?void 0:[f(),l("--tw-shadow",a),l("box-shadow",n)]}{let A=t.get([`--shadow-${C.value.value}`]);if(A)return C.modifier?void 0:[f(),l("--tw-shadow",ne(A,U=>`var(--tw-shadow-color, ${U})`)),l("box-shadow",n)]}{let A=q(C,t,["--box-shadow-color","--color"]);if(A)return[f(),l("--tw-shadow-color",A)]}}),i("shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--box-shadow-color","--color"],modifiers:Array.from({length:21},(C,A)=>`${A*5}`)},{values:["none"],valueThemeKeys:["--shadow"],hasDefaultValue:!0}]),e("inset-shadow-initial",[f,["--tw-inset-shadow-color","initial"]]),r.functional("inset-shadow",C=>{if(!C.value){let A=t.get(["--inset-shadow"]);return A===null?void 0:[f(),l("--tw-inset-shadow",ne(A,U=>`var(--tw-inset-shadow-color, ${U})`)),l("box-shadow",n)]}if(C.value.kind==="arbitrary"){let A=C.value.value;switch(C.value.dataType??I(A,["color"])){case"color":return A=L(A,C.modifier,t),A===null?void 0:[f(),l("--tw-inset-shadow-color",A)];default:return[f(),l("--tw-inset-shadow",`inset ${ne(A,ve=>`var(--tw-inset-shadow-color, ${ve})`)}`),l("box-shadow",n)]}}switch(C.value.value){case"none":return C.modifier?void 0:[f(),l("--tw-inset-shadow",a),l("box-shadow",n)]}{let A=t.get([`--inset-shadow-${C.value.value}`]);if(A)return C.modifier?void 0:[f(),l("--tw-inset-shadow",ne(A,U=>`var(--tw-inset-shadow-color, ${U})`)),l("box-shadow",n)]}{let A=q(C,t,["--box-shadow-color","--color"]);if(A)return[f(),l("--tw-inset-shadow-color",A)]}}),i("inset-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--box-shadow-color","--color"],modifiers:Array.from({length:21},(C,A)=>`${A*5}`)},{values:[],valueThemeKeys:["--inset-shadow"],hasDefaultValue:!0}]),e("ring-inset",[f,["--tw-ring-inset","inset"]]);let k=t.get(["--default-ring-color"])??"currentColor";r.functional("ring",C=>{if(!C.value){if(C.modifier)return;let A=t.get(["--default-ring-width"])??"1px";return[f(),l("--tw-ring-shadow",b(A)),l("box-shadow",n)]}if(C.value.kind==="arbitrary"){let A=C.value.value;switch(C.value.dataType??I(A,["color","length"])){case"length":return C.modifier?void 0:[f(),l("--tw-ring-shadow",b(A)),l("box-shadow",n)];default:return A=L(A,C.modifier,t),A===null?void 0:[l("--tw-ring-color",A)]}}{let A=q(C,t,["--ring-color","--color"]);if(A)return[l("--tw-ring-color",A)]}{if(C.modifier)return;let A=t.resolve(C.value.value,["--ring-width"]);if(A===null&&S(C.value.value)&&(A=`${C.value.value}px`),A)return[f(),l("--tw-ring-shadow",b(A)),l("box-shadow",n)]}}),i("ring",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-color","--color"],modifiers:Array.from({length:21},(C,A)=>`${A*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-width"],hasDefaultValue:!0}]),r.functional("inset-ring",C=>{if(!C.value)return C.modifier?void 0:[f(),l("--tw-inset-ring-shadow",$("1px")),l("box-shadow",n)];if(C.value.kind==="arbitrary"){let A=C.value.value;switch(C.value.dataType??I(A,["color","length"])){case"length":return C.modifier?void 0:[f(),l("--tw-inset-ring-shadow",$(A)),l("box-shadow",n)];default:return A=L(A,C.modifier,t),A===null?void 0:[l("--tw-inset-ring-color",A)]}}{let A=q(C,t,["--ring-color","--color"]);if(A)return[l("--tw-inset-ring-color",A)]}{if(C.modifier)return;let A=t.resolve(C.value.value,["--ring-width"]);if(A===null&&S(C.value.value)&&(A=`${C.value.value}px`),A)return[f(),l("--tw-inset-ring-shadow",$(A)),l("box-shadow",n)]}}),i("inset-ring",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-color","--color"],modifiers:Array.from({length:21},(C,A)=>`${A*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-width"],hasDefaultValue:!0}]);let K="var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color)";r.functional("ring-offset",C=>{if(C.value){if(C.value.kind==="arbitrary"){let A=C.value.value;switch(C.value.dataType??I(A,["color","length"])){case"length":return C.modifier?void 0:[l("--tw-ring-offset-width",A),l("--tw-ring-offset-shadow",K)];default:return A=L(A,C.modifier,t),A===null?void 0:[l("--tw-ring-offset-color",A)]}}{let A=t.resolve(C.value.value,["--ring-offset-width"]);if(A)return C.modifier?void 0:[l("--tw-ring-offset-width",A),l("--tw-ring-offset-shadow",K)];if(S(C.value.value))return C.modifier?void 0:[l("--tw-ring-offset-width",`${C.value.value}px`),l("--tw-ring-offset-shadow",K)]}{let A=q(C,t,["--ring-offset-color","--color"]);if(A)return[l("--tw-ring-offset-color",A)]}}})}return i("ring-offset",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-offset-color","--color"],modifiers:Array.from({length:21},(n,a)=>`${a*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-offset-width"]}]),r.functional("@container",n=>{let a=null;if(n.value===null?a="inline-size":n.value.kind==="arbitrary"?a=n.value.value:n.value.kind==="named"&&n.value.value==="normal"&&(a="normal"),a!==null)return n.modifier?[l("container-type",a),l("container-name",n.modifier.value)]:[l("container-type",a)]}),i("@container",()=>[{values:["normal"],valueThemeKeys:[],hasDefaultValue:!0}]),r}function gt(t){let r=t.params;return mr.test(r)?i=>{let e=new Set,o=new Set;_(t.nodes,c=>{if(c.kind!=="declaration"||!c.value||!c.value.includes("--value(")&&!c.value.includes("--modifier("))return;let s=z(c.value);Y(s,m=>{if(m.kind!=="function"||m.value!=="--value"&&m.value!=="--modifier")return;let p=j(B(m.nodes),",");for(let[u,d]of p.entries())d=d.replace(/\\\*/g,"*"),d=d.replace(/--(.*?)\s--(.*?)/g,"--$1-*--$2"),d=d.replace(/\s+/g,""),d=d.replace(/(-\*){2,}/g,"-*"),d[0]==="-"&&d[1]==="-"&&!d.includes("-*")&&(d+="-*"),p[u]=d;m.nodes=z(p.join(","));for(let u of m.nodes)if(u.kind==="word"&&u.value[0]==="-"&&u.value[1]==="-"){let d=u.value.replace(/-\*.*$/g,"");m.value==="--value"?e.add(d):m.value==="--modifier"&&o.add(d)}}),c.value=B(s)}),i.utilities.functional(r.slice(0,-2),c=>{let s=structuredClone(t),m=c.value,p=c.modifier;if(m===null)return;let u=!1,d=!1,h=!1,g=!1,y=new Map,w=!1;if(_([s],(v,{parent:x,replaceWith:T})=>{if(x?.kind!=="rule"&&x?.kind!=="at-rule"||v.kind!=="declaration"||!v.value)return;let N=z(v.value);(Y(N,(E,{replaceWith:P})=>{if(E.kind==="function"){if(E.value==="--value"){u=!0;let n=dt(m,E,i);return n?(d=!0,n.ratio?w=!0:y.set(v,x),P(n.nodes),1):(u||=!1,T([]),2)}else if(E.value==="--modifier"){if(p===null)return T([]),1;h=!0;let n=dt(p,E,i);return n?(g=!0,P(n.nodes),1):(h||=!1,T([]),2)}}})??0)===0&&(v.value=B(N))}),u&&!d||h&&!g||w&&g||p&&!w&&!g)return null;if(w)for(let[v,x]of y){let T=x.nodes.indexOf(v);T!==-1&&x.nodes.splice(T,1)}return s.nodes}),i.utilities.suggest(r.slice(0,-2),()=>[{values:i.theme.keysInNamespaces(e).map(c=>c.replaceAll("_",".")),modifiers:i.theme.keysInNamespaces(o).map(c=>c.replaceAll("_","."))}])}:gr.test(r)?i=>{i.utilities.static(r,()=>structuredClone(t.nodes))}:null}function dt(t,r,i){for(let e of r.nodes)if(t.kind==="named"&&e.kind==="word"&&e.value[0]==="-"&&e.value[1]==="-"){let o=e.value;if(o.endsWith("-*")){o=o.slice(0,-2);let c=i.theme.resolve(t.value,[o]);if(c)return{nodes:z(c)}}else{let c=o.split("-*");if(c.length<=1)continue;let s=[c.shift()],m=i.theme.resolveWith(t.value,s,c);if(m){let[,p={}]=m;{let u=p[c.pop()];if(u)return{nodes:z(u)}}}}}else if(t.kind==="named"&&e.kind==="word"){if(e.value!=="number"&&e.value!=="integer"&&e.value!=="ratio"&&e.value!=="percentage")continue;let o=e.value==="ratio"&&"fraction"in t?t.fraction:t.value;if(!o)continue;let c=I(o,[e.value]);if(c===null)continue;if(c==="ratio"){let[s,m]=j(o,"/");if(!S(s)||!S(m))continue}else{if(c==="number"&&!oe(o))continue;if(c==="percentage"&&!S(o.slice(0,-1)))continue}return{nodes:z(o),ratio:c==="ratio"}}else if(t.kind==="arbitrary"&&e.kind==="word"&&e.value[0]==="["&&e.value[e.value.length-1]==="]"){let o=e.value.slice(1,-1);if(o==="*")return{nodes:z(t.value)};if("dataType"in t&&t.dataType&&t.dataType!==o)continue;if("dataType"in t&&t.dataType)return{nodes:z(t.value)};if(I(t.value,[o])!==null)return{nodes:z(t.value)}}}var Pe={"--alpha":hr,"--spacing":vr,"--theme":yr,theme:vt};function hr(t,r,...i){let[e,o]=j(r,"/").map(c=>c.trim());if(!e||!o)throw new Error(`The --alpha(\u2026) function requires a color and an alpha value, e.g.: \`--alpha(${e||"var(--my-color)"} / ${o||"50%"})\``);if(i.length>0)throw new Error(`The --alpha(\u2026) function only accepts one argument, e.g.: \`--alpha(${e||"var(--my-color)"} / ${o||"50%"})\``);return G(e,o)}function vr(t,r,...i){if(!r)throw new Error("The --spacing(\u2026) function requires an argument, but received none.");if(i.length>0)throw new Error(`The --spacing(\u2026) function only accepts a single argument, but received ${i.length+1}.`);let e=t.theme.resolve(null,["--spacing"]);if(!e)throw new Error("The --spacing(\u2026) function requires that the `--spacing` theme variable exists, but it was not found.");return`calc(${e} * ${r})`}function yr(t,r,...i){if(!r.startsWith("--"))throw new Error("The --theme(\u2026) function can only be used with CSS variables from your theme.");return vt(t,r,...i)}function vt(t,r,...i){r=wr(r);let e=t.resolveThemeValue(r);if(!e&&i.length>0)return i.join(", ");if(!e)throw new Error(`Could not resolve value for theme function: \`theme(${r})\`. Consider checking if the path is correct or provide a fallback value to silence this error.`);return e}var mt=new RegExp(Object.keys(Pe).map(t=>`${t}\\(`).join("|"));function ae(t,r){let i=0;return _(t,e=>{if(e.kind==="declaration"&&e.value&&mt.test(e.value)){i|=8,e.value=ht(e.value,r);return}e.kind==="at-rule"&&(e.name==="@media"||e.name==="@custom-media"||e.name==="@container"||e.name==="@supports")&&mt.test(e.params)&&(i|=8,e.params=ht(e.params,r))}),i}function ht(t,r){let i=z(t);return Y(i,(e,{replaceWith:o})=>{if(e.kind==="function"&&e.value in Pe){let c=j(B(e.nodes).trim(),",").map(m=>m.trim()),s=Pe[e.value](r,...c);return o(z(s))}}),B(i)}function wr(t){if(t[0]!=="'"&&t[0]!=='"')return t;let r="",i=t[0];for(let e=1;e<t.length-1;e++){let o=t[e],c=t[e+1];o==="\\"&&(c===i||c==="\\")?(r+=c,e++):r+=o}return r}function ke(t,r){let i=t.length,e=r.length,o=i<e?i:e;for(let c=0;c<o;c++){let s=t.charCodeAt(c),m=r.charCodeAt(c);if(s!==m){if(s>=48&&s<=57&&m>=48&&m<=57){let p=c,u=c+1,d=c,h=c+1;for(s=t.charCodeAt(u);s>=48&&s<=57;)s=t.charCodeAt(++u);for(m=r.charCodeAt(h);m>=48&&m<=57;)m=r.charCodeAt(++h);let g=t.slice(p,u),y=r.slice(d,h);return Number(g)-Number(y)||(g<y?-1:1)}return s-m}}return t.length-r.length}function yt(t){let r=[];for(let i of t.utilities.keys("static"))r.push([i,{modifiers:[]}]);for(let i of t.utilities.keys("functional")){let e=t.utilities.getCompletions(i);for(let o of e)for(let c of o.values){let s=c===null?i:`${i}-${c}`;r.push([s,{modifiers:o.modifiers}]),o.supportsNegative&&r.push([`-${s}`,{modifiers:o.modifiers}])}}return r.sort((i,e)=>ke(i[0],e[0])),r}function wt(t){let r=[];for(let[e,o]of t.variants.entries()){let m=function({value:p,modifier:u}={}){let d=e;p&&(d+=c?`-${p}`:p),u&&(d+=`/${u}`);let h=t.parseVariant(d);if(!h)return[];let g=F(".__placeholder__",[]);if(se(g,h,t.variants)===null)return[];let y=[];return We(g.nodes,(w,{path:v})=>{if(w.kind!=="rule"&&w.kind!=="at-rule"||w.nodes.length>0)return;v.sort((N,R)=>{let E=N.kind==="at-rule",P=R.kind==="at-rule";return E&&!P?-1:!E&&P?1:0});let x=v.flatMap(N=>N.kind==="rule"?N.selector==="&"?[]:[N.selector]:N.kind==="at-rule"?[`${N.name} ${N.params}`]:[]),T="";for(let N=x.length-1;N>=0;N--)T=T===""?x[N]:`${x[N]} { ${T} }`;y.push(T)}),y};var i=m;if(o.kind==="arbitrary")continue;let c=e!=="@",s=t.variants.getCompletions(e);switch(o.kind){case"static":{r.push({name:e,values:s,isArbitrary:!1,hasDash:c,selectors:m});break}case"functional":{r.push({name:e,values:s,isArbitrary:!0,hasDash:c,selectors:m});break}case"compound":{r.push({name:e,values:s,isArbitrary:!0,hasDash:c,selectors:m});break}}}return r}function bt(t,r){let{astNodes:i,nodeSorting:e}=Q(Array.from(r),t),o=new Map(r.map(s=>[s,null])),c=0n;for(let s of i){let m=e.get(s)?.candidate;m&&o.set(m,o.get(m)??c++)}return r.map(s=>[s,o.get(s)??null])}var xe=/^@?[a-zA-Z0-9_-]*$/;var je=class{compareFns=new Map;variants=new Map;completions=new Map;groupOrder=null;lastOrder=0;static(r,i,{compounds:e,order:o}={}){this.set(r,{kind:"static",applyFn:i,compoundsWith:0,compounds:e??2,order:o})}fromAst(r,i){let e=[];_(i,o=>{o.kind==="rule"?e.push(o.selector):o.kind==="at-rule"&&o.name!=="@slot"&&e.push(`${o.name} ${o.params}`)}),this.static(r,o=>{let c=structuredClone(i);Oe(c,o.nodes),o.nodes=c},{compounds:ie(e)})}functional(r,i,{compounds:e,order:o}={}){this.set(r,{kind:"functional",applyFn:i,compoundsWith:0,compounds:e??2,order:o})}compound(r,i,e,{compounds:o,order:c}={}){this.set(r,{kind:"compound",applyFn:e,compoundsWith:i,compounds:o??2,order:c})}group(r,i){this.groupOrder=this.nextOrder(),i&&this.compareFns.set(this.groupOrder,i),r(),this.groupOrder=null}has(r){return this.variants.has(r)}get(r){return this.variants.get(r)}kind(r){return this.variants.get(r)?.kind}compoundsWith(r,i){let e=this.variants.get(r),o=typeof i=="string"?this.variants.get(i):i.kind==="arbitrary"?{compounds:ie([i.selector])}:this.variants.get(i.root);return!(!e||!o||e.kind!=="compound"||o.compounds===0||e.compoundsWith===0||!(e.compoundsWith&o.compounds))}suggest(r,i){this.completions.set(r,i)}getCompletions(r){return this.completions.get(r)?.()??[]}compare(r,i){if(r===i)return 0;if(r===null)return-1;if(i===null)return 1;if(r.kind==="arbitrary"&&i.kind==="arbitrary")return r.selector<i.selector?-1:1;if(r.kind==="arbitrary")return 1;if(i.kind==="arbitrary")return-1;let e=this.variants.get(r.root).order,o=this.variants.get(i.root).order,c=e-o;if(c!==0)return c;if(r.kind==="compound"&&i.kind==="compound"){let u=this.compare(r.variant,i.variant);return u!==0?u:r.modifier&&i.modifier?r.modifier.value<i.modifier.value?-1:1:r.modifier?1:i.modifier?-1:0}let s=this.compareFns.get(e);if(s!==void 0)return s(r,i);if(r.root!==i.root)return r.root<i.root?-1:1;let m=r.value,p=i.value;return m===null?-1:p===null||m.kind==="arbitrary"&&p.kind!=="arbitrary"?1:m.kind!=="arbitrary"&&p.kind==="arbitrary"||m.value<p.value?-1:1}keys(){return this.variants.keys()}entries(){return this.variants.entries()}set(r,{kind:i,applyFn:e,compounds:o,compoundsWith:c,order:s}){let m=this.variants.get(r);m?Object.assign(m,{kind:i,applyFn:e,compounds:o}):(s===void 0&&(this.lastOrder=this.nextOrder(),s=this.lastOrder),this.variants.set(r,{kind:i,applyFn:e,order:s,compoundsWith:c,compounds:o}))}nextOrder(){return this.groupOrder??this.lastOrder+1}};function ie(t){let r=0;for(let i of t){if(i[0]==="@"){if(!i.startsWith("@media")&&!i.startsWith("@supports")&&!i.startsWith("@container"))return 0;r|=1;continue}if(i.includes("::"))return 0;r|=2}return r}function xt(t){let r=new je;function i(u,d,{compounds:h}={}){h=h??ie(d),r.static(u,g=>{g.nodes=d.map(y=>W(y,g.nodes))},{compounds:h})}i("*",[":is(& > *)"],{compounds:0}),i("**",[":is(& *)"],{compounds:0});function e(u,d){return d.map(h=>{h=h.trim();let g=j(h," ");return g[0]==="not"?g.slice(1).join(" "):u==="@container"?g[0][0]==="("?`not ${h}`:g[1]==="not"?`${g[0]} ${g.slice(2).join(" ")}`:`${g[0]} not ${g.slice(1).join(" ")}`:`not ${h}`})}let o=["@media","@supports","@container"];function c(u){for(let d of o){if(d!==u.name)continue;let h=j(u.params,",");return h.length>1?null:(h=e(u.name,h),O(u.name,h.join(", ")))}return null}function s(u){return u.includes("::")?null:`&:not(${j(u,",").map(h=>(h.startsWith("&:is(")&&h.endsWith(")")&&(h=h.slice(5,-1)),h=h.replaceAll("&","*"),h)).join(", ")})`}r.compound("not",3,(u,d)=>{if(d.variant.kind==="arbitrary"&&d.variant.relative||d.modifier)return null;let h=!1;if(_([u],(g,{path:y})=>{if(g.kind!=="rule"&&g.kind!=="at-rule")return 0;if(g.nodes.length>0)return 0;let w=[],v=[];for(let T of y)T.kind==="at-rule"?w.push(T):T.kind==="rule"&&v.push(T);if(w.length>1)return 2;if(v.length>1)return 2;let x=[];for(let T of v){let N=s(T.selector);if(!N)return h=!1,2;x.push(F(N,[]))}for(let T of w){let N=c(T);if(!N)return h=!1,2;x.push(N)}return Object.assign(u,F("&",x)),h=!0,1}),u.kind==="rule"&&u.selector==="&"&&u.nodes.length===1&&Object.assign(u,u.nodes[0]),!h)return null}),r.suggest("not",()=>Array.from(r.keys()).filter(u=>r.compoundsWith("not",u))),r.compound("group",2,(u,d)=>{if(d.variant.kind==="arbitrary"&&d.variant.relative)return null;let h=d.modifier?`:where(.${t.prefix?`${t.prefix}\\:`:""}group\\/${d.modifier.value})`:`:where(.${t.prefix?`${t.prefix}\\:`:""}group)`,g=!1;if(_([u],(y,{path:w})=>{if(y.kind!=="rule")return 0;for(let x of w.slice(0,-1))if(x.kind==="rule")return g=!1,2;let v=y.selector.replaceAll("&",h);j(v,",").length>1&&(v=`:is(${v})`),y.selector=`&:is(${v} *)`,g=!0}),!g)return null}),r.suggest("group",()=>Array.from(r.keys()).filter(u=>r.compoundsWith("group",u))),r.compound("peer",2,(u,d)=>{if(d.variant.kind==="arbitrary"&&d.variant.relative)return null;let h=d.modifier?`:where(.${t.prefix?`${t.prefix}\\:`:""}peer\\/${d.modifier.value})`:`:where(.${t.prefix?`${t.prefix}\\:`:""}peer)`,g=!1;if(_([u],(y,{path:w})=>{if(y.kind!=="rule")return 0;for(let x of w.slice(0,-1))if(x.kind==="rule")return g=!1,2;let v=y.selector.replaceAll("&",h);j(v,",").length>1&&(v=`:is(${v})`),y.selector=`&:is(${v} ~ *)`,g=!0}),!g)return null}),r.suggest("peer",()=>Array.from(r.keys()).filter(u=>r.compoundsWith("peer",u))),i("first-letter",["&::first-letter"]),i("first-line",["&::first-line"]),i("marker",["& *::marker","&::marker"]),i("selection",["& *::selection","&::selection"]),i("file",["&::file-selector-button"]),i("placeholder",["&::placeholder"]),i("backdrop",["&::backdrop"]);{let u=function(){return D([O("@property","--tw-content",[l("syntax",'"*"'),l("initial-value",'""'),l("inherits","false")])])};var m=u;r.static("before",d=>{d.nodes=[F("&::before",[u(),l("content","var(--tw-content)"),...d.nodes])]},{compounds:0}),r.static("after",d=>{d.nodes=[F("&::after",[u(),l("content","var(--tw-content)"),...d.nodes])]},{compounds:0})}i("first",["&:first-child"]),i("last",["&:last-child"]),i("only",["&:only-child"]),i("odd",["&:nth-child(odd)"]),i("even",["&:nth-child(even)"]),i("first-of-type",["&:first-of-type"]),i("last-of-type",["&:last-of-type"]),i("only-of-type",["&:only-of-type"]),i("visited",["&:visited"]),i("target",["&:target"]),i("open",["&:is([open], :popover-open, :open)"]),i("default",["&:default"]),i("checked",["&:checked"]),i("indeterminate",["&:indeterminate"]),i("placeholder-shown",["&:placeholder-shown"]),i("autofill",["&:autofill"]),i("optional",["&:optional"]),i("required",["&:required"]),i("valid",["&:valid"]),i("invalid",["&:invalid"]),i("in-range",["&:in-range"]),i("out-of-range",["&:out-of-range"]),i("read-only",["&:read-only"]),i("empty",["&:empty"]),i("focus-within",["&:focus-within"]),r.static("hover",u=>{u.nodes=[F("&:hover",[O("@media","(hover: hover)",u.nodes)])]}),i("focus",["&:focus"]),i("focus-visible",["&:focus-visible"]),i("active",["&:active"]),i("enabled",["&:enabled"]),i("disabled",["&:disabled"]),i("inert",["&:is([inert], [inert] *)"]),r.compound("in",2,(u,d)=>{if(d.modifier)return null;let h=!1;if(_([u],(g,{path:y})=>{if(g.kind!=="rule")return 0;for(let w of y.slice(0,-1))if(w.kind==="rule")return h=!1,2;g.selector=`:where(${g.selector.replaceAll("&","*")}) &`,h=!0}),!h)return null}),r.suggest("in",()=>Array.from(r.keys()).filter(u=>r.compoundsWith("in",u))),r.compound("has",2,(u,d)=>{if(d.modifier)return null;let h=!1;if(_([u],(g,{path:y})=>{if(g.kind!=="rule")return 0;for(let w of y.slice(0,-1))if(w.kind==="rule")return h=!1,2;g.selector=`&:has(${g.selector.replaceAll("&","*")})`,h=!0}),!h)return null}),r.suggest("has",()=>Array.from(r.keys()).filter(u=>r.compoundsWith("has",u))),r.functional("aria",(u,d)=>{if(!d.value||d.modifier)return null;d.value.kind==="arbitrary"?u.nodes=[F(`&[aria-${kt(d.value.value)}]`,u.nodes)]:u.nodes=[F(`&[aria-${d.value.value}="true"]`,u.nodes)]}),r.suggest("aria",()=>["busy","checked","disabled","expanded","hidden","pressed","readonly","required","selected"]),r.functional("data",(u,d)=>{if(!d.value||d.modifier)return null;u.nodes=[F(`&[data-${kt(d.value.value)}]`,u.nodes)]}),r.functional("nth",(u,d)=>{if(!d.value||d.modifier||d.value.kind==="named"&&!S(d.value.value))return null;u.nodes=[F(`&:nth-child(${d.value.value})`,u.nodes)]}),r.functional("nth-last",(u,d)=>{if(!d.value||d.modifier||d.value.kind==="named"&&!S(d.value.value))return null;u.nodes=[F(`&:nth-last-child(${d.value.value})`,u.nodes)]}),r.functional("nth-of-type",(u,d)=>{if(!d.value||d.modifier||d.value.kind==="named"&&!S(d.value.value))return null;u.nodes=[F(`&:nth-of-type(${d.value.value})`,u.nodes)]}),r.functional("nth-last-of-type",(u,d)=>{if(!d.value||d.modifier||d.value.kind==="named"&&!S(d.value.value))return null;u.nodes=[F(`&:nth-last-of-type(${d.value.value})`,u.nodes)]}),r.functional("supports",(u,d)=>{if(!d.value||d.modifier)return null;let h=d.value.value;if(h===null)return null;if(/^[\w-]*\s*\(/.test(h)){let g=h.replace(/\b(and|or|not)\b/g," $1 ");u.nodes=[O("@supports",g,u.nodes)];return}h.includes(":")||(h=`${h}: var(--tw)`),(h[0]!=="("||h[h.length-1]!==")")&&(h=`(${h})`),u.nodes=[O("@supports",h,u.nodes)]},{compounds:1}),i("motion-safe",["@media (prefers-reduced-motion: no-preference)"]),i("motion-reduce",["@media (prefers-reduced-motion: reduce)"]),i("contrast-more",["@media (prefers-contrast: more)"]),i("contrast-less",["@media (prefers-contrast: less)"]);{let u=function(d,h,g,y){if(d===h)return 0;let w=y.get(d);if(w===null)return g==="asc"?-1:1;let v=y.get(h);return v===null?g==="asc"?1:-1:re(w,v,g)};var p=u;{let d=t.namespace("--breakpoint"),h=new M(g=>{switch(g.kind){case"static":return t.resolveValue(g.root,["--breakpoint"])??null;case"functional":{if(!g.value||g.modifier)return null;let y=null;return g.value.kind==="arbitrary"?y=g.value.value:g.value.kind==="named"&&(y=t.resolveValue(g.value.value,["--breakpoint"])),!y||y.includes("var(")?null:y}case"arbitrary":case"compound":return null}});r.group(()=>{r.functional("max",(g,y)=>{if(y.modifier)return null;let w=h.get(y);if(w===null)return null;g.nodes=[O("@media",`(width < ${w})`,g.nodes)]},{compounds:1})},(g,y)=>u(g,y,"desc",h)),r.suggest("max",()=>Array.from(d.keys()).filter(g=>g!==null)),r.group(()=>{for(let[g,y]of t.namespace("--breakpoint"))g!==null&&r.static(g,w=>{w.nodes=[O("@media",`(width >= ${y})`,w.nodes)]},{compounds:1});r.functional("min",(g,y)=>{if(y.modifier)return null;let w=h.get(y);if(w===null)return null;g.nodes=[O("@media",`(width >= ${w})`,g.nodes)]},{compounds:1})},(g,y)=>u(g,y,"asc",h)),r.suggest("min",()=>Array.from(d.keys()).filter(g=>g!==null))}{let d=t.namespace("--container"),h=new M(g=>{switch(g.kind){case"functional":{if(g.value===null)return null;let y=null;return g.value.kind==="arbitrary"?y=g.value.value:g.value.kind==="named"&&(y=t.resolveValue(g.value.value,["--container"])),!y||y.includes("var(")?null:y}case"static":case"arbitrary":case"compound":return null}});r.group(()=>{r.functional("@max",(g,y)=>{let w=h.get(y);if(w===null)return null;g.nodes=[O("@container",y.modifier?`${y.modifier.value} (width < ${w})`:`(width < ${w})`,g.nodes)]},{compounds:1})},(g,y)=>u(g,y,"desc",h)),r.suggest("@max",()=>Array.from(d.keys()).filter(g=>g!==null)),r.group(()=>{r.functional("@",(g,y)=>{let w=h.get(y);if(w===null)return null;g.nodes=[O("@container",y.modifier?`${y.modifier.value} (width >= ${w})`:`(width >= ${w})`,g.nodes)]},{compounds:1}),r.functional("@min",(g,y)=>{let w=h.get(y);if(w===null)return null;g.nodes=[O("@container",y.modifier?`${y.modifier.value} (width >= ${w})`:`(width >= ${w})`,g.nodes)]},{compounds:1})},(g,y)=>u(g,y,"asc",h)),r.suggest("@min",()=>Array.from(d.keys()).filter(g=>g!==null)),r.suggest("@",()=>Array.from(d.keys()).filter(g=>g!==null))}}return i("portrait",["@media (orientation: portrait)"]),i("landscape",["@media (orientation: landscape)"]),i("ltr",['&:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *)']),i("rtl",['&:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *)']),i("dark",["@media (prefers-color-scheme: dark)"]),i("starting",["@starting-style"]),i("print",["@media print"]),i("forced-colors",["@media (forced-colors: active)"]),r}function kt(t){if(t.includes("=")){let[r,...i]=j(t,"="),e=i.join("=").trim();if(e[0]==="'"||e[0]==='"')return t;if(e.length>1){let o=e[e.length-1];if(e[e.length-2]===" "&&(o==="i"||o==="I"||o==="s"||o==="S"))return`${r}="${e.slice(0,-2)}" ${o}`}return`${r}="${e}"`}return t}function Oe(t,r){_(t,(i,{replaceWith:e})=>{if(i.kind==="at-rule"&&i.name==="@slot")e(r);else if(i.kind==="at-rule"&&(i.name==="@keyframes"||i.name==="@property"))return Object.assign(i,D([O(i.name,i.params,i.nodes)])),1})}function At(t){let r=pt(t),i=xt(t),e=new M(p=>ut(p,m)),o=new M(p=>Array.from(st(p,m))),c=new M(p=>{let u=Ct(p,m);try{ae(u.map(({node:d})=>d),m)}catch{return[]}return u}),s=new M(p=>(Y(z(p),u=>{if(!(u.kind!=="function"||u.value!=="var"))return Y(u.nodes,d=>{d.kind!=="word"||d.value[0]!=="-"||d.value[1]!=="-"||t.markUsedVariable(d.value)}),1}),!0)),m={theme:t,utilities:r,variants:i,invalidCandidates:new Set,important:!1,candidatesToCss(p){let u=[];for(let d of p){let h=!1,{astNodes:g}=Q([d],this,{onInvalidCandidate(){h=!0}});g=te(g,m),g.length===0||h?u.push(null):u.push(J(g))}return u},getClassOrder(p){return bt(this,p)},getClassList(){return yt(this)},getVariants(){return wt(this)},parseCandidate(p){return o.get(p)},parseVariant(p){return e.get(p)},compileAstNodes(p){return c.get(p)},getVariantOrder(){let p=Array.from(e.values());p.sort((g,y)=>this.variants.compare(g,y));let u=new Map,d,h=0;for(let g of p)g!==null&&(d!==void 0&&this.variants.compare(d,g)!==0&&h++,u.set(g,h),d=g);return u},resolveThemeValue(p){let u=p.lastIndexOf("/"),d=null;u!==-1&&(d=p.slice(u+1).trim(),p=p.slice(0,u).trim());let h=t.get([p])??void 0;return d&&h?G(h,d):h},trackUsedVariables(p){s.get(p)}};return m}var De=["container-type","pointer-events","visibility","position","inset","inset-inline","inset-block","inset-inline-start","inset-inline-end","top","right","bottom","left","isolation","z-index","order","grid-column","grid-column-start","grid-column-end","grid-row","grid-row-start","grid-row-end","float","clear","--tw-container-component","margin","margin-inline","margin-block","margin-inline-start","margin-inline-end","margin-top","margin-right","margin-bottom","margin-left","box-sizing","display","field-sizing","aspect-ratio","height","max-height","min-height","width","max-width","min-width","flex","flex-shrink","flex-grow","flex-basis","table-layout","caption-side","border-collapse","border-spacing","transform-origin","translate","--tw-translate-x","--tw-translate-y","scale","--tw-scale-x","--tw-scale-y","--tw-scale-z","rotate","--tw-rotate-x","--tw-rotate-y","--tw-rotate-z","--tw-skew-x","--tw-skew-y","transform","animation","cursor","touch-action","--tw-pan-x","--tw-pan-y","--tw-pinch-zoom","resize","scroll-snap-type","--tw-scroll-snap-strictness","scroll-snap-align","scroll-snap-stop","scroll-margin","scroll-margin-inline","scroll-margin-block","scroll-margin-inline-start","scroll-margin-inline-end","scroll-margin-top","scroll-margin-right","scroll-margin-bottom","scroll-margin-left","scroll-padding","scroll-padding-inline","scroll-padding-block","scroll-padding-inline-start","scroll-padding-inline-end","scroll-padding-top","scroll-padding-right","scroll-padding-bottom","scroll-padding-left","list-style-position","list-style-type","list-style-image","appearance","columns","break-before","break-inside","break-after","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-template-columns","grid-template-rows","flex-direction","flex-wrap","place-content","place-items","align-content","align-items","justify-content","justify-items","gap","column-gap","row-gap","--tw-space-x-reverse","--tw-space-y-reverse","divide-x-width","divide-y-width","--tw-divide-y-reverse","divide-style","divide-color","place-self","align-self","justify-self","overflow","overflow-x","overflow-y","overscroll-behavior","overscroll-behavior-x","overscroll-behavior-y","scroll-behavior","border-radius","border-start-radius","border-end-radius","border-top-radius","border-right-radius","border-bottom-radius","border-left-radius","border-start-start-radius","border-start-end-radius","border-end-end-radius","border-end-start-radius","border-top-left-radius","border-top-right-radius","border-bottom-right-radius","border-bottom-left-radius","border-width","border-inline-width","border-block-width","border-inline-start-width","border-inline-end-width","border-top-width","border-right-width","border-bottom-width","border-left-width","border-style","border-inline-style","border-block-style","border-inline-start-style","border-inline-end-style","border-top-style","border-right-style","border-bottom-style","border-left-style","border-color","border-inline-color","border-block-color","border-inline-start-color","border-inline-end-color","border-top-color","border-right-color","border-bottom-color","border-left-color","background-color","background-image","--tw-gradient-position","--tw-gradient-stops","--tw-gradient-via-stops","--tw-gradient-from","--tw-gradient-from-position","--tw-gradient-via","--tw-gradient-via-position","--tw-gradient-to","--tw-gradient-to-position","box-decoration-break","background-size","background-attachment","background-clip","background-position","background-repeat","background-origin","fill","stroke","stroke-width","object-fit","object-position","padding","padding-inline","padding-block","padding-inline-start","padding-inline-end","padding-top","padding-right","padding-bottom","padding-left","text-align","text-indent","vertical-align","font-family","font-size","line-height","font-weight","letter-spacing","text-wrap","overflow-wrap","word-break","text-overflow","hyphens","white-space","color","text-transform","font-style","font-stretch","font-variant-numeric","text-decoration-line","text-decoration-color","text-decoration-style","text-decoration-thickness","text-underline-offset","-webkit-font-smoothing","placeholder-color","caret-color","accent-color","color-scheme","opacity","background-blend-mode","mix-blend-mode","box-shadow","--tw-shadow","--tw-shadow-color","--tw-ring-shadow","--tw-ring-color","--tw-inset-shadow","--tw-inset-shadow-color","--tw-inset-ring-shadow","--tw-inset-ring-color","--tw-ring-offset-width","--tw-ring-offset-color","outline","outline-width","outline-offset","outline-color","--tw-blur","--tw-brightness","--tw-contrast","--tw-drop-shadow","--tw-grayscale","--tw-hue-rotate","--tw-invert","--tw-saturate","--tw-sepia","filter","--tw-backdrop-blur","--tw-backdrop-brightness","--tw-backdrop-contrast","--tw-backdrop-grayscale","--tw-backdrop-hue-rotate","--tw-backdrop-invert","--tw-backdrop-opacity","--tw-backdrop-saturate","--tw-backdrop-sepia","backdrop-filter","transition-property","transition-behavior","transition-delay","transition-duration","transition-timing-function","will-change","contain","content","forced-color-adjust"];function Q(t,r,{onInvalidCandidate:i}={}){let e=new Map,o=[],c=new Map;for(let m of t){if(r.invalidCandidates.has(m)){i?.(m);continue}let p=r.parseCandidate(m);if(p.length===0){i?.(m);continue}c.set(m,p)}let s=r.getVariantOrder();for(let[m,p]of c){let u=!1;for(let d of p){let h=r.compileAstNodes(d);if(h.length!==0){u=!0;for(let{node:g,propertySort:y}of h){let w=0n;for(let v of d.variants)w|=1n<<BigInt(s.get(v));e.set(g,{properties:y,variants:w,candidate:m}),o.push(g)}}}u||i?.(m)}return o.sort((m,p)=>{let u=e.get(m),d=e.get(p);if(u.variants-d.variants!==0n)return Number(u.variants-d.variants);let h=0;for(;u.properties.length<h&&d.properties.length<h&&u.properties[h]===d.properties[h];)h+=1;return(u.properties[h]??1/0)-(d.properties[h]??1/0)||d.properties.length-u.properties.length||ke(u.candidate,d.candidate)}),{astNodes:o,nodeSorting:e}}function Ct(t,r){let i=br(t,r);if(i.length===0)return[];let e=[],o=`.${ye(t.raw)}`;for(let c of i){let s=kr(c);(t.important||r.important)&&$t(c);let m={kind:"rule",selector:o,nodes:c};for(let p of t.variants)if(se(m,p,r.variants)===null)return[];e.push({node:m,propertySort:s})}return e}function se(t,r,i,e=0){if(r.kind==="arbitrary"){if(r.relative&&e===0)return null;t.nodes=[W(r.selector,t.nodes)];return}let{applyFn:o}=i.get(r.root);if(r.kind==="compound"){let s=O("@slot");if(se(s,r.variant,i,e+1)===null||r.root==="not"&&s.nodes.length>1)return null;for(let p of s.nodes)if(p.kind!=="rule"&&p.kind!=="at-rule"||o(p,r)===null)return null;_(s.nodes,p=>{if((p.kind==="rule"||p.kind==="at-rule")&&p.nodes.length<=0)return p.nodes=t.nodes,1}),t.nodes=s.nodes;return}if(o(t,r)===null)return null}function Vt(t){let r=t.options?.types??[];return r.length>1&&r.includes("any")}function br(t,r){if(t.kind==="arbitrary"){let s=t.value;return t.modifier&&(s=L(s,t.modifier,r.theme)),s===null?[]:[[l(t.property,s)]]}let i=r.utilities.get(t.root)??[],e=[],o=i.filter(s=>!Vt(s));for(let s of o){if(s.kind!==t.kind)continue;let m=s.compileFn(t);if(m!==void 0){if(m===null)return e;e.push(m)}}if(e.length>0)return e;let c=i.filter(s=>Vt(s));for(let s of c){if(s.kind!==t.kind)continue;let m=s.compileFn(t);if(m!==void 0){if(m===null)return e;e.push(m)}}return e}function $t(t){for(let r of t)r.kind!=="at-root"&&(r.kind==="declaration"?r.important=!0:(r.kind==="rule"||r.kind==="at-rule")&&$t(r.nodes))}function kr(t){let r=new Set,i=t.slice();for(;i.length>0;){let e=i.shift();if(e.kind==="declaration"){if(e.property==="--tw-sort"){let c=De.indexOf(e.value??"");if(c!==-1){r.add(c);break}}let o=De.indexOf(e.property);o!==-1&&r.add(o)}else if(e.kind==="rule"||e.kind==="at-rule")for(let o of e.nodes)i.push(o)}return Array.from(r).sort((e,o)=>e-o)}function ge(t,r){let i=0,e=W("&",t),o=new Set,c=new M(()=>new Set),s=new M(()=>new Set);_([e],(h,{parent:g})=>{if(h.kind==="at-rule"){if(h.name==="@keyframes")return _(h.nodes,y=>{if(y.kind==="at-rule"&&y.name==="@apply")throw new Error("You cannot use `@apply` inside `@keyframes`.")}),1;if(h.name==="@utility"){let y=h.params.replace(/-\*$/,"");s.get(y).add(h),_(h.nodes,w=>{if(!(w.kind!=="at-rule"||w.name!=="@apply")){o.add(h);for(let v of Tt(w,r))c.get(h).add(v)}});return}if(h.name==="@apply"){if(g===null)return;i|=1,o.add(g);for(let y of Tt(h,r))c.get(g).add(y)}}});let m=new Set,p=[],u=new Set;function d(h,g=[]){if(!m.has(h)){if(u.has(h)){let y=g[(g.indexOf(h)+1)%g.length];throw h.kind==="at-rule"&&h.name==="@utility"&&y.kind==="at-rule"&&y.name==="@utility"&&_(h.nodes,w=>{if(w.kind!=="at-rule"||w.name!=="@apply")return;let v=w.params.split(/\s+/g);for(let x of v)for(let T of r.parseCandidate(x))switch(T.kind){case"arbitrary":break;case"static":case"functional":if(y.params.replace(/-\*$/,"")===T.root)throw new Error(`You cannot \`@apply\` the \`${x}\` utility here because it creates a circular dependency.`);break;default:}}),new Error(`Circular dependency detected:

${J([h])}
Relies on:

${J([y])}`)}u.add(h);for(let y of c.get(h))for(let w of s.get(y))g.push(h),d(w,g),g.pop();m.add(h),u.delete(h),p.push(h)}}for(let h of o)d(h);return _(p,(h,{replaceWith:g})=>{if(h.kind!=="at-rule"||h.name!=="@apply")return;let y=h.params.split(/\s+/g);{let w=Q(y,r,{onInvalidCandidate:x=>{throw new Error(`Cannot apply unknown utility class: ${x}`)}}).astNodes,v=[];for(let x of w)if(x.kind==="rule")for(let T of x.nodes)v.push(T);else v.push(x);g(v)}}),i}function*Tt(t,r){for(let i of t.params.split(/\s+/g))for(let e of r.parseCandidate(i))switch(e.kind){case"arbitrary":break;case"static":case"functional":yield e.root;break;default:}}async function _e(t,r,i,e=0){let o=0,c=[];return _(t,(s,{replaceWith:m})=>{if(s.kind==="at-rule"&&(s.name==="@import"||s.name==="@reference")){let p=xr(z(s.params));if(p===null)return;s.name==="@reference"&&(p.media="reference"),o|=2;let{uri:u,layer:d,media:h,supports:g}=p;if(u.startsWith("data:")||u.startsWith("http://")||u.startsWith("https://"))return;let y=Z({},[]);return c.push((async()=>{if(e>100)throw new Error(`Exceeded maximum recursion depth while resolving \`${u}\` in \`${r}\`)`);let w=await i(u,r),v=ee(w.content);await _e(v,w.base,i,e+1),y.nodes=Ar([Z({base:w.base},v)],d,h,g)})()),m(y),1}}),c.length>0&&await Promise.all(c),o}function xr(t){let r,i=null,e=null,o=null;for(let c=0;c<t.length;c++){let s=t[c];if(s.kind!=="separator"){if(s.kind==="word"&&!r){if(!s.value||s.value[0]!=='"'&&s.value[0]!=="'")return null;r=s.value.slice(1,-1);continue}if(s.kind==="function"&&s.value.toLowerCase()==="url"||!r)return null;if((s.kind==="word"||s.kind==="function")&&s.value.toLowerCase()==="layer"){if(i)return null;if(o)throw new Error("`layer(\u2026)` in an `@import` should come before any other functions or conditions");"nodes"in s?i=B(s.nodes):i="";continue}if(s.kind==="function"&&s.value.toLowerCase()==="supports"){if(o)return null;o=B(s.nodes);continue}e=B(t.slice(c));break}}return r?{uri:r,layer:i,media:e,supports:o}:null}function Ar(t,r,i,e){let o=t;return r!==null&&(o=[O("@layer",r,o)]),i!==null&&(o=[O("@media",i,o)]),e!==null&&(o=[O("@supports",e[0]==="("?e:`(${e})`,o)]),o}function ue(t,r=null){return Array.isArray(t)&&t.length===2&&typeof t[1]=="object"&&typeof t[1]!==null?r?t[1][r]??null:t[0]:Array.isArray(t)&&r===null?t.join(", "):typeof t=="string"&&r===null?t:null}function Et(t,{theme:r},i){for(let e of i){let o=Ae([e]);o&&t.theme.clearNamespace(`--${o}`,4)}for(let[e,o]of Cr(r)){if(typeof o!="string"&&typeof o!="number")continue;if(typeof o=="string"&&(o=o.replace(/<alpha-value>/g,"1")),e[0]==="opacity"&&(typeof o=="number"||typeof o=="string")){let s=typeof o=="string"?parseFloat(o):o;s>=0&&s<=1&&(o=s*100+"%")}let c=Ae(e);c&&t.theme.add(`--${c}`,""+o,7)}if(Object.hasOwn(r,"fontFamily")){let e=5;{let o=ue(r.fontFamily.sans);o&&t.theme.hasDefault("--font-sans")&&(t.theme.add("--default-font-family",o,e),t.theme.add("--default-font-feature-settings",ue(r.fontFamily.sans,"fontFeatureSettings")??"normal",e),t.theme.add("--default-font-variation-settings",ue(r.fontFamily.sans,"fontVariationSettings")??"normal",e))}{let o=ue(r.fontFamily.mono);o&&t.theme.hasDefault("--font-mono")&&(t.theme.add("--default-mono-font-family",o,e),t.theme.add("--default-mono-font-feature-settings",ue(r.fontFamily.mono,"fontFeatureSettings")??"normal",e),t.theme.add("--default-mono-font-variation-settings",ue(r.fontFamily.mono,"fontVariationSettings")??"normal",e))}}return r}function Cr(t){let r=[];return Kt(t,[],(i,e)=>{if($r(i))return r.push([e,i]),1;if(Tr(i)){r.push([e,i[0]]);for(let o of Reflect.ownKeys(i[1]))r.push([[...e,`-${o}`],i[1][o]]);return 1}if(Array.isArray(i)&&i.every(o=>typeof o=="string"))return r.push([e,i.join(", ")]),1}),r}var Vr=/^[a-zA-Z0-9-_%/\.]+$/;function Ae(t){if(t[0]==="container")return null;t=structuredClone(t),t[0]==="animation"&&(t[0]="animate"),t[0]==="aspectRatio"&&(t[0]="aspect"),t[0]==="borderRadius"&&(t[0]="radius"),t[0]==="boxShadow"&&(t[0]="shadow"),t[0]==="colors"&&(t[0]="color"),t[0]==="containers"&&(t[0]="container"),t[0]==="fontFamily"&&(t[0]="font"),t[0]==="fontSize"&&(t[0]="text"),t[0]==="letterSpacing"&&(t[0]="tracking"),t[0]==="lineHeight"&&(t[0]="leading"),t[0]==="maxWidth"&&(t[0]="container"),t[0]==="screens"&&(t[0]="breakpoint"),t[0]==="transitionTimingFunction"&&(t[0]="ease");for(let r of t)if(!Vr.test(r))return null;return t.map((r,i,e)=>r==="1"&&i!==e.length-1?"":r).map(r=>r.replaceAll(".","_").replace(/([a-z])([A-Z])/g,(i,e,o)=>`${e}-${o.toLowerCase()}`)).filter((r,i)=>r!=="DEFAULT"||i!==t.length-1).join("-")}function $r(t){return typeof t=="number"||typeof t=="string"}function Tr(t){if(!Array.isArray(t)||t.length!==2||typeof t[0]!="string"&&typeof t[0]!="number"||t[1]===void 0||t[1]===null||typeof t[1]!="object")return!1;for(let r of Reflect.ownKeys(t[1]))if(typeof r!="string"||typeof t[1][r]!="string"&&typeof t[1][r]!="number")return!1;return!0}function Kt(t,r=[],i){for(let e of Reflect.ownKeys(t)){let o=t[e];if(o==null)continue;let c=[...r,e],s=i(o,c)??0;if(s!==1){if(s===2)return 2;if(!(!Array.isArray(o)&&typeof o!="object")&&Kt(o,c,i)===2)return 2}}}function Ce(t){let r=[];for(let i of j(t,".")){if(!i.includes("[")){r.push(i);continue}let e=0;for(;;){let o=i.indexOf("[",e),c=i.indexOf("]",o);if(o===-1||c===-1)break;o>e&&r.push(i.slice(e,o)),r.push(i.slice(o+1,c)),e=c+1}e<=i.length-1&&r.push(i.slice(e))}return r}function fe(t){if(Object.prototype.toString.call(t)!=="[object Object]")return!1;let r=Object.getPrototypeOf(t);return r===null||Object.getPrototypeOf(r)===null}function me(t,r,i,e=[]){for(let o of r)if(o!=null)for(let c of Reflect.ownKeys(o)){e.push(c);let s=i(t[c],o[c],e);s!==void 0?t[c]=s:!fe(t[c])||!fe(o[c])?t[c]=o[c]:t[c]=me({},[t[c],o[c]],i,e),e.pop()}return t}function Ve(t,r,i){return function(o,c){let s=o.lastIndexOf("/"),m=null;s!==-1&&(m=o.slice(s+1).trim(),o=o.slice(0,s).trim());let p=(()=>{let u=Ce(o),[d,h]=Nr(t.theme,u),g=i(Rt(r()??{},u)??null);if(typeof g=="string"&&(g=g.replace("<alpha-value>","1")),typeof d!="object")return typeof h!="object"&&h&4?g??d:d;if(g!==null&&typeof g=="object"&&!Array.isArray(g)){let y=me({},[g],(w,v)=>v);if(d===null&&Object.hasOwn(g,"__CSS_VALUES__")){let w={};for(let v in g.__CSS_VALUES__)w[v]=g[v],delete y[v];d=w}for(let w in d)w!=="__CSS_VALUES__"&&(g?.__CSS_VALUES__?.[w]&4&&Rt(y,w.split("-"))!==void 0||(y[we(w)]=d[w]));return y}if(Array.isArray(d)&&Array.isArray(h)&&Array.isArray(g)){let y=d[0],w=d[1];h[0]&4&&(y=g[0]??y);for(let v of Object.keys(w))h[1][v]&4&&(w[v]=g[1][v]??w[v]);return[y,w]}return d??g})();return m&&typeof p=="string"&&(p=G(p,m)),p??c}}function Nr(t,r){if(r.length===1&&r[0].startsWith("--"))return[t.get([r[0]]),t.getOptions(r[0])];let i=Ae(r),e=new Map,o=new M(()=>new Map),c=t.namespace(`--${i}`);if(c.size===0)return[null,0];let s=new Map;for(let[d,h]of c){if(!d||!d.includes("--")){e.set(d,h),s.set(d,t.getOptions(d?`--${i}-${d}`:`--${i}`));continue}let g=d.indexOf("--"),y=d.slice(0,g),w=d.slice(g+2);w=w.replace(/-([a-z])/g,(v,x)=>x.toUpperCase()),o.get(y===""?null:y).set(w,[h,t.getOptions(`--${i}${d}`)])}let m=t.getOptions(`--${i}`);for(let[d,h]of o){let g=e.get(d);if(typeof g!="string")continue;let y={},w={};for(let[v,[x,T]]of h)y[v]=x,w[v]=T;e.set(d,[g,y]),s.set(d,[m,w])}let p={},u={};for(let[d,h]of e)Pt(p,[d??"DEFAULT"],h);for(let[d,h]of s)Pt(u,[d??"DEFAULT"],h);return r[r.length-1]==="DEFAULT"?[p?.DEFAULT??null,u.DEFAULT??0]:"DEFAULT"in p&&Object.keys(p).length===1?[p.DEFAULT,u.DEFAULT??0]:(p.__CSS_VALUES__=u,[p,u])}function Rt(t,r){for(let i=0;i<r.length;++i){let e=r[i];if(t?.[e]===void 0){if(r[i+1]===void 0)return;r[i+1]=`${e}-${r[i+1]}`;continue}t=t[e]}return t}function Pt(t,r,i){for(let e of r.slice(0,-1))t[e]===void 0&&(t[e]={}),t=t[e];t[r[r.length-1]]=i}function Sr(t){return{kind:"combinator",value:t}}function Er(t,r){return{kind:"function",value:t,nodes:r}}function he(t){return{kind:"selector",value:t}}function Kr(t){return{kind:"separator",value:t}}function Rr(t){return{kind:"value",value:t}}function $e(t,r,i=null){for(let e=0;e<t.length;e++){let o=t[e],c=!1,s=0,m=r(o,{parent:i,replaceWith(p){c=!0,Array.isArray(p)?p.length===0?(t.splice(e,1),s=0):p.length===1?(t[e]=p[0],s=1):(t.splice(e,1,...p),s=p.length):(t[e]=p,s=1)}})??0;if(c){m===0?e--:e+=s-1;continue}if(m===2)return 2;if(m!==1&&o.kind==="function"&&$e(o.nodes,r,o)===2)return 2}}function Te(t){let r="";for(let i of t)switch(i.kind){case"combinator":case"selector":case"separator":case"value":{r+=i.value;break}case"function":r+=i.value+"("+Te(i.nodes)+")"}return r}var jt=92,Pr=93,Ot=41,jr=58,Dt=44,Or=34,Dr=46,_t=62,Ft=10,_r=35,Ut=91,It=40,zt=43,Fr=39,Mt=32,Lt=9,Wt=126;function Fe(t){t=t.replaceAll(`\r
`,`
`);let r=[],i=[],e=null,o="",c;for(let s=0;s<t.length;s++){let m=t.charCodeAt(s);switch(m){case Dt:case _t:case Ft:case Mt:case zt:case Lt:case Wt:{if(o.length>0){let g=he(o);e?e.nodes.push(g):r.push(g),o=""}let p=s,u=s+1;for(;u<t.length&&(c=t.charCodeAt(u),!(c!==Dt&&c!==_t&&c!==Ft&&c!==Mt&&c!==zt&&c!==Lt&&c!==Wt));u++);s=u-1;let d=t.slice(p,u),h=d.trim()===","?Kr(d):Sr(d);e?e.nodes.push(h):r.push(h);break}case It:{let p=Er(o,[]);if(o="",p.value!==":not"&&p.value!==":where"&&p.value!==":has"&&p.value!==":is"){let u=s+1,d=0;for(let g=s+1;g<t.length;g++){if(c=t.charCodeAt(g),c===It){d++;continue}if(c===Ot){if(d===0){s=g;break}d--}}let h=s;p.nodes.push(Rr(t.slice(u,h))),o="",s=h,r.push(p);break}e?e.nodes.push(p):r.push(p),i.push(p),e=p;break}case Ot:{let p=i.pop();if(o.length>0){let u=he(o);p.nodes.push(u),o=""}i.length>0?e=i[i.length-1]:e=null;break}case Dr:case jr:case _r:{if(o.length>0){let p=he(o);e?e.nodes.push(p):r.push(p)}o=String.fromCharCode(m);break}case Ut:{if(o.length>0){let d=he(o);e?e.nodes.push(d):r.push(d)}o="";let p=s,u=0;for(let d=s+1;d<t.length;d++){if(c=t.charCodeAt(d),c===Ut){u++;continue}if(c===Pr){if(u===0){s=d;break}u--}}o+=t.slice(p,s+1);break}case Fr:case Or:{let p=s;for(let u=s+1;u<t.length;u++)if(c=t.charCodeAt(u),c===jt)u+=1;else if(c===m){s=u;break}o+=t.slice(p,s+1);break}case jt:{let p=t.charCodeAt(s+1);o+=String.fromCharCode(m)+String.fromCharCode(p),s+=1;break}default:o+=String.fromCharCode(m)}}return o.length>0&&r.push(he(o)),r}var Bt=/^[a-z@][a-zA-Z0-9/%._-]*$/;function Ue({designSystem:t,ast:r,resolvedConfig:i,featuresRef:e,referenceMode:o}){let c={addBase(s){if(o)return;let m=H(s);e.current|=ae(m,t),r.push(O("@layer","base",m))},addVariant(s,m){if(!xe.test(s))throw new Error(`\`addVariant('${s}')\` defines an invalid variant name. Variants should only contain alphanumeric, dashes or underscore characters.`);typeof m=="string"||Array.isArray(m)?t.variants.static(s,p=>{p.nodes=qt(m,p.nodes)},{compounds:ie(typeof m=="string"?[m]:m)}):typeof m=="object"&&t.variants.fromAst(s,H(m))},matchVariant(s,m,p){function u(h,g,y){let w=m(h,{modifier:g?.value??null});return qt(w,y)}let d=Object.keys(p?.values??{});t.variants.group(()=>{t.variants.functional(s,(h,g)=>{if(!g.value){if(p?.values&&"DEFAULT"in p.values){h.nodes=u(p.values.DEFAULT,g.modifier,h.nodes);return}return null}if(g.value.kind==="arbitrary")h.nodes=u(g.value.value,g.modifier,h.nodes);else if(g.value.kind==="named"&&p?.values){let y=p.values[g.value.value];if(typeof y!="string")return;h.nodes=u(y,g.modifier,h.nodes)}})},(h,g)=>{if(h.kind!=="functional"||g.kind!=="functional")return 0;let y=h.value?h.value.value:"DEFAULT",w=g.value?g.value.value:"DEFAULT",v=p?.values?.[y]??y,x=p?.values?.[w]??w;if(p&&typeof p.sort=="function")return p.sort({value:v,modifier:h.modifier?.value??null},{value:x,modifier:g.modifier?.value??null});let T=d.indexOf(y),N=d.indexOf(w);return T=T===-1?d.length:T,N=N===-1?d.length:N,T!==N?T-N:v<x?-1:1})},addUtilities(s){s=Array.isArray(s)?s:[s];let m=s.flatMap(u=>Object.entries(u));m=m.flatMap(([u,d])=>j(u,",").map(h=>[h.trim(),d]));let p=new M(()=>[]);for(let[u,d]of m){if(u.startsWith("@keyframes ")){o||r.push(W(u,H(d)));continue}let h=Fe(u),g=!1;if($e(h,y=>{if(y.kind==="selector"&&y.value[0]==="."&&Bt.test(y.value.slice(1))){let w=y.value;y.value="&";let v=Te(h),x=w.slice(1),T=v==="&"?H(d):[W(v,H(d))];p.get(x).push(...T),g=!0,y.value=w;return}if(y.kind==="function"&&y.value===":not")return 1}),!g)throw new Error(`\`addUtilities({ '${u}' : \u2026 })\` defines an invalid utility selector. Utilities must be a single class name and start with a lowercase letter, eg. \`.scrollbar-none\`.`)}for(let[u,d]of p)t.theme.prefix&&_(d,h=>{if(h.kind==="rule"){let g=Fe(h.selector);$e(g,y=>{y.kind==="selector"&&y.value[0]==="."&&(y.value=`.${t.theme.prefix}\\:${y.value.slice(1)}`)}),h.selector=Te(g)}}),t.utilities.static(u,()=>{let h=structuredClone(d);return e.current|=ge(h,t),h})},matchUtilities(s,m){let p=m?.type?Array.isArray(m?.type)?m.type:[m.type]:["any"];for(let[d,h]of Object.entries(s)){let g=function({negative:y}){return w=>{if(w.value?.kind==="arbitrary"&&p.length>0&&!p.includes("any")&&(w.value.dataType&&!p.includes(w.value.dataType)||!w.value.dataType&&!I(w.value.value,p)))return;let v=p.includes("color"),x=null,T=!1;{let E=m?.values??{};v&&(E=Object.assign({inherit:"inherit",transparent:"transparent",current:"currentColor"},E)),w.value?w.value.kind==="arbitrary"?x=w.value.value:w.value.fraction&&E[w.value.fraction]?(x=E[w.value.fraction],T=!0):E[w.value.value]?x=E[w.value.value]:E.__BARE_VALUE__&&(x=E.__BARE_VALUE__(w.value)??null,T=(w.value.fraction!==null&&x?.includes("/"))??!1):x=E.DEFAULT??null}if(x===null)return;let N;{let E=m?.modifiers??null;w.modifier?E==="any"||w.modifier.kind==="arbitrary"?N=w.modifier.value:E?.[w.modifier.value]?N=E[w.modifier.value]:v&&!Number.isNaN(Number(w.modifier.value))?N=`${w.modifier.value}%`:N=null:N=null}if(w.modifier&&N===null&&!T)return w.value?.kind==="arbitrary"?null:void 0;v&&N!==null&&(x=G(x,N)),y&&(x=`calc(${x} * -1)`);let R=H(h(x,{modifier:N}));return e.current|=ge(R,t),R}};var u=g;if(!Bt.test(d))throw new Error(`\`matchUtilities({ '${d}' : \u2026 })\` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter, eg. \`scrollbar\`.`);m?.supportsNegativeValues&&t.utilities.functional(`-${d}`,g({negative:!0}),{types:p}),t.utilities.functional(d,g({negative:!1}),{types:p}),t.utilities.suggest(d,()=>{let y=m?.values??{},w=new Set(Object.keys(y));w.delete("__BARE_VALUE__"),w.has("DEFAULT")&&(w.delete("DEFAULT"),w.add(null));let v=m?.modifiers??{},x=v==="any"?[]:Object.keys(v);return[{supportsNegative:m?.supportsNegativeValues??!1,values:Array.from(w),modifiers:x}]})}},addComponents(s,m){this.addUtilities(s,m)},matchComponents(s,m){this.matchUtilities(s,m)},theme:Ve(t,()=>i.theme??{},s=>s),prefix(s){return s},config(s,m){let p=i;if(!s)return p;let u=Ce(s);for(let d=0;d<u.length;++d){let h=u[d];if(p[h]===void 0)return m;p=p[h]}return p??m}};return c.addComponents=c.addComponents.bind(c),c.matchComponents=c.matchComponents.bind(c),c}function H(t){let r=[];t=Array.isArray(t)?t:[t];let i=t.flatMap(e=>Object.entries(e));for(let[e,o]of i)if(typeof o!="object"){if(!e.startsWith("--")){if(o==="@slot"){r.push(W(e,[O("@slot")]));continue}e=e.replace(/([A-Z])/g,"-$1").toLowerCase()}r.push(l(e,String(o)))}else if(Array.isArray(o))for(let c of o)typeof c=="string"?r.push(l(e,c)):r.push(W(e,H(c)));else o!==null&&r.push(W(e,H(o)));return r}function qt(t,r){return(typeof t=="string"?[t]:t).flatMap(e=>{if(e.trim().endsWith("}")){let o=e.replace("}","{@slot}}"),c=ee(o);return Oe(c,r),c}else return W(e,r)})}function Jt(t,r,i){for(let e of Ir(r))t.theme.addKeyframes(e)}function Ir(t){let r=[];if("keyframes"in t.theme)for(let[i,e]of Object.entries(t.theme.keyframes))r.push(O("@keyframes",i,H(e)));return r}function Gt(t){return{theme:{...Je,colors:({theme:r})=>r("color",{}),extend:{fontSize:({theme:r})=>({...r("text",{})}),boxShadow:({theme:r})=>({...r("shadow",{})}),animation:({theme:r})=>({...r("animate",{})}),aspectRatio:({theme:r})=>({...r("aspect",{})}),borderRadius:({theme:r})=>({...r("radius",{})}),screens:({theme:r})=>({...r("breakpoint",{})}),letterSpacing:({theme:r})=>({...r("tracking",{})}),lineHeight:({theme:r})=>({...r("leading",{})}),transitionDuration:{DEFAULT:t.get(["--default-transition-duration"])??null},transitionTimingFunction:{DEFAULT:t.get(["--default-transition-timing-function"])??null},maxWidth:({theme:r})=>({...r("container",{})})}}}}var zr={blocklist:[],future:{},prefix:"",important:!1,darkMode:null,theme:{},plugins:[],content:{files:[]}};function ze(t,r){let i={design:t,configs:[],plugins:[],content:{files:[]},theme:{},extend:{},result:structuredClone(zr)};for(let o of r)Ie(i,o);for(let o of i.configs)"darkMode"in o&&o.darkMode!==void 0&&(i.result.darkMode=o.darkMode??null),"prefix"in o&&o.prefix!==void 0&&(i.result.prefix=o.prefix??""),"blocklist"in o&&o.blocklist!==void 0&&(i.result.blocklist=o.blocklist??[]),"important"in o&&o.important!==void 0&&(i.result.important=o.important??!1);let e=Lr(i);return{resolvedConfig:{...i.result,content:i.content,theme:i.theme,plugins:i.plugins},replacedThemeKeys:e}}function Mr(t,r){if(Array.isArray(t)&&fe(t[0]))return t.concat(r);if(Array.isArray(r)&&fe(r[0])&&fe(t))return[t,...r];if(Array.isArray(r))return r}function Ie(t,{config:r,base:i,path:e,reference:o}){let c=[];for(let p of r.plugins??[])"__isOptionsFunction"in p?c.push({...p(),reference:o}):"handler"in p?c.push({...p,reference:o}):c.push({handler:p,reference:o});if(Array.isArray(r.presets)&&r.presets.length===0)throw new Error("Error in the config file/plugin/preset. An empty preset (`preset: []`) is not currently supported.");for(let p of r.presets??[])Ie(t,{path:e,base:i,config:p,reference:o});for(let p of c)t.plugins.push(p),p.config&&Ie(t,{path:e,base:i,config:p.config,reference:!!p.reference});let s=r.content??[],m=Array.isArray(s)?s:s.files;for(let p of m)t.content.files.push(typeof p=="object"?p:{base:i,pattern:p});t.configs.push(r)}function Lr(t){let r=new Set,i=Ve(t.design,()=>t.theme,o),e=Object.assign(i,{theme:i,colors:qe});function o(c){return typeof c=="function"?c(e)??null:c??null}for(let c of t.configs){let s=c.theme??{},m=s.extend??{};for(let p in s)p!=="extend"&&r.add(p);Object.assign(t.theme,s);for(let p in m)t.extend[p]??=[],t.extend[p].push(m[p])}delete t.theme.extend;for(let c in t.extend){let s=[t.theme[c],...t.extend[c]];t.theme[c]=()=>{let m=s.map(o);return me({},m,Mr)}}for(let c in t.theme)t.theme[c]=o(t.theme[c]);if(t.theme.screens&&typeof t.theme.screens=="object")for(let c of Object.keys(t.theme.screens)){let s=t.theme.screens[c];s&&typeof s=="object"&&("raw"in s||"max"in s||"min"in s&&(t.theme.screens[c]=s.min))}return r}function Ht(t,r){let i=t.theme.container||{};if(typeof i!="object"||i===null)return;let e=Wr(i,r);e.length!==0&&r.utilities.static("container",()=>structuredClone(e))}function Wr({center:t,padding:r,screens:i},e){let o=[],c=null;if(t&&o.push(l("margin-inline","auto")),(typeof r=="string"||typeof r=="object"&&r!==null&&"DEFAULT"in r)&&o.push(l("padding-inline",typeof r=="string"?r:r.DEFAULT)),typeof i=="object"&&i!==null){c=new Map;let s=Array.from(e.theme.namespace("--breakpoint").entries());if(s.sort((m,p)=>re(m[1],p[1],"asc")),s.length>0){let[m]=s[0];o.push(O("@media",`(width >= --theme(--breakpoint-${m}))`,[l("max-width","none")]))}for(let[m,p]of Object.entries(i)){if(typeof p=="object")if("min"in p)p=p.min;else continue;c.set(m,O("@media",`(width >= ${p})`,[l("max-width",p)]))}}if(typeof r=="object"&&r!==null){let s=Object.entries(r).filter(([m])=>m!=="DEFAULT").map(([m,p])=>[m,e.theme.resolveValue(m,["--breakpoint"]),p]).filter(Boolean);s.sort((m,p)=>re(m[1],p[1],"asc"));for(let[m,,p]of s)if(c&&c.has(m))c.get(m).nodes.push(l("padding-inline",p));else{if(c)continue;o.push(O("@media",`(width >= theme(--breakpoint-${m}))`,[l("padding-inline",p)]))}}if(c)for(let[,s]of c)o.push(s);return o}function Zt({addVariant:t,config:r}){let i=r("darkMode",null),[e,o=".dark"]=Array.isArray(i)?i:[i];if(e==="variant"){let c;if(Array.isArray(o)||typeof o=="function"?c=o:typeof o=="string"&&(c=[o]),Array.isArray(c))for(let s of c)s===".dark"?(e=!1,console.warn('When using `variant` for `darkMode`, you must provide a selector.\nExample: `darkMode: ["variant", ".your-selector &"]`')):s.includes("&")||(e=!1,console.warn('When using `variant` for `darkMode`, your selector must contain `&`.\nExample `darkMode: ["variant", ".your-selector &"]`'));o=c}e===null||(e==="selector"?t("dark",`&:where(${o}, ${o} *)`):e==="media"?t("dark","@media (prefers-color-scheme: dark)"):e==="variant"?t("dark",o):e==="class"&&t("dark",`&:is(${o} *)`))}function Yt(t){for(let[r,i]of[["t","top"],["tr","top right"],["r","right"],["br","bottom right"],["b","bottom"],["bl","bottom left"],["l","left"],["tl","top left"]])t.utilities.static(`bg-gradient-to-${r}`,()=>[l("--tw-gradient-position",`to ${i} in oklab`),l("background-image","linear-gradient(var(--tw-gradient-stops))")]);t.utilities.functional("max-w-screen",r=>{if(!r.value||r.value.kind==="arbitrary")return;let i=t.theme.resolve(r.value.value,["--breakpoint"]);if(i)return[l("max-width",i)]}),t.utilities.static("overflow-ellipsis",()=>[l("text-overflow","ellipsis")]),t.utilities.static("decoration-slice",()=>[l("-webkit-box-decoration-break","slice"),l("box-decoration-break","slice")]),t.utilities.static("decoration-clone",()=>[l("-webkit-box-decoration-break","clone"),l("box-decoration-break","clone")]),t.utilities.functional("flex-shrink",r=>{if(!r.modifier){if(!r.value)return[l("flex-shrink","1")];if(r.value.kind==="arbitrary")return[l("flex-shrink",r.value.value)];if(S(r.value.value))return[l("flex-shrink",r.value.value)]}}),t.utilities.functional("flex-grow",r=>{if(!r.modifier){if(!r.value)return[l("flex-grow","1")];if(r.value.kind==="arbitrary")return[l("flex-grow",r.value.value)];if(S(r.value.value))return[l("flex-grow",r.value.value)]}})}function Xt(t,r){let i=t.theme.screens||{},e=r.variants.get("min")?.order??0,o=[];for(let[s,m]of Object.entries(i)){let g=function(y){r.variants.static(s,w=>{w.nodes=[O("@media",h,w.nodes)]},{order:y})};var c=g;let p=r.variants.get(s),u=r.theme.resolveValue(s,["--breakpoint"]);if(p&&u&&!r.theme.hasDefault(`--breakpoint-${s}`))continue;let d=!0;typeof m=="string"&&(d=!1);let h=Br(m);d?o.push(g):g(e)}if(o.length!==0){for(let[,s]of r.variants.variants)s.order>e&&(s.order+=o.length);r.variants.compareFns=new Map(Array.from(r.variants.compareFns).map(([s,m])=>(s>e&&(s+=o.length),[s,m])));for(let[s,m]of o.entries())m(e+s+1)}}function Br(t){return(Array.isArray(t)?t:[t]).map(i=>typeof i=="string"?{min:i}:i&&typeof i=="object"?i:null).map(i=>{if(i===null)return null;if("raw"in i)return i.raw;let e="";return i.max!==void 0&&(e+=`${i.max} >= `),e+="width",i.min!==void 0&&(e+=` >= ${i.min}`),`(${e})`}).filter(Boolean).join(", ")}function Qt(t,r){let i=t.theme.aria||{},e=t.theme.supports||{},o=t.theme.data||{};if(Object.keys(i).length>0){let c=r.variants.get("aria"),s=c?.applyFn,m=c?.compounds;r.variants.functional("aria",(p,u)=>{let d=u.value;return d&&d.kind==="named"&&d.value in i?s?.(p,{...u,value:{kind:"arbitrary",value:i[d.value]}}):s?.(p,u)},{compounds:m})}if(Object.keys(e).length>0){let c=r.variants.get("supports"),s=c?.applyFn,m=c?.compounds;r.variants.functional("supports",(p,u)=>{let d=u.value;return d&&d.kind==="named"&&d.value in e?s?.(p,{...u,value:{kind:"arbitrary",value:e[d.value]}}):s?.(p,u)},{compounds:m})}if(Object.keys(o).length>0){let c=r.variants.get("data"),s=c?.applyFn,m=c?.compounds;r.variants.functional("data",(p,u)=>{let d=u.value;return d&&d.kind==="named"&&d.value in o?s?.(p,{...u,value:{kind:"arbitrary",value:o[d.value]}}):s?.(p,u)},{compounds:m})}}var qr=/^[a-z]+$/;async function tr({designSystem:t,base:r,ast:i,loadModule:e,globs:o}){let c=0,s=[],m=[];_(i,(h,{parent:g,replaceWith:y,context:w})=>{if(h.kind==="at-rule"){if(h.name==="@plugin"){if(g!==null)throw new Error("`@plugin` cannot be nested.");let v=h.params.slice(1,-1);if(v.length===0)throw new Error("`@plugin` must have a path.");let x={};for(let T of h.nodes??[]){if(T.kind!=="declaration")throw new Error(`Unexpected \`@plugin\` option:

${J([T])}

\`@plugin\` options must be a flat list of declarations.`);if(T.value===void 0)continue;let N=T.value,R=j(N,",").map(E=>{if(E=E.trim(),E==="null")return null;if(E==="true")return!0;if(E==="false")return!1;if(Number.isNaN(Number(E))){if(E[0]==='"'&&E[E.length-1]==='"'||E[0]==="'"&&E[E.length-1]==="'")return E.slice(1,-1);if(E[0]==="{"&&E[E.length-1]==="}")throw new Error(`Unexpected \`@plugin\` option: Value of declaration \`${J([T]).trim()}\` is not supported.

Using an object as a plugin option is currently only supported in JavaScript configuration files.`)}else return Number(E);return E});x[T.property]=R.length===1?R[0]:R}s.push([{id:v,base:w.base,reference:!!w.reference},Object.keys(x).length>0?x:null]),y([]),c|=4;return}if(h.name==="@config"){if(h.nodes.length>0)throw new Error("`@config` cannot have a body.");if(g!==null)throw new Error("`@config` cannot be nested.");m.push({id:h.params.slice(1,-1),base:w.base,reference:!!w.reference}),y([]),c|=4;return}}}),Yt(t);let p=t.resolveThemeValue;if(t.resolveThemeValue=function(g){return g.startsWith("--")?p(g):(c|=er({designSystem:t,base:r,ast:i,globs:o,configs:[],pluginDetails:[]}),t.resolveThemeValue(g))},!s.length&&!m.length)return 0;let[u,d]=await Promise.all([Promise.all(m.map(async({id:h,base:g,reference:y})=>{let w=await e(h,g,"config");return{path:h,base:w.base,config:w.module,reference:y}})),Promise.all(s.map(async([{id:h,base:g,reference:y},w])=>{let v=await e(h,g,"plugin");return{path:h,base:v.base,plugin:v.module,options:w,reference:y}}))]);return c|=er({designSystem:t,base:r,ast:i,globs:o,configs:u,pluginDetails:d}),c}function er({designSystem:t,base:r,ast:i,globs:e,configs:o,pluginDetails:c}){let s=0,p=[...c.map(v=>{if(!v.options)return{config:{plugins:[v.plugin]},base:v.base,reference:v.reference};if("__isOptionsFunction"in v.plugin)return{config:{plugins:[v.plugin(v.options)]},base:v.base,reference:v.reference};throw new Error(`The plugin "${v.path}" does not accept options`)}),...o],{resolvedConfig:u}=ze(t,[{config:Gt(t.theme),base:r,reference:!0},...p,{config:{plugins:[Zt]},base:r,reference:!0}]),{resolvedConfig:d,replacedThemeKeys:h}=ze(t,p);t.resolveThemeValue=function(x,T){let N=y.theme(x,T);if(Array.isArray(N)&&N.length===2)return N[0];if(Array.isArray(N))return N.join(", ");if(typeof N=="string")return N};let g={designSystem:t,ast:i,resolvedConfig:u,featuresRef:{set current(v){s|=v}}},y=Ue({...g,referenceMode:!1}),w;for(let{handler:v,reference:x}of u.plugins)x?(w||=Ue({...g,referenceMode:!0}),v(w)):v(y);if(Et(t,d,h),Jt(t,d,h),Qt(d,t),Xt(d,t),Ht(d,t),!t.theme.prefix&&u.prefix){if(u.prefix.endsWith("-")&&(u.prefix=u.prefix.slice(0,-1),console.warn(`The prefix "${u.prefix}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only and is written as a variant before all utilities. We have fixed up the prefix for you. Remove the trailing \`-\` to silence this warning.`)),!qr.test(u.prefix))throw new Error(`The prefix "${u.prefix}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.`);t.theme.prefix=u.prefix}if(!t.important&&u.important===!0&&(t.important=!0),typeof u.important=="string"){let v=u.important;_(i,(x,{replaceWith:T,parent:N})=>{if(x.kind==="at-rule"&&!(x.name!=="@tailwind"||x.params!=="utilities"))return N?.kind==="rule"&&N.selector===v?2:(T(F(v,[x])),2)})}for(let v of u.blocklist)t.invalidCandidates.add(v);for(let v of u.content.files){if("raw"in v)throw new Error(`Error in the config file/plugin/preset. The \`content\` key contains a \`raw\` entry:

${JSON.stringify(v,null,2)}

This feature is not currently supported.`);e.push(v)}return s}var Jr=/^[a-z]+$/;function Gr(){throw new Error("No `loadModule` function provided to `compile`")}function Hr(){throw new Error("No `loadStylesheet` function provided to `compile`")}function Zr(t){let r=0,i=null;for(let e of j(t," "))e==="reference"?r|=2:e==="inline"?r|=1:e==="default"?r|=4:e==="static"?r|=8:e.startsWith("prefix(")&&e.endsWith(")")&&(i=e.slice(7,-1));return[r,i]}var de=(m=>(m[m.None=0]="None",m[m.AtApply=1]="AtApply",m[m.AtImport=2]="AtImport",m[m.JsPluginCompat=4]="JsPluginCompat",m[m.ThemeFunction=8]="ThemeFunction",m[m.Utilities=16]="Utilities",m[m.Variants=32]="Variants",m))(de||{});async function rr(t,{base:r="",loadModule:i=Gr,loadStylesheet:e=Hr}={}){let o=0;t=[Z({base:r},t)],o|=await _e(t,r,e);let c=null,s=new Me,m=[],p=[],u=null,d=null,h=[],g=[],y=null;_(t,(v,{parent:x,replaceWith:T,context:N})=>{if(v.kind==="at-rule"){if(v.name==="@tailwind"&&(v.params==="utilities"||v.params.startsWith("utilities"))){if(d!==null){T([]);return}let R=j(v.params," ");for(let E of R)if(E.startsWith("source(")){let P=E.slice(7,-1);if(P==="none"){y=P;continue}if(P[0]==='"'&&P[P.length-1]!=='"'||P[0]==="'"&&P[P.length-1]!=="'"||P[0]!=="'"&&P[0]!=='"')throw new Error("`source(\u2026)` paths must be quoted.");y={base:N.sourceBase??N.base,pattern:P.slice(1,-1)}}d=v,o|=16}if(v.name==="@utility"){if(x!==null)throw new Error("`@utility` cannot be nested.");if(v.nodes.length===0)throw new Error(`\`@utility ${v.params}\` is empty. Utilities should include at least one property.`);let R=gt(v);if(R===null)throw new Error(`\`@utility ${v.params}\` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter.`);p.push(R)}if(v.name==="@source"){if(v.nodes.length>0)throw new Error("`@source` cannot have a body.");if(x!==null)throw new Error("`@source` cannot be nested.");let R=v.params;if(R[0]==='"'&&R[R.length-1]!=='"'||R[0]==="'"&&R[R.length-1]!=="'"||R[0]!=="'"&&R[0]!=='"')throw new Error("`@source` paths must be quoted.");g.push({base:N.base,pattern:R.slice(1,-1)}),T([]);return}if(v.name==="@variant"&&(x===null?v.nodes.length===0?v.name="@custom-variant":(_(v.nodes,R=>{if(R.kind==="at-rule"&&R.name==="@slot")return v.name="@custom-variant",2}),v.name==="@variant"&&h.push(v)):h.push(v)),v.name==="@custom-variant"){if(x!==null)throw new Error("`@custom-variant` cannot be nested.");T([]);let[R,E]=j(v.params," ");if(!xe.test(R))throw new Error(`\`@custom-variant ${R}\` defines an invalid variant name. Variants should only contain alphanumeric, dashes or underscore characters.`);if(v.nodes.length>0&&E)throw new Error(`\`@custom-variant ${R}\` cannot have both a selector and a body.`);if(v.nodes.length===0){if(!E)throw new Error(`\`@custom-variant ${R}\` has no selector or body.`);let P=j(E.slice(1,-1),",");if(P.length===0||P.some(f=>f.trim()===""))throw new Error(`\`@custom-variant ${R} (${P.join(",")})\` selector is invalid.`);let n=[],a=[];for(let f of P)f=f.trim(),f[0]==="@"?n.push(f):a.push(f);m.push(f=>{f.variants.static(R,k=>{let b=[];a.length>0&&b.push(F(a.join(", "),k.nodes));for(let $ of n)b.push(W($,k.nodes));k.nodes=b},{compounds:ie([...a,...n])})});return}else{m.push(P=>{P.variants.fromAst(R,v.nodes)});return}}if(v.name==="@media"){let R=j(v.params," "),E=[];for(let P of R)if(P.startsWith("source(")){let n=P.slice(7,-1);_(v.nodes,(a,{replaceWith:f})=>{if(a.kind==="at-rule"&&a.name==="@tailwind"&&a.params==="utilities")return a.params+=` source(${n})`,f([Z({sourceBase:N.base},[a])]),2})}else if(P.startsWith("theme(")){let n=P.slice(6,-1);_(v.nodes,a=>{if(a.kind!=="at-rule")throw new Error('Files imported with `@import "\u2026" theme(\u2026)` must only contain `@theme` blocks.');if(a.name==="@theme")return a.params+=" "+n,1})}else if(P.startsWith("prefix(")){let n=P.slice(7,-1);_(v.nodes,a=>{if(a.kind==="at-rule"&&a.name==="@theme")return a.params+=` prefix(${n})`,1})}else P==="important"?c=!0:P==="reference"?v.nodes=[Z({reference:!0},v.nodes)]:E.push(P);E.length>0?v.params=E.join(" "):R.length>0&&T(v.nodes)}if(v.name==="@theme"){let[R,E]=Zr(v.params);if(N.reference&&(R|=2),E){if(!Jr.test(E))throw new Error(`The prefix "${E}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.`);s.prefix=E}return _(v.nodes,P=>{if(P.kind==="at-rule"&&P.name==="@keyframes")return R&2?1:(s.addKeyframes(P),1);if(P.kind==="comment")return;if(P.kind==="declaration"&&P.property.startsWith("--")){s.add(we(P.property),P.value??"",R);return}let n=J([O(v.name,v.params,[P])]).split(`
`).map((a,f,k)=>`${f===0||f>=k.length-2?" ":">"} ${a}`).join(`
`);throw new Error(`\`@theme\` blocks must only contain custom properties or \`@keyframes\`.

${n}`)}),!u&&!(R&2)?(u=F(":root, :host",[]),T([u])):T([]),1}}});let w=At(s);c&&(w.important=c),o|=await tr({designSystem:w,base:r,ast:t,loadModule:i,globs:g});for(let v of m)v(w);for(let v of p)v(w);if(u){let v=[];for(let[T,N]of w.theme.entries())N.options&2||v.push(l(ye(T),N.value));let x=w.theme.getKeyframes();for(let T of x)v.push(D([T]));u.nodes=[Z({theme:!0},v)]}if(d){let v=d;v.kind="context",v.context={}}if(h.length>0){for(let v of h){let x=F("&",v.nodes),T=v.params,N=w.parseVariant(T);if(N===null)throw new Error(`Cannot use \`@variant\` with unknown variant: ${T}`);if(se(x,N,w.variants)===null)throw new Error(`Cannot use \`@variant\` with variant: ${T}`);Object.assign(v,x)}o|=32}return o|=ae(t,w),o|=ge(t,w),_(t,(v,{replaceWith:x})=>{if(v.kind==="at-rule")return v.name==="@utility"&&x([]),1}),{designSystem:w,ast:t,globs:g,root:y,utilitiesNode:d,features:o}}async function Yr(t,r={}){let{designSystem:i,ast:e,globs:o,root:c,utilitiesNode:s,features:m}=await rr(t,r);e.unshift(Le(`! tailwindcss v${Ge} | MIT License | https://tailwindcss.com `));function p(g){i.invalidCandidates.add(g)}let u=new Set,d=null,h=0;return{globs:o,root:c,features:m,build(g){if(m===0)return t;if(!s)return d??=te(e,i),d;let y=!1,w=u.size;for(let x of g)i.invalidCandidates.has(x)||(x[0]==="-"&&x[1]==="-"?i.theme.markUsedVariable(x):u.add(x),y||=u.size!==w);if(!y)return d??=te(e,i),d;let v=Q(u,i,{onInvalidCandidate:p}).astNodes;return h===v.length?(d??=te(e,i),d):(h=v.length,s.nodes=v,d=te(e,i),d)}}}async function So(t,r={}){let i=ee(t),e=await Yr(i,r),o=i,c=t;return{...e,build(s){let m=e.build(s);return m===o||(c=J(m),o=m),c}}}async function Eo(t,r={}){return(await rr(ee(t),r)).designSystem}function Xr(){throw new Error("It looks like you're trying to use `tailwindcss` directly as a PostCSS plugin. The PostCSS plugin has moved to a separate package, so to continue using Tailwind CSS with PostCSS you'll need to install `@tailwindcss/postcss` and update your PostCSS configuration.")}export{de as Features,Eo as __unstable__loadDesignSystem,So as compile,Yr as compileAst,Xr as default};
