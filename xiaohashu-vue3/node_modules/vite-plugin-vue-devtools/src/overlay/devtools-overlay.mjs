(function(){"use strict";/**
* @vue/shared v3.5.13
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function ys(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const le={},nn=[],je=()=>{},Tc=()=>!1,ao=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Es=e=>e.startsWith("onUpdate:"),we=Object.assign,bs=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},xc=Object.prototype.hasOwnProperty,oe=(e,t)=>xc.call(e,t),Y=Array.isArray,yn=e=>uo(e)==="[object Map]",Oc=e=>uo(e)==="[object Set]",K=e=>typeof e=="function",me=e=>typeof e=="string",Bt=e=>typeof e=="symbol",pe=e=>e!==null&&typeof e=="object",Qr=e=>(pe(e)||K(e))&&K(e.then)&&K(e.catch),Ac=Object.prototype.toString,uo=e=>Ac.call(e),Cc=e=>uo(e).slice(8,-1),Dc=e=>uo(e)==="[object Object]",ws=e=>me(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,En=ys(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),co=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Pc=/-(\w)/g,Ue=co(e=>e.replace(Pc,(t,n)=>n?n.toUpperCase():"")),Ic=/\B([A-Z])/g,wt=co(e=>e.replace(Ic,"-$1").toLowerCase()),fo=co(e=>e.charAt(0).toUpperCase()+e.slice(1)),Ss=co(e=>e?`on${fo(e)}`:""),St=(e,t)=>!Object.is(e,t),Ts=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},ei=(e,t,n,o=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:o,value:n})},kc=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let ti;const po=()=>ti||(ti=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Ne(e){if(Y(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],s=me(o)?Lc(o):Ne(o);if(s)for(const r in s)t[r]=s[r]}return t}else if(me(e)||pe(e))return e}const Rc=/;(?![^(]*\))/g,Nc=/:([^]+)/,Vc=/\/\*[^]*?\*\//g;function Lc(e){const t={};return e.replace(Vc,"").split(Rc).forEach(n=>{if(n){const o=n.split(Nc);o.length>1&&(t[o[0].trim()]=o[1].trim())}}),t}function pt(e){let t="";if(me(e))t=e;else if(Y(e))for(let n=0;n<e.length;n++){const o=pt(e[n]);o&&(t+=o+" ")}else if(pe(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}function Mc(e){if(!e)return null;let{class:t,style:n}=e;return t&&!me(t)&&(e.class=pt(t)),n&&(e.style=Ne(n)),e}const $c=ys("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function ni(e){return!!e||e===""}var Fc={npm_package_dependencies__vueuse_core:"^12.0.0",TERM_PROGRAM:"vscode",NODE:"/Users/<USER>/Library/Application Support/fnm/node-versions/v20.17.0/installation/bin/node",INIT_CWD:"/Users/<USER>/g/devtools-next/packages/overlay",npm_package_devDependencies_vite:"^6.0.1",SHELL:"/bin/zsh",TERM:"xterm-256color",npm_config_shamefully_hoist:"true",npm_package_dependencies__vue_devtools_shared:"workspace:^",npm_package_devDependencies__vitejs_plugin_vue:"^5.2.1",npm_config_registry:"https://registry.npmjs.org/",npm_package_private:"true",USER:"arlo",npm_package_license:"MIT",PNPM_SCRIPT_SRC_DIR:"/Users/<USER>/g/devtools-next/packages/overlay",npm_config_strict_peer_dependencies:"",__CF_USER_TEXT_ENCODING:"0x1F5:0x19:0x34",npm_execpath:"/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@9.15.2/node_modules/pnpm/bin/pnpm.cjs",npm_config_frozen_lockfile:"",PATH:"/Users/<USER>/g/devtools-next/packages/overlay/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@9.15.2/node_modules/pnpm/dist/node-gyp-bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@9.15.2/node_modules/pnpm/dist/node-gyp-bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/.cache/node/corepack/v1/pnpm/9.15.0/dist/node-gyp-bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/Library/Caches/fnm_multishells/87168_1737676917540/bin:/opt/homebrew/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Users/<USER>/Library/pnpm:/Users/<USER>/Library/Caches/fnm_multishells/87221_1737529505749/bin:/opt/homebrew/bin:/Users/<USER>/.cargo/bin:/Applications/WebStorm.app/Contents/MacOS:/Applications/WebStorm.app/Contents/MacOS:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/node_modules/.bin:/Users/<USER>/node_modules/.bin:/Users/<USER>/.bin:/node_modules/.bin",npm_package_dependencies__vue_devtools_ui:"workspace:*",npm_package_devDependencies_sass_embedded:"^1.81.0",npm_package_author:"webfansplz",npm_command:"run-script",PWD:"/Users/<USER>/g/devtools-next/packages/overlay",npm_package_exports____:"./dist/*",npm_lifecycle_event:"build",npm_package_devDependencies_vue:"^3.5.13",npm_package_name:"@vue/devtools-overlay",LANG:"zh_CN.UTF-8",npm_package_scripts_build:"vite build",NODE_PATH:"/Users/<USER>/g/devtools-next/node_modules/.pnpm/vite@6.0.7_@types+node@22.10.5_jiti@2.4.2_sass-embedded@1.83.1_terser@5.37.0_tsx@4.19.2_yaml@2.7.0/node_modules/vite/bin/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/vite@6.0.7_@types+node@22.10.5_jiti@2.4.2_sass-embedded@1.83.1_terser@5.37.0_tsx@4.19.2_yaml@2.7.0/node_modules/vite/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/vite@6.0.7_@types+node@22.10.5_jiti@2.4.2_sass-embedded@1.83.1_terser@5.37.0_tsx@4.19.2_yaml@2.7.0/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@9.15.2/node_modules/pnpm/bin/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@9.15.2/node_modules/pnpm/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@9.15.2/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/node_modules",TURBO_HASH:"ed56765cad1619d5",VSCODE_GIT_ASKPASS_EXTRA_ARGS:"",npm_package_engines_node:">=v14.21.3",npm_config_node_gyp:"/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@9.15.2/node_modules/pnpm/dist/node_modules/node-gyp/bin/node-gyp.js",npm_config_side_effects_cache:"",npm_package_devDependencies__iconify_json:"^2.2.277",npm_package_version:"7.7.1",VSCODE_INJECTION:"1",npm_package_dependencies__vue_devtools_core:"workspace:^",npm_package_type:"module",HOME:"/Users/<USER>",SHLVL:"0",VSCODE_GIT_ASKPASS_MAIN:"/Applications/Cursor.app/Contents/Resources/app/extensions/git/dist/askpass-main.js",npm_lifecycle_script:"vite build",npm_package_dependencies__vue_devtools_kit:"workspace:*",VSCODE_GIT_IPC_HANDLE:"/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/vscode-git-92667dda0d.sock",npm_config_user_agent:"pnpm/9.15.2 npm/? node/v20.17.0 darwin arm64",npm_package_devDependencies__types_node:"^22.10.1",VSCODE_GIT_ASKPASS_NODE:"/Applications/Cursor.app/Contents/Frameworks/Cursor Helper (Plugin).app/Contents/MacOS/Cursor Helper (Plugin)",npm_package_scripts_stub:"vite build --watch",npm_package_files_0:"dist",npm_package_scripts_play:"vite --config vite.play.config.ts --open",npm_node_execpath:"/Users/<USER>/Library/Application Support/fnm/node-versions/v20.17.0/installation/bin/node",npm_config_shell_emulator:"true",COLORTERM:"truecolor",NODE_ENV:"production"};let Oe;class Uc{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Oe,!t&&Oe&&(this.index=(Oe.scopes||(Oe.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Oe;try{return Oe=this,t()}finally{Oe=n}}}on(){Oe=this}off(){Oe=this.parent}stop(t){if(this._active){this._active=!1;let n,o;for(n=0,o=this.effects.length;n<o;n++)this.effects[n].stop();for(this.effects.length=0,n=0,o=this.cleanups.length;n<o;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,o=this.scopes.length;n<o;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const s=this.parent.scopes.pop();s&&s!==this&&(this.parent.scopes[this.index]=s,s.index=this.index)}this.parent=void 0}}}function oi(){return Oe}function Bc(e,t=!1){Oe&&Oe.cleanups.push(e)}let ue;const xs=new WeakSet;class si{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Oe&&Oe.active&&Oe.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,xs.has(this)&&(xs.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||ii(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,fi(this),li(this);const t=ue,n=Ke;ue=this,Ke=!0;try{return this.fn()}finally{ai(this),ue=t,Ke=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Ds(t);this.deps=this.depsTail=void 0,fi(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?xs.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Cs(this)&&this.run()}get dirty(){return Cs(this)}}let ri=0,bn,wn;function ii(e,t=!1){if(e.flags|=8,t){e.next=wn,wn=e;return}e.next=bn,bn=e}function Os(){ri++}function As(){if(--ri>0)return;if(wn){let t=wn;for(wn=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;bn;){let t=bn;for(bn=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(o){e||(e=o)}t=n}}if(e)throw e}function li(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function ai(e){let t,n=e.depsTail,o=n;for(;o;){const s=o.prevDep;o.version===-1?(o===n&&(n=s),Ds(o),Hc(o)):t=o,o.dep.activeLink=o.prevActiveLink,o.prevActiveLink=void 0,o=s}e.deps=t,e.depsTail=n}function Cs(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(ui(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function ui(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Sn))return;e.globalVersion=Sn;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!Cs(e)){e.flags&=-3;return}const n=ue,o=Ke;ue=e,Ke=!0;try{li(e);const s=e.fn(e._value);(t.version===0||St(s,e._value))&&(e._value=s,t.version++)}catch(s){throw t.version++,s}finally{ue=n,Ke=o,ai(e),e.flags&=-3}}function Ds(e,t=!1){const{dep:n,prevSub:o,nextSub:s}=e;if(o&&(o.nextSub=s,e.prevSub=void 0),s&&(s.prevSub=o,e.nextSub=void 0),n.subs===e&&(n.subs=o,!o&&n.computed)){n.computed.flags&=-5;for(let r=n.computed.deps;r;r=r.nextDep)Ds(r,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Hc(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Ke=!0;const ci=[];function ht(){ci.push(Ke),Ke=!1}function _t(){const e=ci.pop();Ke=e===void 0?!0:e}function fi(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=ue;ue=void 0;try{t()}finally{ue=n}}}let Sn=0;class zc{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class ho{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!ue||!Ke||ue===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==ue)n=this.activeLink=new zc(ue,this),ue.deps?(n.prevDep=ue.depsTail,ue.depsTail.nextDep=n,ue.depsTail=n):ue.deps=ue.depsTail=n,di(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const o=n.nextDep;o.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=o),n.prevDep=ue.depsTail,n.nextDep=void 0,ue.depsTail.nextDep=n,ue.depsTail=n,ue.deps===n&&(ue.deps=o)}return n}trigger(t){this.version++,Sn++,this.notify(t)}notify(t){Os();try{Fc.NODE_ENV;for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{As()}}}function di(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let o=t.deps;o;o=o.nextDep)di(o)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const _o=new WeakMap,Ht=Symbol(""),Ps=Symbol(""),Tn=Symbol("");function Se(e,t,n){if(Ke&&ue){let o=_o.get(e);o||_o.set(e,o=new Map);let s=o.get(n);s||(o.set(n,s=new ho),s.map=o,s.key=n),s.track()}}function mt(e,t,n,o,s,r){const i=_o.get(e);if(!i){Sn++;return}const l=a=>{a&&a.trigger()};if(Os(),t==="clear")i.forEach(l);else{const a=Y(e),u=a&&ws(n);if(a&&n==="length"){const f=Number(o);i.forEach((c,h)=>{(h==="length"||h===Tn||!Bt(h)&&h>=f)&&l(c)})}else switch((n!==void 0||i.has(void 0))&&l(i.get(n)),u&&l(i.get(Tn)),t){case"add":a?u&&l(i.get("length")):(l(i.get(Ht)),yn(e)&&l(i.get(Ps)));break;case"delete":a||(l(i.get(Ht)),yn(e)&&l(i.get(Ps)));break;case"set":yn(e)&&l(i.get(Ht));break}}As()}function jc(e,t){const n=_o.get(e);return n&&n.get(t)}function on(e){const t=ne(e);return t===e?t:(Se(t,"iterate",Tn),We(e)?t:t.map(Ae))}function Is(e){return Se(e=ne(e),"iterate",Tn),e}const Kc={__proto__:null,[Symbol.iterator](){return ks(this,Symbol.iterator,Ae)},concat(...e){return on(this).concat(...e.map(t=>Y(t)?on(t):t))},entries(){return ks(this,"entries",e=>(e[1]=Ae(e[1]),e))},every(e,t){return gt(this,"every",e,t,void 0,arguments)},filter(e,t){return gt(this,"filter",e,t,n=>n.map(Ae),arguments)},find(e,t){return gt(this,"find",e,t,Ae,arguments)},findIndex(e,t){return gt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return gt(this,"findLast",e,t,Ae,arguments)},findLastIndex(e,t){return gt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return gt(this,"forEach",e,t,void 0,arguments)},includes(...e){return Rs(this,"includes",e)},indexOf(...e){return Rs(this,"indexOf",e)},join(e){return on(this).join(e)},lastIndexOf(...e){return Rs(this,"lastIndexOf",e)},map(e,t){return gt(this,"map",e,t,void 0,arguments)},pop(){return xn(this,"pop")},push(...e){return xn(this,"push",e)},reduce(e,...t){return pi(this,"reduce",e,t)},reduceRight(e,...t){return pi(this,"reduceRight",e,t)},shift(){return xn(this,"shift")},some(e,t){return gt(this,"some",e,t,void 0,arguments)},splice(...e){return xn(this,"splice",e)},toReversed(){return on(this).toReversed()},toSorted(e){return on(this).toSorted(e)},toSpliced(...e){return on(this).toSpliced(...e)},unshift(...e){return xn(this,"unshift",e)},values(){return ks(this,"values",Ae)}};function ks(e,t,n){const o=Is(e),s=o[t]();return o!==e&&!We(e)&&(s._next=s.next,s.next=()=>{const r=s._next();return r.value&&(r.value=n(r.value)),r}),s}const Wc=Array.prototype;function gt(e,t,n,o,s,r){const i=Is(e),l=i!==e&&!We(e),a=i[t];if(a!==Wc[t]){const c=a.apply(e,r);return l?Ae(c):c}let u=n;i!==e&&(l?u=function(c,h){return n.call(this,Ae(c),h,e)}:n.length>2&&(u=function(c,h){return n.call(this,c,h,e)}));const f=a.call(i,u,o);return l&&s?s(f):f}function pi(e,t,n,o){const s=Is(e);let r=n;return s!==e&&(We(e)?n.length>3&&(r=function(i,l,a){return n.call(this,i,l,a,e)}):r=function(i,l,a){return n.call(this,i,Ae(l),a,e)}),s[t](r,...o)}function Rs(e,t,n){const o=ne(e);Se(o,"iterate",Tn);const s=o[t](...n);return(s===-1||s===!1)&&Vs(n[0])?(n[0]=ne(n[0]),o[t](...n)):s}function xn(e,t,n=[]){ht(),Os();const o=ne(e)[t].apply(e,n);return As(),_t(),o}const Gc=ys("__proto__,__v_isRef,__isVue"),hi=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Bt));function Yc(e){Bt(e)||(e=String(e));const t=ne(this);return Se(t,"has",e),t.hasOwnProperty(e)}class _i{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,o){if(n==="__v_skip")return t.__v_skip;const s=this._isReadonly,r=this._isShallow;if(n==="__v_isReactive")return!s;if(n==="__v_isReadonly")return s;if(n==="__v_isShallow")return r;if(n==="__v_raw")return o===(s?r?bi:Ei:r?yi:vi).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(o)?t:void 0;const i=Y(t);if(!s){let a;if(i&&(a=Kc[n]))return a;if(n==="hasOwnProperty")return Yc}const l=Reflect.get(t,n,ye(t)?t:o);return(Bt(n)?hi.has(n):Gc(n))||(s||Se(t,"get",n),r)?l:ye(l)?i&&ws(n)?l:l.value:pe(l)?s?On(l):sn(l):l}}class mi extends _i{constructor(t=!1){super(!1,t)}set(t,n,o,s){let r=t[n];if(!this._isShallow){const a=zt(r);if(!We(o)&&!zt(o)&&(r=ne(r),o=ne(o)),!Y(t)&&ye(r)&&!ye(o))return a?!1:(r.value=o,!0)}const i=Y(t)&&ws(n)?Number(n)<t.length:oe(t,n),l=Reflect.set(t,n,o,ye(t)?t:s);return t===ne(s)&&(i?St(o,r)&&mt(t,"set",n,o):mt(t,"add",n,o)),l}deleteProperty(t,n){const o=oe(t,n);t[n];const s=Reflect.deleteProperty(t,n);return s&&o&&mt(t,"delete",n,void 0),s}has(t,n){const o=Reflect.has(t,n);return(!Bt(n)||!hi.has(n))&&Se(t,"has",n),o}ownKeys(t){return Se(t,"iterate",Y(t)?"length":Ht),Reflect.ownKeys(t)}}class gi extends _i{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const qc=new mi,Xc=new gi,Zc=new mi(!0),Jc=new gi(!0),Ns=e=>e,mo=e=>Reflect.getPrototypeOf(e);function Qc(e,t,n){return function(...o){const s=this.__v_raw,r=ne(s),i=yn(r),l=e==="entries"||e===Symbol.iterator&&i,a=e==="keys"&&i,u=s[e](...o),f=n?Ns:t?Ls:Ae;return!t&&Se(r,"iterate",a?Ps:Ht),{next(){const{value:c,done:h}=u.next();return h?{value:c,done:h}:{value:l?[f(c[0]),f(c[1])]:f(c),done:h}},[Symbol.iterator](){return this}}}}function go(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function ef(e,t){const n={get(s){const r=this.__v_raw,i=ne(r),l=ne(s);e||(St(s,l)&&Se(i,"get",s),Se(i,"get",l));const{has:a}=mo(i),u=t?Ns:e?Ls:Ae;if(a.call(i,s))return u(r.get(s));if(a.call(i,l))return u(r.get(l));r!==i&&r.get(s)},get size(){const s=this.__v_raw;return!e&&Se(ne(s),"iterate",Ht),Reflect.get(s,"size",s)},has(s){const r=this.__v_raw,i=ne(r),l=ne(s);return e||(St(s,l)&&Se(i,"has",s),Se(i,"has",l)),s===l?r.has(s):r.has(s)||r.has(l)},forEach(s,r){const i=this,l=i.__v_raw,a=ne(l),u=t?Ns:e?Ls:Ae;return!e&&Se(a,"iterate",Ht),l.forEach((f,c)=>s.call(r,u(f),u(c),i))}};return we(n,e?{add:go("add"),set:go("set"),delete:go("delete"),clear:go("clear")}:{add(s){!t&&!We(s)&&!zt(s)&&(s=ne(s));const r=ne(this);return mo(r).has.call(r,s)||(r.add(s),mt(r,"add",s,s)),this},set(s,r){!t&&!We(r)&&!zt(r)&&(r=ne(r));const i=ne(this),{has:l,get:a}=mo(i);let u=l.call(i,s);u||(s=ne(s),u=l.call(i,s));const f=a.call(i,s);return i.set(s,r),u?St(r,f)&&mt(i,"set",s,r):mt(i,"add",s,r),this},delete(s){const r=ne(this),{has:i,get:l}=mo(r);let a=i.call(r,s);a||(s=ne(s),a=i.call(r,s)),l&&l.call(r,s);const u=r.delete(s);return a&&mt(r,"delete",s,void 0),u},clear(){const s=ne(this),r=s.size!==0,i=s.clear();return r&&mt(s,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(s=>{n[s]=Qc(s,e,t)}),n}function vo(e,t){const n=ef(e,t);return(o,s,r)=>s==="__v_isReactive"?!e:s==="__v_isReadonly"?e:s==="__v_raw"?o:Reflect.get(oe(n,s)&&s in o?n:o,s,r)}const tf={get:vo(!1,!1)},nf={get:vo(!1,!0)},of={get:vo(!0,!1)},sf={get:vo(!0,!0)},vi=new WeakMap,yi=new WeakMap,Ei=new WeakMap,bi=new WeakMap;function rf(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function lf(e){return e.__v_skip||!Object.isExtensible(e)?0:rf(Cc(e))}function sn(e){return zt(e)?e:Eo(e,!1,qc,tf,vi)}function af(e){return Eo(e,!1,Zc,nf,yi)}function On(e){return Eo(e,!0,Xc,of,Ei)}function yo(e){return Eo(e,!0,Jc,sf,bi)}function Eo(e,t,n,o,s){if(!pe(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const r=s.get(e);if(r)return r;const i=lf(e);if(i===0)return e;const l=new Proxy(e,i===2?o:n);return s.set(e,l),l}function An(e){return zt(e)?An(e.__v_raw):!!(e&&e.__v_isReactive)}function zt(e){return!!(e&&e.__v_isReadonly)}function We(e){return!!(e&&e.__v_isShallow)}function Vs(e){return e?!!e.__v_raw:!1}function ne(e){const t=e&&e.__v_raw;return t?ne(t):e}function uf(e){return!oe(e,"__v_skip")&&Object.isExtensible(e)&&ei(e,"__v_skip",!0),e}const Ae=e=>pe(e)?sn(e):e,Ls=e=>pe(e)?On(e):e;function ye(e){return e?e.__v_isRef===!0:!1}function de(e){return Si(e,!1)}function wi(e){return Si(e,!0)}function Si(e,t){return ye(e)?e:new cf(e,t)}class cf{constructor(t,n){this.dep=new ho,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:ne(t),this._value=n?t:Ae(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,o=this.__v_isShallow||We(t)||zt(t);t=o?t:ne(t),St(t,n)&&(this._rawValue=t,this._value=o?t:Ae(t),this.dep.trigger())}}function Q(e){return ye(e)?e.value:e}function Be(e){return K(e)?e():Q(e)}const ff={get:(e,t,n)=>t==="__v_raw"?e:Q(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const s=e[t];return ye(s)&&!ye(n)?(s.value=n,!0):Reflect.set(e,t,n,o)}};function Ti(e){return An(e)?e:new Proxy(e,ff)}class df{constructor(t){this.__v_isRef=!0,this._value=void 0;const n=this.dep=new ho,{get:o,set:s}=t(n.track.bind(n),n.trigger.bind(n));this._get=o,this._set=s}get value(){return this._value=this._get()}set value(t){this._set(t)}}function pf(e){return new df(e)}class hf{constructor(t,n,o){this._object=t,this._key=n,this._defaultValue=o,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return jc(ne(this._object),this._key)}}class _f{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function mf(e,t,n){return ye(e)?e:K(e)?new _f(e):pe(e)&&arguments.length>1?gf(e,t,n):de(e)}function gf(e,t,n){const o=e[t];return ye(o)?o:new hf(e,t,n)}class vf{constructor(t,n,o){this.fn=t,this.setter=n,this._value=void 0,this.dep=new ho(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Sn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=o}notify(){if(this.flags|=16,!(this.flags&8)&&ue!==this)return ii(this,!0),!0}get value(){const t=this.dep.track();return ui(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function yf(e,t,n=!1){let o,s;return K(e)?o=e:(o=e.get,s=e.set),new vf(o,s,n)}const bo={},wo=new WeakMap;let jt;function Ef(e,t=!1,n=jt){if(n){let o=wo.get(n);o||wo.set(n,o=[]),o.push(e)}}function bf(e,t,n=le){const{immediate:o,deep:s,once:r,scheduler:i,augmentJob:l,call:a}=n,u=b=>s?b:We(b)||s===!1||s===0?vt(b,1):vt(b);let f,c,h,d,_=!1,v=!1;if(ye(e)?(c=()=>e.value,_=We(e)):An(e)?(c=()=>u(e),_=!0):Y(e)?(v=!0,_=e.some(b=>An(b)||We(b)),c=()=>e.map(b=>{if(ye(b))return b.value;if(An(b))return u(b);if(K(b))return a?a(b,2):b()})):K(e)?t?c=a?()=>a(e,2):e:c=()=>{if(h){ht();try{h()}finally{_t()}}const b=jt;jt=f;try{return a?a(e,3,[d]):e(d)}finally{jt=b}}:c=je,t&&s){const b=c,I=s===!0?1/0:s;c=()=>vt(b(),I)}const g=oi(),y=()=>{f.stop(),g&&g.active&&bs(g.effects,f)};if(r&&t){const b=t;t=(...I)=>{b(...I),y()}}let T=v?new Array(e.length).fill(bo):bo;const P=b=>{if(!(!(f.flags&1)||!f.dirty&&!b))if(t){const I=f.run();if(s||_||(v?I.some((F,U)=>St(F,T[U])):St(I,T))){h&&h();const F=jt;jt=f;try{const U=[I,T===bo?void 0:v&&T[0]===bo?[]:T,d];a?a(t,3,U):t(...U),T=I}finally{jt=F}}}else f.run()};return l&&l(P),f=new si(c),f.scheduler=i?()=>i(P,!1):P,d=b=>Ef(b,!1,f),h=f.onStop=()=>{const b=wo.get(f);if(b){if(a)a(b,4);else for(const I of b)I();wo.delete(f)}},t?o?P(!0):T=f.run():i?i(P.bind(null,!0),!0):f.run(),y.pause=f.pause.bind(f),y.resume=f.resume.bind(f),y.stop=y,y}function vt(e,t=1/0,n){if(t<=0||!pe(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,ye(e))vt(e.value,t,n);else if(Y(e))for(let o=0;o<e.length;o++)vt(e[o],t,n);else if(Oc(e)||yn(e))e.forEach(o=>{vt(o,t,n)});else if(Dc(e)){for(const o in e)vt(e[o],t,n);for(const o of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,o)&&vt(e[o],t,n)}return e}var Tt={npm_package_dependencies__vueuse_core:"^12.0.0",TERM_PROGRAM:"vscode",NODE:"/Users/<USER>/Library/Application Support/fnm/node-versions/v20.17.0/installation/bin/node",INIT_CWD:"/Users/<USER>/g/devtools-next/packages/overlay",npm_package_devDependencies_vite:"^6.0.1",SHELL:"/bin/zsh",TERM:"xterm-256color",npm_config_shamefully_hoist:"true",npm_package_dependencies__vue_devtools_shared:"workspace:^",npm_package_devDependencies__vitejs_plugin_vue:"^5.2.1",npm_config_registry:"https://registry.npmjs.org/",npm_package_private:"true",USER:"arlo",npm_package_license:"MIT",PNPM_SCRIPT_SRC_DIR:"/Users/<USER>/g/devtools-next/packages/overlay",npm_config_strict_peer_dependencies:"",__CF_USER_TEXT_ENCODING:"0x1F5:0x19:0x34",npm_execpath:"/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@9.15.2/node_modules/pnpm/bin/pnpm.cjs",npm_config_frozen_lockfile:"",PATH:"/Users/<USER>/g/devtools-next/packages/overlay/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@9.15.2/node_modules/pnpm/dist/node-gyp-bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@9.15.2/node_modules/pnpm/dist/node-gyp-bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/.cache/node/corepack/v1/pnpm/9.15.0/dist/node-gyp-bin:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/Library/Caches/fnm_multishells/87168_1737676917540/bin:/opt/homebrew/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Users/<USER>/Library/pnpm:/Users/<USER>/Library/Caches/fnm_multishells/87221_1737529505749/bin:/opt/homebrew/bin:/Users/<USER>/.cargo/bin:/Applications/WebStorm.app/Contents/MacOS:/Applications/WebStorm.app/Contents/MacOS:/Users/<USER>/g/devtools-next/node_modules/.bin:/Users/<USER>/g/node_modules/.bin:/Users/<USER>/node_modules/.bin:/Users/<USER>/.bin:/node_modules/.bin",npm_package_dependencies__vue_devtools_ui:"workspace:*",npm_package_devDependencies_sass_embedded:"^1.81.0",npm_package_author:"webfansplz",npm_command:"run-script",PWD:"/Users/<USER>/g/devtools-next/packages/overlay",npm_package_exports____:"./dist/*",npm_lifecycle_event:"build",npm_package_devDependencies_vue:"^3.5.13",npm_package_name:"@vue/devtools-overlay",LANG:"zh_CN.UTF-8",npm_package_scripts_build:"vite build",NODE_PATH:"/Users/<USER>/g/devtools-next/node_modules/.pnpm/vite@6.0.7_@types+node@22.10.5_jiti@2.4.2_sass-embedded@1.83.1_terser@5.37.0_tsx@4.19.2_yaml@2.7.0/node_modules/vite/bin/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/vite@6.0.7_@types+node@22.10.5_jiti@2.4.2_sass-embedded@1.83.1_terser@5.37.0_tsx@4.19.2_yaml@2.7.0/node_modules/vite/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/vite@6.0.7_@types+node@22.10.5_jiti@2.4.2_sass-embedded@1.83.1_terser@5.37.0_tsx@4.19.2_yaml@2.7.0/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@9.15.2/node_modules/pnpm/bin/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@9.15.2/node_modules/pnpm/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@9.15.2/node_modules:/Users/<USER>/g/devtools-next/node_modules/.pnpm/node_modules",TURBO_HASH:"ed56765cad1619d5",VSCODE_GIT_ASKPASS_EXTRA_ARGS:"",npm_package_engines_node:">=v14.21.3",npm_config_node_gyp:"/Users/<USER>/g/devtools-next/node_modules/.pnpm/pnpm@9.15.2/node_modules/pnpm/dist/node_modules/node-gyp/bin/node-gyp.js",npm_config_side_effects_cache:"",npm_package_devDependencies__iconify_json:"^2.2.277",npm_package_version:"7.7.1",VSCODE_INJECTION:"1",npm_package_dependencies__vue_devtools_core:"workspace:^",npm_package_type:"module",HOME:"/Users/<USER>",SHLVL:"0",VSCODE_GIT_ASKPASS_MAIN:"/Applications/Cursor.app/Contents/Resources/app/extensions/git/dist/askpass-main.js",npm_lifecycle_script:"vite build",npm_package_dependencies__vue_devtools_kit:"workspace:*",VSCODE_GIT_IPC_HANDLE:"/var/folders/6s/79hm4q7x5f982tndmbvckmn80000gn/T/vscode-git-92667dda0d.sock",npm_config_user_agent:"pnpm/9.15.2 npm/? node/v20.17.0 darwin arm64",npm_package_devDependencies__types_node:"^22.10.1",VSCODE_GIT_ASKPASS_NODE:"/Applications/Cursor.app/Contents/Frameworks/Cursor Helper (Plugin).app/Contents/MacOS/Cursor Helper (Plugin)",npm_package_scripts_stub:"vite build --watch",npm_package_files_0:"dist",npm_package_scripts_play:"vite --config vite.play.config.ts --open",npm_node_execpath:"/Users/<USER>/Library/Application Support/fnm/node-versions/v20.17.0/installation/bin/node",npm_config_shell_emulator:"true",COLORTERM:"truecolor",NODE_ENV:"production"};const Cn=[];let Ms=!1;function wf(e,...t){if(Ms)return;Ms=!0,ht();const n=Cn.length?Cn[Cn.length-1].component:null,o=n&&n.appContext.config.warnHandler,s=Sf();if(o)rn(o,n,11,[e+t.map(r=>{var i,l;return(l=(i=r.toString)==null?void 0:i.call(r))!=null?l:JSON.stringify(r)}).join(""),n&&n.proxy,s.map(({vnode:r})=>`at <${yl(n,r.type)}>`).join(`
`),s]);else{const r=[`[Vue warn]: ${e}`,...t];s.length&&r.push(`
`,...Tf(s)),console.warn(...r)}_t(),Ms=!1}function Sf(){let e=Cn[Cn.length-1];if(!e)return[];const t=[];for(;e;){const n=t[0];n&&n.vnode===e?n.recurseCount++:t.push({vnode:e,recurseCount:0});const o=e.component&&e.component.parent;e=o&&o.vnode}return t}function Tf(e){const t=[];return e.forEach((n,o)=>{t.push(...o===0?[]:[`
`],...xf(n))}),t}function xf({vnode:e,recurseCount:t}){const n=t>0?`... (${t} recursive calls)`:"",o=e.component?e.component.parent==null:!1,s=` at <${yl(e.component,e.type,o)}`,r=">"+n;return e.props?[s,...Of(e.props),r]:[s+r]}function Of(e){const t=[],n=Object.keys(e);return n.slice(0,3).forEach(o=>{t.push(...xi(o,e[o]))}),n.length>3&&t.push(" ..."),t}function xi(e,t,n){return me(t)?(t=JSON.stringify(t),n?t:[`${e}=${t}`]):typeof t=="number"||typeof t=="boolean"||t==null?n?t:[`${e}=${t}`]:ye(t)?(t=xi(e,ne(t.value),!0),n?t:[`${e}=Ref<`,t,">"]):K(t)?[`${e}=fn${t.name?`<${t.name}>`:""}`]:(t=ne(t),n?t:[`${e}=`,t])}function rn(e,t,n,o){try{return o?e(...o):e()}catch(s){So(s,t,n)}}function Ze(e,t,n,o){if(K(e)){const s=rn(e,t,n,o);return s&&Qr(s)&&s.catch(r=>{So(r,t,n)}),s}if(Y(e)){const s=[];for(let r=0;r<e.length;r++)s.push(Ze(e[r],t,n,o));return s}}function So(e,t,n,o=!0){const s=t?t.vnode:null,{errorHandler:r,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||le;if(t){let l=t.parent;const a=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const f=l.ec;if(f){for(let c=0;c<f.length;c++)if(f[c](e,a,u)===!1)return}l=l.parent}if(r){ht(),rn(r,null,10,[e,a,u]),_t();return}}Af(e,n,s,o,i)}function Af(e,t,n,o=!0,s=!1){if(s)throw e;console.error(e)}const Ce=[];let Je=-1;const ln=[];let xt=null,an=0;const Oi=Promise.resolve();let To=null;function xo(e){const t=To||Oi;return e?t.then(this?e.bind(this):e):t}function Cf(e){let t=Je+1,n=Ce.length;for(;t<n;){const o=t+n>>>1,s=Ce[o],r=Dn(s);r<e||r===e&&s.flags&2?t=o+1:n=o}return t}function $s(e){if(!(e.flags&1)){const t=Dn(e),n=Ce[Ce.length-1];!n||!(e.flags&2)&&t>=Dn(n)?Ce.push(e):Ce.splice(Cf(t),0,e),e.flags|=1,Ai()}}function Ai(){To||(To=Oi.then(Pi))}function Df(e){Y(e)?ln.push(...e):xt&&e.id===-1?xt.splice(an+1,0,e):e.flags&1||(ln.push(e),e.flags|=1),Ai()}function Ci(e,t,n=Je+1){for(;n<Ce.length;n++){const o=Ce[n];if(o&&o.flags&2){if(e&&o.id!==e.uid)continue;Ce.splice(n,1),n--,o.flags&4&&(o.flags&=-2),o(),o.flags&4||(o.flags&=-2)}}}function Di(e){if(ln.length){const t=[...new Set(ln)].sort((n,o)=>Dn(n)-Dn(o));if(ln.length=0,xt){xt.push(...t);return}for(xt=t,an=0;an<xt.length;an++){const n=xt[an];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}xt=null,an=0}}const Dn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Pi(e){const t=je;try{for(Je=0;Je<Ce.length;Je++){const n=Ce[Je];n&&!(n.flags&8)&&(Tt.NODE_ENV!=="production"&&t(n),n.flags&4&&(n.flags&=-2),rn(n,n.i,n.i?15:14),n.flags&4||(n.flags&=-2))}}finally{for(;Je<Ce.length;Je++){const n=Ce[Je];n&&(n.flags&=-2)}Je=-1,Ce.length=0,Di(),To=null,(Ce.length||ln.length)&&Pi()}}let Ee=null,Oo=null;function Ao(e){const t=Ee;return Ee=e,Oo=e&&e.type.__scopeId||null,t}function Pf(e){Oo=e}function If(){Oo=null}const kf=e=>Co;function Co(e,t=Ee,n){if(!t||e._n)return e;const o=(...s)=>{o._d&&ul(-1);const r=Ao(t);let i;try{i=e(...s)}finally{Ao(r),o._d&&ul(1)}return i};return o._n=!0,o._c=!0,o._d=!0,o}function Qe(e,t){if(Ee===null)return e;const n=$o(Ee),o=e.dirs||(e.dirs=[]);for(let s=0;s<t.length;s++){let[r,i,l,a=le]=t[s];r&&(K(r)&&(r={mounted:r,updated:r}),r.deep&&vt(i),o.push({dir:r,instance:n,value:i,oldValue:void 0,arg:l,modifiers:a}))}return e}function Kt(e,t,n,o){const s=e.dirs,r=t&&t.dirs;for(let i=0;i<s.length;i++){const l=s[i];r&&(l.oldValue=r[i].value);let a=l.dir[o];a&&(ht(),Ze(a,n,8,[e.el,l,e,t]),_t())}}const Rf=Symbol("_vte"),Nf=e=>e.__isTeleport;function Fs(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Fs(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function un(e,t){return K(e)?we({name:e.name},t,{setup:e}):e}function Ii(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Do(e,t,n,o,s=!1){if(Y(e)){e.forEach((_,v)=>Do(_,t&&(Y(t)?t[v]:t),n,o,s));return}if(cn(o)&&!s){o.shapeFlag&512&&o.type.__asyncResolved&&o.component.subTree.component&&Do(e,t,n,o.component.subTree);return}const r=o.shapeFlag&4?$o(o.component):o.el,i=s?null:r,{i:l,r:a}=e,u=t&&t.r,f=l.refs===le?l.refs={}:l.refs,c=l.setupState,h=ne(c),d=c===le?()=>!1:_=>oe(h,_);if(u!=null&&u!==a&&(me(u)?(f[u]=null,d(u)&&(c[u]=null)):ye(u)&&(u.value=null)),K(a))rn(a,l,12,[i,f]);else{const _=me(a),v=ye(a);if(_||v){const g=()=>{if(e.f){const y=_?d(a)?c[a]:f[a]:a.value;s?Y(y)&&bs(y,r):Y(y)?y.includes(r)||y.push(r):_?(f[a]=[r],d(a)&&(c[a]=f[a])):(a.value=[r],e.k&&(f[e.k]=a.value))}else _?(f[a]=i,d(a)&&(c[a]=i)):v&&(a.value=i,e.k&&(f[e.k]=i))};i?(g.id=-1,Ve(g,n)):g()}}}po().requestIdleCallback,po().cancelIdleCallback;const cn=e=>!!e.type.__asyncLoader,ki=e=>e.type.__isKeepAlive;function Vf(e,t){Ri(e,"a",t)}function Lf(e,t){Ri(e,"da",t)}function Ri(e,t,n=ge){const o=e.__wdc||(e.__wdc=()=>{let s=n;for(;s;){if(s.isDeactivated)return;s=s.parent}return e()});if(Po(t,o,n),n){let s=n.parent;for(;s&&s.parent;)ki(s.parent.vnode)&&Mf(o,t,n,s),s=s.parent}}function Mf(e,t,n,o){const s=Po(t,e,o,!0);Ni(()=>{bs(o[t],s)},n)}function Po(e,t,n=ge,o=!1){if(n){const s=n[e]||(n[e]=[]),r=t.__weh||(t.__weh=(...i)=>{ht();const l=$n(n),a=Ze(t,n,e,i);return l(),_t(),a});return o?s.unshift(r):s.push(r),r}}const yt=e=>(t,n=ge)=>{(!Fn||e==="sp")&&Po(e,(...o)=>t(...o),n)},$f=yt("bm"),Pn=yt("m"),Ff=yt("bu"),Uf=yt("u"),Bf=yt("bum"),Ni=yt("um"),Hf=yt("sp"),zf=yt("rtg"),jf=yt("rtc");function Kf(e,t=ge){Po("ec",e,t)}const Wf="components";function Us(e,t){return Yf(Wf,e,!0,t)||e}const Gf=Symbol.for("v-ndc");function Yf(e,t,n=!0,o=!1){const s=Ee||ge;if(s){const r=s.type;{const l=vl(r,!1);if(l&&(l===t||l===Ue(t)||l===fo(Ue(t))))return r}const i=Vi(s[e]||r[e],t)||Vi(s.appContext[e],t);return!i&&o?r:i}}function Vi(e,t){return e&&(e[t]||e[Ue(t)]||e[fo(Ue(t))])}function Io(e,t,n={},o,s){if(Ee.ce||Ee.parent&&cn(Ee.parent)&&Ee.parent.ce)return t!=="default"&&(n.name=t),Ie(),fn(Pe,null,[Te("slot",n,o)],64);let r=e[t];r&&r._c&&(r._d=!1),Ie();const i=r&&Li(r(n)),l=n.key||i&&i.key,a=fn(Pe,{key:(l&&!Bt(l)?l:`_${t}`)+""},i||[],i&&e._===1?64:-2);return a.scopeId&&(a.slotScopeIds=[a.scopeId+"-s"]),r&&r._c&&(r._d=!0),a}function Li(e){return e.some(t=>Ln(t)?!(t.type===Ot||t.type===Pe&&!Li(t.children)):!0)?e:null}const Bs=e=>e?_l(e)?$o(e):Bs(e.parent):null,In=we(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Bs(e.parent),$root:e=>Bs(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Ui(e),$forceUpdate:e=>e.f||(e.f=()=>{$s(e.update)}),$nextTick:e=>e.n||(e.n=xo.bind(e.proxy)),$watch:e=>md.bind(e)}),Hs=(e,t)=>e!==le&&!e.__isScriptSetup&&oe(e,t),qf={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:o,data:s,props:r,accessCache:i,type:l,appContext:a}=e;let u;if(t[0]!=="$"){const d=i[t];if(d!==void 0)switch(d){case 1:return o[t];case 2:return s[t];case 4:return n[t];case 3:return r[t]}else{if(Hs(o,t))return i[t]=1,o[t];if(s!==le&&oe(s,t))return i[t]=2,s[t];if((u=e.propsOptions[0])&&oe(u,t))return i[t]=3,r[t];if(n!==le&&oe(n,t))return i[t]=4,n[t];zs&&(i[t]=0)}}const f=In[t];let c,h;if(f)return t==="$attrs"&&Se(e.attrs,"get",""),f(e);if((c=l.__cssModules)&&(c=c[t]))return c;if(n!==le&&oe(n,t))return i[t]=4,n[t];if(h=a.config.globalProperties,oe(h,t))return h[t]},set({_:e},t,n){const{data:o,setupState:s,ctx:r}=e;return Hs(s,t)?(s[t]=n,!0):o!==le&&oe(o,t)?(o[t]=n,!0):oe(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(r[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:o,appContext:s,propsOptions:r}},i){let l;return!!n[i]||e!==le&&oe(e,i)||Hs(t,i)||(l=r[0])&&oe(l,i)||oe(o,i)||oe(In,i)||oe(s.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:oe(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Mi(e){return Y(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let zs=!0;function Xf(e){const t=Ui(e),n=e.proxy,o=e.ctx;zs=!1,t.beforeCreate&&$i(t.beforeCreate,e,"bc");const{data:s,computed:r,methods:i,watch:l,provide:a,inject:u,created:f,beforeMount:c,mounted:h,beforeUpdate:d,updated:_,activated:v,deactivated:g,beforeDestroy:y,beforeUnmount:T,destroyed:P,unmounted:b,render:I,renderTracked:F,renderTriggered:U,errorCaptured:z,serverPrefetch:G,expose:D,inheritAttrs:N,components:$,directives:j,filters:J}=t;if(u&&Zf(u,o,null),i)for(const L in i){const Z=i[L];K(Z)&&(o[L]=Z.bind(n))}if(s){const L=s.call(n,n);pe(L)&&(e.data=sn(L))}if(zs=!0,r)for(const L in r){const Z=r[L],ae=K(Z)?Z.bind(n,n):K(Z.get)?Z.get.bind(n,n):je,Me=!K(Z)&&K(Z.set)?Z.set.bind(n):je,xe=be({get:ae,set:Me});Object.defineProperty(o,L,{enumerable:!0,configurable:!0,get:()=>xe.value,set:_e=>xe.value=_e})}if(l)for(const L in l)Fi(l[L],o,n,L);if(a){const L=K(a)?a.call(n):a;Reflect.ownKeys(L).forEach(Z=>{od(Z,L[Z])})}f&&$i(f,e,"c");function X(L,Z){Y(Z)?Z.forEach(ae=>L(ae.bind(n))):Z&&L(Z.bind(n))}if(X($f,c),X(Pn,h),X(Ff,d),X(Uf,_),X(Vf,v),X(Lf,g),X(Kf,z),X(jf,F),X(zf,U),X(Bf,T),X(Ni,b),X(Hf,G),Y(D))if(D.length){const L=e.exposed||(e.exposed={});D.forEach(Z=>{Object.defineProperty(L,Z,{get:()=>n[Z],set:ae=>n[Z]=ae})})}else e.exposed||(e.exposed={});I&&e.render===je&&(e.render=I),N!=null&&(e.inheritAttrs=N),$&&(e.components=$),j&&(e.directives=j),G&&Ii(e)}function Zf(e,t,n=je){Y(e)&&(e=js(e));for(const o in e){const s=e[o];let r;pe(s)?"default"in s?r=Rn(s.from||o,s.default,!0):r=Rn(s.from||o):r=Rn(s),ye(r)?Object.defineProperty(t,o,{enumerable:!0,configurable:!0,get:()=>r.value,set:i=>r.value=i}):t[o]=r}}function $i(e,t,n){Ze(Y(e)?e.map(o=>o.bind(t.proxy)):e.bind(t.proxy),t,n)}function Fi(e,t,n,o){let s=o.includes(".")?sl(n,o):()=>n[o];if(me(e)){const r=t[e];K(r)&&et(s,r)}else if(K(e))et(s,e.bind(n));else if(pe(e))if(Y(e))e.forEach(r=>Fi(r,t,n,o));else{const r=K(e.handler)?e.handler.bind(n):t[e.handler];K(r)&&et(s,r,e)}}function Ui(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:s,optionsCache:r,config:{optionMergeStrategies:i}}=e.appContext,l=r.get(t);let a;return l?a=l:!s.length&&!n&&!o?a=t:(a={},s.length&&s.forEach(u=>ko(a,u,i,!0)),ko(a,t,i)),pe(t)&&r.set(t,a),a}function ko(e,t,n,o=!1){const{mixins:s,extends:r}=t;r&&ko(e,r,n,!0),s&&s.forEach(i=>ko(e,i,n,!0));for(const i in t)if(!(o&&i==="expose")){const l=Jf[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const Jf={data:Bi,props:Hi,emits:Hi,methods:kn,computed:kn,beforeCreate:De,created:De,beforeMount:De,mounted:De,beforeUpdate:De,updated:De,beforeDestroy:De,beforeUnmount:De,destroyed:De,unmounted:De,activated:De,deactivated:De,errorCaptured:De,serverPrefetch:De,components:kn,directives:kn,watch:ed,provide:Bi,inject:Qf};function Bi(e,t){return t?e?function(){return we(K(e)?e.call(this,this):e,K(t)?t.call(this,this):t)}:t:e}function Qf(e,t){return kn(js(e),js(t))}function js(e){if(Y(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function De(e,t){return e?[...new Set([].concat(e,t))]:t}function kn(e,t){return e?we(Object.create(null),e,t):t}function Hi(e,t){return e?Y(e)&&Y(t)?[...new Set([...e,...t])]:we(Object.create(null),Mi(e),Mi(t??{})):t}function ed(e,t){if(!e)return t;if(!t)return e;const n=we(Object.create(null),e);for(const o in t)n[o]=De(e[o],t[o]);return n}function zi(){return{app:null,config:{isNativeTag:Tc,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let td=0;function nd(e,t){return function(o,s=null){K(o)||(o=we({},o)),s!=null&&!pe(s)&&(s=null);const r=zi(),i=new WeakSet,l=[];let a=!1;const u=r.app={_uid:td++,_component:o,_props:s,_container:null,_context:r,_instance:null,version:$d,get config(){return r.config},set config(f){},use(f,...c){return i.has(f)||(f&&K(f.install)?(i.add(f),f.install(u,...c)):K(f)&&(i.add(f),f(u,...c))),u},mixin(f){return r.mixins.includes(f)||r.mixins.push(f),u},component(f,c){return c?(r.components[f]=c,u):r.components[f]},directive(f,c){return c?(r.directives[f]=c,u):r.directives[f]},mount(f,c,h){if(!a){const d=u._ceVNode||Te(o,s);return d.appContext=r,h===!0?h="svg":h===!1&&(h=void 0),e(d,f,h),a=!0,u._container=f,f.__vue_app__=u,$o(d.component)}},onUnmount(f){l.push(f)},unmount(){a&&(Ze(l,u._instance,16),e(null,u._container),delete u._container.__vue_app__)},provide(f,c){return r.provides[f]=c,u},runWithContext(f){const c=Wt;Wt=u;try{return f()}finally{Wt=c}}};return u}}let Wt=null;function od(e,t){if(ge){let n=ge.provides;const o=ge.parent&&ge.parent.provides;o===n&&(n=ge.provides=Object.create(o)),n[e]=t}}function Rn(e,t,n=!1){const o=ge||Ee;if(o||Wt){const s=Wt?Wt._context.provides:o?o.parent==null?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:void 0;if(s&&e in s)return s[e];if(arguments.length>1)return n&&K(t)?t.call(o&&o.proxy):t}}function ji(){return!!(ge||Ee||Wt)}const Ki={},Wi=()=>Object.create(Ki),Gi=e=>Object.getPrototypeOf(e)===Ki;function sd(e,t,n,o=!1){const s={},r=Wi();e.propsDefaults=Object.create(null),Yi(e,t,s,r);for(const i in e.propsOptions[0])i in s||(s[i]=void 0);n?e.props=o?s:af(s):e.type.props?e.props=s:e.props=r,e.attrs=r}function rd(e,t,n,o){const{props:s,attrs:r,vnode:{patchFlag:i}}=e,l=ne(s),[a]=e.propsOptions;let u=!1;if((o||i>0)&&!(i&16)){if(i&8){const f=e.vnode.dynamicProps;for(let c=0;c<f.length;c++){let h=f[c];if(Ro(e.emitsOptions,h))continue;const d=t[h];if(a)if(oe(r,h))d!==r[h]&&(r[h]=d,u=!0);else{const _=Ue(h);s[_]=Ks(a,l,_,d,e,!1)}else d!==r[h]&&(r[h]=d,u=!0)}}}else{Yi(e,t,s,r)&&(u=!0);let f;for(const c in l)(!t||!oe(t,c)&&((f=wt(c))===c||!oe(t,f)))&&(a?n&&(n[c]!==void 0||n[f]!==void 0)&&(s[c]=Ks(a,l,c,void 0,e,!0)):delete s[c]);if(r!==l)for(const c in r)(!t||!oe(t,c))&&(delete r[c],u=!0)}u&&mt(e.attrs,"set","")}function Yi(e,t,n,o){const[s,r]=e.propsOptions;let i=!1,l;if(t)for(let a in t){if(En(a))continue;const u=t[a];let f;s&&oe(s,f=Ue(a))?!r||!r.includes(f)?n[f]=u:(l||(l={}))[f]=u:Ro(e.emitsOptions,a)||(!(a in o)||u!==o[a])&&(o[a]=u,i=!0)}if(r){const a=ne(n),u=l||le;for(let f=0;f<r.length;f++){const c=r[f];n[c]=Ks(s,a,c,u[c],e,!oe(u,c))}}return i}function Ks(e,t,n,o,s,r){const i=e[n];if(i!=null){const l=oe(i,"default");if(l&&o===void 0){const a=i.default;if(i.type!==Function&&!i.skipFactory&&K(a)){const{propsDefaults:u}=s;if(n in u)o=u[n];else{const f=$n(s);o=u[n]=a.call(null,t),f()}}else o=a;s.ce&&s.ce._setProp(n,o)}i[0]&&(r&&!l?o=!1:i[1]&&(o===""||o===wt(n))&&(o=!0))}return o}const id=new WeakMap;function qi(e,t,n=!1){const o=n?id:t.propsCache,s=o.get(e);if(s)return s;const r=e.props,i={},l=[];let a=!1;if(!K(e)){const f=c=>{a=!0;const[h,d]=qi(c,t,!0);we(i,h),d&&l.push(...d)};!n&&t.mixins.length&&t.mixins.forEach(f),e.extends&&f(e.extends),e.mixins&&e.mixins.forEach(f)}if(!r&&!a)return pe(e)&&o.set(e,nn),nn;if(Y(r))for(let f=0;f<r.length;f++){const c=Ue(r[f]);Xi(c)&&(i[c]=le)}else if(r)for(const f in r){const c=Ue(f);if(Xi(c)){const h=r[f],d=i[c]=Y(h)||K(h)?{type:h}:we({},h),_=d.type;let v=!1,g=!0;if(Y(_))for(let y=0;y<_.length;++y){const T=_[y],P=K(T)&&T.name;if(P==="Boolean"){v=!0;break}else P==="String"&&(g=!1)}else v=K(_)&&_.name==="Boolean";d[0]=v,d[1]=g,(v||oe(d,"default"))&&l.push(c)}}const u=[i,l];return pe(e)&&o.set(e,u),u}function Xi(e){return e[0]!=="$"&&!En(e)}const Zi=e=>e[0]==="_"||e==="$stable",Ws=e=>Y(e)?e.map(tt):[tt(e)],ld=(e,t,n)=>{if(t._n)return t;const o=Co((...s)=>(Tt.NODE_ENV!=="production"&&ge&&(!n||(n.root,ge.root)),Ws(t(...s))),n);return o._c=!1,o},Ji=(e,t,n)=>{const o=e._ctx;for(const s in e){if(Zi(s))continue;const r=e[s];if(K(r))t[s]=ld(s,r,o);else if(r!=null){const i=Ws(r);t[s]=()=>i}}},Qi=(e,t)=>{const n=Ws(t);e.slots.default=()=>n},el=(e,t,n)=>{for(const o in t)(n||o!=="_")&&(e[o]=t[o])},ad=(e,t,n)=>{const o=e.slots=Wi();if(e.vnode.shapeFlag&32){const s=t._;s?(el(o,t,n),n&&ei(o,"_",s,!0)):Ji(t,o)}else t&&Qi(e,t)},ud=(e,t,n)=>{const{vnode:o,slots:s}=e;let r=!0,i=le;if(o.shapeFlag&32){const l=t._;l?n&&l===1?r=!1:el(s,t,n):(r=!t.$stable,Ji(t,s)),i=t}else t&&(Qi(e,t),i={default:1});if(r)for(const l in s)!Zi(l)&&i[l]==null&&delete s[l]},Ve=Sd;function cd(e){return fd(e)}function fd(e,t){const n=po();n.__VUE__=!0;const{insert:o,remove:s,patchProp:r,createElement:i,createText:l,createComment:a,setText:u,setElementText:f,parentNode:c,nextSibling:h,setScopeId:d=je,insertStaticContent:_}=e,v=(p,m,E,A=null,S=null,x=null,V=void 0,R=null,k=!!m.dynamicChildren)=>{if(p===m)return;p&&!Mn(p,m)&&(A=vs(p),_e(p,S,x,!0),p=null),m.patchFlag===-2&&(k=!1,m.dynamicChildren=null);const{type:C,ref:H,shapeFlag:M}=m;switch(C){case No:g(p,m,E,A);break;case Ot:y(p,m,E,A);break;case Xs:p==null&&T(m,E,A,V);break;case Pe:$(p,m,E,A,S,x,V,R,k);break;default:M&1?I(p,m,E,A,S,x,V,R,k):M&6?j(p,m,E,A,S,x,V,R,k):(M&64||M&128)&&C.process(p,m,E,A,S,x,V,R,k,io)}H!=null&&S&&Do(H,p&&p.ref,x,m||p,!m)},g=(p,m,E,A)=>{if(p==null)o(m.el=l(m.children),E,A);else{const S=m.el=p.el;m.children!==p.children&&u(S,m.children)}},y=(p,m,E,A)=>{p==null?o(m.el=a(m.children||""),E,A):m.el=p.el},T=(p,m,E,A)=>{[p.el,p.anchor]=_(p.children,m,E,A,p.el,p.anchor)},P=({el:p,anchor:m},E,A)=>{let S;for(;p&&p!==m;)S=h(p),o(p,E,A),p=S;o(m,E,A)},b=({el:p,anchor:m})=>{let E;for(;p&&p!==m;)E=h(p),s(p),p=E;s(m)},I=(p,m,E,A,S,x,V,R,k)=>{m.type==="svg"?V="svg":m.type==="math"&&(V="mathml"),p==null?F(m,E,A,S,x,V,R,k):G(p,m,S,x,V,R,k)},F=(p,m,E,A,S,x,V,R)=>{let k,C;const{props:H,shapeFlag:M,transition:B,dirs:W}=p;if(k=p.el=i(p.type,x,H&&H.is,H),M&8?f(k,p.children):M&16&&z(p.children,k,null,A,S,Gs(p,x),V,R),W&&Kt(p,null,A,"created"),U(k,p,p.scopeId,V,A),H){for(const fe in H)fe!=="value"&&!En(fe)&&r(k,fe,null,H[fe],x,A);"value"in H&&r(k,"value",null,H.value,x),(C=H.onVnodeBeforeMount)&&nt(C,A,p)}W&&Kt(p,null,A,"beforeMount");const te=dd(S,B);te&&B.beforeEnter(k),o(k,m,E),((C=H&&H.onVnodeMounted)||te||W)&&Ve(()=>{C&&nt(C,A,p),te&&B.enter(k),W&&Kt(p,null,A,"mounted")},S)},U=(p,m,E,A,S)=>{if(E&&d(p,E),A)for(let x=0;x<A.length;x++)d(p,A[x]);if(S){let x=S.subTree;if(m===x||al(x.type)&&(x.ssContent===m||x.ssFallback===m)){const V=S.vnode;U(p,V,V.scopeId,V.slotScopeIds,S.parent)}}},z=(p,m,E,A,S,x,V,R,k=0)=>{for(let C=k;C<p.length;C++){const H=p[C]=R?Ct(p[C]):tt(p[C]);v(null,H,m,E,A,S,x,V,R)}},G=(p,m,E,A,S,x,V)=>{const R=m.el=p.el;let{patchFlag:k,dynamicChildren:C,dirs:H}=m;k|=p.patchFlag&16;const M=p.props||le,B=m.props||le;let W;if(E&&Gt(E,!1),(W=B.onVnodeBeforeUpdate)&&nt(W,E,m,p),H&&Kt(m,p,E,"beforeUpdate"),E&&Gt(E,!0),(M.innerHTML&&B.innerHTML==null||M.textContent&&B.textContent==null)&&f(R,""),C?D(p.dynamicChildren,C,R,E,A,Gs(m,S),x):V||Z(p,m,R,null,E,A,Gs(m,S),x,!1),k>0){if(k&16)N(R,M,B,E,S);else if(k&2&&M.class!==B.class&&r(R,"class",null,B.class,S),k&4&&r(R,"style",M.style,B.style,S),k&8){const te=m.dynamicProps;for(let fe=0;fe<te.length;fe++){const re=te[fe],$e=M[re],Re=B[re];(Re!==$e||re==="value")&&r(R,re,$e,Re,S,E)}}k&1&&p.children!==m.children&&f(R,m.children)}else!V&&C==null&&N(R,M,B,E,S);((W=B.onVnodeUpdated)||H)&&Ve(()=>{W&&nt(W,E,m,p),H&&Kt(m,p,E,"updated")},A)},D=(p,m,E,A,S,x,V)=>{for(let R=0;R<m.length;R++){const k=p[R],C=m[R],H=k.el&&(k.type===Pe||!Mn(k,C)||k.shapeFlag&70)?c(k.el):E;v(k,C,H,null,A,S,x,V,!0)}},N=(p,m,E,A,S)=>{if(m!==E){if(m!==le)for(const x in m)!En(x)&&!(x in E)&&r(p,x,m[x],null,S,A);for(const x in E){if(En(x))continue;const V=E[x],R=m[x];V!==R&&x!=="value"&&r(p,x,R,V,S,A)}"value"in E&&r(p,"value",m.value,E.value,S)}},$=(p,m,E,A,S,x,V,R,k)=>{const C=m.el=p?p.el:l(""),H=m.anchor=p?p.anchor:l("");let{patchFlag:M,dynamicChildren:B,slotScopeIds:W}=m;W&&(R=R?R.concat(W):W),p==null?(o(C,E,A),o(H,E,A),z(m.children||[],E,H,S,x,V,R,k)):M>0&&M&64&&B&&p.dynamicChildren?(D(p.dynamicChildren,B,E,S,x,V,R),(m.key!=null||S&&m===S.subTree)&&tl(p,m,!0)):Z(p,m,E,H,S,x,V,R,k)},j=(p,m,E,A,S,x,V,R,k)=>{m.slotScopeIds=R,p==null?m.shapeFlag&512?S.ctx.activate(m,E,A,V,k):J(m,E,A,S,x,V,k):ve(p,m,k)},J=(p,m,E,A,S,x,V)=>{const R=p.component=Dd(p,A,S);if(ki(p)&&(R.ctx.renderer=io),Pd(R,!1,V),R.asyncDep){if(S&&S.registerDep(R,X,V),!p.el){const k=R.subTree=Te(Ot);y(null,k,m,E)}}else X(R,p,m,E,S,x,V)},ve=(p,m,E)=>{const A=m.component=p.component;if(bd(p,m,E))if(A.asyncDep&&!A.asyncResolved){L(A,m,E);return}else A.next=m,A.update();else m.el=p.el,A.vnode=m},X=(p,m,E,A,S,x,V)=>{const R=()=>{if(p.isMounted){let{next:M,bu:B,u:W,parent:te,vnode:fe}=p;{const ft=nl(p);if(ft){M&&(M.el=fe.el,L(p,M,V)),ft.asyncDep.then(()=>{p.isUnmounted||R()});return}}let re=M,$e;Gt(p,!1),M?(M.el=fe.el,L(p,M,V)):M=fe,B&&Ts(B),($e=M.props&&M.props.onVnodeBeforeUpdate)&&nt($e,te,M,fe),Gt(p,!0);const Re=il(p),ct=p.subTree;p.subTree=Re,v(ct,Re,c(ct.el),vs(ct),p,S,x),M.el=Re.el,re===null&&wd(p,Re.el),W&&Ve(W,S),($e=M.props&&M.props.onVnodeUpdated)&&Ve(()=>nt($e,te,M,fe),S)}else{let M;const{el:B,props:W}=m,{bm:te,m:fe,parent:re,root:$e,type:Re}=p,ct=cn(m);Gt(p,!1),te&&Ts(te),!ct&&(M=W&&W.onVnodeBeforeMount)&&nt(M,re,m),Gt(p,!0);{$e.ce&&$e.ce._injectChildStyle(Re);const ft=p.subTree=il(p);v(null,ft,E,A,p,S,x),m.el=ft.el}if(fe&&Ve(fe,S),!ct&&(M=W&&W.onVnodeMounted)){const ft=m;Ve(()=>nt(M,re,ft),S)}(m.shapeFlag&256||re&&cn(re.vnode)&&re.vnode.shapeFlag&256)&&p.a&&Ve(p.a,S),p.isMounted=!0,m=E=A=null}};p.scope.on();const k=p.effect=new si(R);p.scope.off();const C=p.update=k.run.bind(k),H=p.job=k.runIfDirty.bind(k);H.i=p,H.id=p.uid,k.scheduler=()=>$s(H),Gt(p,!0),C()},L=(p,m,E)=>{m.component=p;const A=p.vnode.props;p.vnode=m,p.next=null,rd(p,m.props,A,E),ud(p,m.children,E),ht(),Ci(p),_t()},Z=(p,m,E,A,S,x,V,R,k=!1)=>{const C=p&&p.children,H=p?p.shapeFlag:0,M=m.children,{patchFlag:B,shapeFlag:W}=m;if(B>0){if(B&128){Me(C,M,E,A,S,x,V,R,k);return}else if(B&256){ae(C,M,E,A,S,x,V,R,k);return}}W&8?(H&16&&ro(C,S,x),M!==C&&f(E,M)):H&16?W&16?Me(C,M,E,A,S,x,V,R,k):ro(C,S,x,!0):(H&8&&f(E,""),W&16&&z(M,E,A,S,x,V,R,k))},ae=(p,m,E,A,S,x,V,R,k)=>{p=p||nn,m=m||nn;const C=p.length,H=m.length,M=Math.min(C,H);let B;for(B=0;B<M;B++){const W=m[B]=k?Ct(m[B]):tt(m[B]);v(p[B],W,E,null,S,x,V,R,k)}C>H?ro(p,S,x,!0,!1,M):z(m,E,A,S,x,V,R,k,M)},Me=(p,m,E,A,S,x,V,R,k)=>{let C=0;const H=m.length;let M=p.length-1,B=H-1;for(;C<=M&&C<=B;){const W=p[C],te=m[C]=k?Ct(m[C]):tt(m[C]);if(Mn(W,te))v(W,te,E,null,S,x,V,R,k);else break;C++}for(;C<=M&&C<=B;){const W=p[M],te=m[B]=k?Ct(m[B]):tt(m[B]);if(Mn(W,te))v(W,te,E,null,S,x,V,R,k);else break;M--,B--}if(C>M){if(C<=B){const W=B+1,te=W<H?m[W].el:A;for(;C<=B;)v(null,m[C]=k?Ct(m[C]):tt(m[C]),E,te,S,x,V,R,k),C++}}else if(C>B)for(;C<=M;)_e(p[C],S,x,!0),C++;else{const W=C,te=C,fe=new Map;for(C=te;C<=B;C++){const Fe=m[C]=k?Ct(m[C]):tt(m[C]);Fe.key!=null&&fe.set(Fe.key,C)}let re,$e=0;const Re=B-te+1;let ct=!1,ft=0;const lo=new Array(Re);for(C=0;C<Re;C++)lo[C]=0;for(C=W;C<=M;C++){const Fe=p[C];if($e>=Re){_e(Fe,S,x,!0);continue}let dt;if(Fe.key!=null)dt=fe.get(Fe.key);else for(re=te;re<=B;re++)if(lo[re-te]===0&&Mn(Fe,m[re])){dt=re;break}dt===void 0?_e(Fe,S,x,!0):(lo[dt-te]=C+1,dt>=ft?ft=dt:ct=!0,v(Fe,m[dt],E,null,S,x,V,R,k),$e++)}const wc=ct?pd(lo):nn;for(re=wc.length-1,C=Re-1;C>=0;C--){const Fe=te+C,dt=m[Fe],Sc=Fe+1<H?m[Fe+1].el:A;lo[C]===0?v(null,dt,E,Sc,S,x,V,R,k):ct&&(re<0||C!==wc[re]?xe(dt,E,Sc,2):re--)}}},xe=(p,m,E,A,S=null)=>{const{el:x,type:V,transition:R,children:k,shapeFlag:C}=p;if(C&6){xe(p.component.subTree,m,E,A);return}if(C&128){p.suspense.move(m,E,A);return}if(C&64){V.move(p,m,E,io);return}if(V===Pe){o(x,m,E);for(let M=0;M<k.length;M++)xe(k[M],m,E,A);o(p.anchor,m,E);return}if(V===Xs){P(p,m,E);return}if(A!==2&&C&1&&R)if(A===0)R.beforeEnter(x),o(x,m,E),Ve(()=>R.enter(x),S);else{const{leave:M,delayLeave:B,afterLeave:W}=R,te=()=>o(x,m,E),fe=()=>{M(x,()=>{te(),W&&W()})};B?B(x,te,fe):fe()}else o(x,m,E)},_e=(p,m,E,A=!1,S=!1)=>{const{type:x,props:V,ref:R,children:k,dynamicChildren:C,shapeFlag:H,patchFlag:M,dirs:B,cacheIndex:W}=p;if(M===-2&&(S=!1),R!=null&&Do(R,null,E,p,!0),W!=null&&(m.renderCache[W]=void 0),H&256){m.ctx.deactivate(p);return}const te=H&1&&B,fe=!cn(p);let re;if(fe&&(re=V&&V.onVnodeBeforeUnmount)&&nt(re,m,p),H&6)so(p.component,E,A);else{if(H&128){p.suspense.unmount(E,A);return}te&&Kt(p,null,m,"beforeUnmount"),H&64?p.type.remove(p,m,E,io,A):C&&!C.hasOnce&&(x!==Pe||M>0&&M&64)?ro(C,m,E,!1,!0):(x===Pe&&M&384||!S&&H&16)&&ro(k,m,E),A&&Ut(p)}(fe&&(re=V&&V.onVnodeUnmounted)||te)&&Ve(()=>{re&&nt(re,m,p),te&&Kt(p,null,m,"unmounted")},E)},Ut=p=>{const{type:m,el:E,anchor:A,transition:S}=p;if(m===Pe){gs(E,A);return}if(m===Xs){b(p);return}const x=()=>{s(E),S&&!S.persisted&&S.afterLeave&&S.afterLeave()};if(p.shapeFlag&1&&S&&!S.persisted){const{leave:V,delayLeave:R}=S,k=()=>V(E,x);R?R(p.el,x,k):k()}else x()},gs=(p,m)=>{let E;for(;p!==m;)E=h(p),s(p),p=E;s(m)},so=(p,m,E)=>{const{bum:A,scope:S,job:x,subTree:V,um:R,m:k,a:C}=p;ol(k),ol(C),A&&Ts(A),S.stop(),x&&(x.flags|=8,_e(V,p,m,E)),R&&Ve(R,m),Ve(()=>{p.isUnmounted=!0},m),m&&m.pendingBranch&&!m.isUnmounted&&p.asyncDep&&!p.asyncResolved&&p.suspenseId===m.pendingId&&(m.deps--,m.deps===0&&m.resolve())},ro=(p,m,E,A=!1,S=!1,x=0)=>{for(let V=x;V<p.length;V++)_e(p[V],m,E,A,S)},vs=p=>{if(p.shapeFlag&6)return vs(p.component.subTree);if(p.shapeFlag&128)return p.suspense.next();const m=h(p.anchor||p.el),E=m&&m[Rf];return E?h(E):m};let Jr=!1;const bc=(p,m,E)=>{p==null?m._vnode&&_e(m._vnode,null,null,!0):v(m._vnode||null,p,m,null,null,null,E),m._vnode=p,Jr||(Jr=!0,Ci(),Di(),Jr=!1)},io={p:v,um:_e,m:xe,r:Ut,mt:J,mc:z,pc:Z,pbc:D,n:vs,o:e};return{render:bc,hydrate:void 0,createApp:nd(bc)}}function Gs({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Gt({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function dd(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function tl(e,t,n=!1){const o=e.children,s=t.children;if(Y(o)&&Y(s))for(let r=0;r<o.length;r++){const i=o[r];let l=s[r];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=s[r]=Ct(s[r]),l.el=i.el),!n&&l.patchFlag!==-2&&tl(i,l)),l.type===No&&(l.el=i.el)}}function pd(e){const t=e.slice(),n=[0];let o,s,r,i,l;const a=e.length;for(o=0;o<a;o++){const u=e[o];if(u!==0){if(s=n[n.length-1],e[s]<u){t[o]=s,n.push(o);continue}for(r=0,i=n.length-1;r<i;)l=r+i>>1,e[n[l]]<u?r=l+1:i=l;u<e[n[r]]&&(r>0&&(t[o]=n[r-1]),n[r]=o)}}for(r=n.length,i=n[r-1];r-- >0;)n[r]=i,i=t[i];return n}function nl(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:nl(t)}function ol(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const hd=Symbol.for("v-scx"),_d=()=>Rn(hd);function Ys(e,t){return qs(e,null,t)}function et(e,t,n){return qs(e,t,n)}function qs(e,t,n=le){const{immediate:o,deep:s,flush:r,once:i}=n,l=we({},n),a=t&&o||!t&&r!=="post";let u;if(Fn){if(r==="sync"){const d=_d();u=d.__watcherHandles||(d.__watcherHandles=[])}else if(!a){const d=()=>{};return d.stop=je,d.resume=je,d.pause=je,d}}const f=ge;l.call=(d,_,v)=>Ze(d,f,_,v);let c=!1;r==="post"?l.scheduler=d=>{Ve(d,f&&f.suspense)}:r!=="sync"&&(c=!0,l.scheduler=(d,_)=>{_?d():$s(d)}),l.augmentJob=d=>{t&&(d.flags|=4),c&&(d.flags|=2,f&&(d.id=f.uid,d.i=f))};const h=bf(e,t,l);return Fn&&(u?u.push(h):a&&h()),h}function md(e,t,n){const o=this.proxy,s=me(e)?e.includes(".")?sl(o,e):()=>o[e]:e.bind(o,o);let r;K(t)?r=t:(r=t.handler,n=t);const i=$n(this),l=qs(s,r.bind(o),n);return i(),l}function sl(e,t){const n=t.split(".");return()=>{let o=e;for(let s=0;s<n.length&&o;s++)o=o[n[s]];return o}}const gd=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Ue(t)}Modifiers`]||e[`${wt(t)}Modifiers`];function vd(e,t,...n){if(e.isUnmounted)return;const o=e.vnode.props||le;let s=n;const r=t.startsWith("update:"),i=r&&gd(o,t.slice(7));i&&(i.trim&&(s=n.map(f=>me(f)?f.trim():f)),i.number&&(s=n.map(kc)));let l,a=o[l=Ss(t)]||o[l=Ss(Ue(t))];!a&&r&&(a=o[l=Ss(wt(t))]),a&&Ze(a,e,6,s);const u=o[l+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,Ze(u,e,6,s)}}function rl(e,t,n=!1){const o=t.emitsCache,s=o.get(e);if(s!==void 0)return s;const r=e.emits;let i={},l=!1;if(!K(e)){const a=u=>{const f=rl(u,t,!0);f&&(l=!0,we(i,f))};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}return!r&&!l?(pe(e)&&o.set(e,null),null):(Y(r)?r.forEach(a=>i[a]=null):we(i,r),pe(e)&&o.set(e,i),i)}function Ro(e,t){return!e||!ao(t)?!1:(t=t.slice(2).replace(/Once$/,""),oe(e,t[0].toLowerCase()+t.slice(1))||oe(e,wt(t))||oe(e,t))}function zg(){}function il(e){const{type:t,vnode:n,proxy:o,withProxy:s,propsOptions:[r],slots:i,attrs:l,emit:a,render:u,renderCache:f,props:c,data:h,setupState:d,ctx:_,inheritAttrs:v}=e,g=Ao(e);let y,T;try{if(n.shapeFlag&4){const b=s||o,I=Tt.NODE_ENV!=="production"&&d.__isScriptSetup?new Proxy(b,{get(F,U,z){return wf(`Property '${String(U)}' was accessed via 'this'. Avoid using 'this' in templates.`),Reflect.get(F,U,z)}}):b;y=tt(u.call(I,b,f,Tt.NODE_ENV!=="production"?yo(c):c,d,h,_)),T=l}else{const b=t;Tt.NODE_ENV,y=tt(b.length>1?b(Tt.NODE_ENV!=="production"?yo(c):c,Tt.NODE_ENV!=="production"?{get attrs(){return yo(l)},slots:i,emit:a}:{attrs:l,slots:i,emit:a}):b(Tt.NODE_ENV!=="production"?yo(c):c,null)),T=t.props?l:yd(l)}}catch(b){Nn.length=0,So(b,e,1),y=Te(Ot)}let P=y;if(T&&v!==!1){const b=Object.keys(T),{shapeFlag:I}=P;b.length&&I&7&&(r&&b.some(Es)&&(T=Ed(T,r)),P=dn(P,T,!1,!0))}return n.dirs&&(P=dn(P,null,!1,!0),P.dirs=P.dirs?P.dirs.concat(n.dirs):n.dirs),n.transition&&Fs(P,n.transition),y=P,Ao(g),y}const yd=e=>{let t;for(const n in e)(n==="class"||n==="style"||ao(n))&&((t||(t={}))[n]=e[n]);return t},Ed=(e,t)=>{const n={};for(const o in e)(!Es(o)||!(o.slice(9)in t))&&(n[o]=e[o]);return n};function bd(e,t,n){const{props:o,children:s,component:r}=e,{props:i,children:l,patchFlag:a}=t,u=r.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&a>=0){if(a&1024)return!0;if(a&16)return o?ll(o,i,u):!!i;if(a&8){const f=t.dynamicProps;for(let c=0;c<f.length;c++){const h=f[c];if(i[h]!==o[h]&&!Ro(u,h))return!0}}}else return(s||l)&&(!l||!l.$stable)?!0:o===i?!1:o?i?ll(o,i,u):!0:!!i;return!1}function ll(e,t,n){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let s=0;s<o.length;s++){const r=o[s];if(t[r]!==e[r]&&!Ro(n,r))return!0}return!1}function wd({vnode:e,parent:t},n){for(;t;){const o=t.subTree;if(o.suspense&&o.suspense.activeBranch===e&&(o.el=e.el),o===e)(e=t.vnode).el=n,t=t.parent;else break}}const al=e=>e.__isSuspense;function Sd(e,t){t&&t.pendingBranch?Y(e)?t.effects.push(...e):t.effects.push(e):Df(e)}const Pe=Symbol.for("v-fgt"),No=Symbol.for("v-txt"),Ot=Symbol.for("v-cmt"),Xs=Symbol.for("v-stc"),Nn=[];let Le=null;function Ie(e=!1){Nn.push(Le=e?null:[])}function Td(){Nn.pop(),Le=Nn[Nn.length-1]||null}let Vn=1;function ul(e,t=!1){Vn+=e,e<0&&Le&&t&&(Le.hasOnce=!0)}function cl(e){return e.dynamicChildren=Vn>0?Le||nn:null,Td(),Vn>0&&Le&&Le.push(e),e}function At(e,t,n,o,s,r){return cl(ie(e,t,n,o,s,r,!0))}function fn(e,t,n,o,s){return cl(Te(e,t,n,o,s,!0))}function Ln(e){return e?e.__v_isVNode===!0:!1}function Mn(e,t){return e.type===t.type&&e.key===t.key}const fl=({key:e})=>e??null,Vo=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?me(e)||ye(e)||K(e)?{i:Ee,r:e,k:t,f:!!n}:e:null);function ie(e,t=null,n=null,o=0,s=null,r=e===Pe?0:1,i=!1,l=!1){const a={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&fl(t),ref:t&&Vo(t),scopeId:Oo,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:r,patchFlag:o,dynamicProps:s,dynamicChildren:null,appContext:null,ctx:Ee};return l?(Zs(a,n),r&128&&e.normalize(a)):n&&(a.shapeFlag|=me(n)?8:16),Vn>0&&!i&&Le&&(a.patchFlag>0||r&6)&&a.patchFlag!==32&&Le.push(a),a}const Te=xd;function xd(e,t=null,n=null,o=0,s=null,r=!1){if((!e||e===Gf)&&(e=Ot),Ln(e)){const l=dn(e,t,!0);return n&&Zs(l,n),Vn>0&&!r&&Le&&(l.shapeFlag&6?Le[Le.indexOf(e)]=l:Le.push(l)),l.patchFlag=-2,l}if(Ld(e)&&(e=e.__vccOpts),t){t=dl(t);let{class:l,style:a}=t;l&&!me(l)&&(t.class=pt(l)),pe(a)&&(Vs(a)&&!Y(a)&&(a=we({},a)),t.style=Ne(a))}const i=me(e)?1:al(e)?128:Nf(e)?64:pe(e)?4:K(e)?2:0;return ie(e,t,n,o,s,i,r,!0)}function dl(e){return e?Vs(e)||Gi(e)?we({},e):e:null}function dn(e,t,n=!1,o=!1){const{props:s,ref:r,patchFlag:i,children:l,transition:a}=e,u=t?pl(s||{},t):s,f={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&fl(u),ref:t&&t.ref?n&&r?Y(r)?r.concat(Vo(t)):[r,Vo(t)]:Vo(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Pe?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:a,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&dn(e.ssContent),ssFallback:e.ssFallback&&dn(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return a&&o&&Fs(f,a.clone(f)),f}function Od(e=" ",t=0){return Te(No,null,e,t)}function Lo(e="",t=!1){return t?(Ie(),fn(Ot,null,e)):Te(Ot,null,e)}function tt(e){return e==null||typeof e=="boolean"?Te(Ot):Y(e)?Te(Pe,null,e.slice()):Ln(e)?Ct(e):Te(No,null,String(e))}function Ct(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:dn(e)}function Zs(e,t){let n=0;const{shapeFlag:o}=e;if(t==null)t=null;else if(Y(t))n=16;else if(typeof t=="object")if(o&65){const s=t.default;s&&(s._c&&(s._d=!1),Zs(e,s()),s._c&&(s._d=!0));return}else{n=32;const s=t._;!s&&!Gi(t)?t._ctx=Ee:s===3&&Ee&&(Ee.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else K(t)?(t={default:t,_ctx:Ee},n=32):(t=String(t),o&64?(n=16,t=[Od(t)]):n=8);e.children=t,e.shapeFlag|=n}function pl(...e){const t={};for(let n=0;n<e.length;n++){const o=e[n];for(const s in o)if(s==="class")t.class!==o.class&&(t.class=pt([t.class,o.class]));else if(s==="style")t.style=Ne([t.style,o.style]);else if(ao(s)){const r=t[s],i=o[s];i&&r!==i&&!(Y(r)&&r.includes(i))&&(t[s]=r?[].concat(r,i):i)}else s!==""&&(t[s]=o[s])}return t}function nt(e,t,n,o=null){Ze(e,t,7,[n,o])}const Ad=zi();let Cd=0;function Dd(e,t,n){const o=e.type,s=(t?t.appContext:e.appContext)||Ad,r={uid:Cd++,vnode:e,type:o,parent:t,appContext:s,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Uc(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(s.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:qi(o,s),emitsOptions:rl(o,s),emit:null,emitted:null,propsDefaults:le,inheritAttrs:o.inheritAttrs,ctx:le,data:le,props:le,attrs:le,slots:le,refs:le,setupState:le,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return r.ctx={_:r},r.root=t?t.root:r,r.emit=vd.bind(null,r),e.ce&&e.ce(r),r}let ge=null;const Js=()=>ge||Ee;let Mo,Qs;{const e=po(),t=(n,o)=>{let s;return(s=e[n])||(s=e[n]=[]),s.push(o),r=>{s.length>1?s.forEach(i=>i(r)):s[0](r)}};Mo=t("__VUE_INSTANCE_SETTERS__",n=>ge=n),Qs=t("__VUE_SSR_SETTERS__",n=>Fn=n)}const $n=e=>{const t=ge;return Mo(e),e.scope.on(),()=>{e.scope.off(),Mo(t)}},hl=()=>{ge&&ge.scope.off(),Mo(null)};function _l(e){return e.vnode.shapeFlag&4}let Fn=!1;function Pd(e,t=!1,n=!1){t&&Qs(t);const{props:o,children:s}=e.vnode,r=_l(e);sd(e,o,r,t),ad(e,s,n);const i=r?Id(e,t):void 0;return t&&Qs(!1),i}function Id(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,qf);const{setup:o}=n;if(o){ht();const s=e.setupContext=o.length>1?Rd(e):null,r=$n(e),i=rn(o,e,0,[e.props,s]),l=Qr(i);if(_t(),r(),(l||e.sp)&&!cn(e)&&Ii(e),l){if(i.then(hl,hl),t)return i.then(a=>{ml(e,a)}).catch(a=>{So(a,e,0)});e.asyncDep=i}else ml(e,i)}else gl(e)}function ml(e,t,n){K(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:pe(t)&&(e.setupState=Ti(t)),gl(e)}function gl(e,t,n){const o=e.type;e.render||(e.render=o.render||je);{const s=$n(e);ht();try{Xf(e)}finally{_t(),s()}}}const kd={get(e,t){return Se(e,"get",""),e[t]}};function Rd(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,kd),slots:e.slots,emit:e.emit,expose:t}}function $o(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Ti(uf(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in In)return In[n](e)},has(t,n){return n in t||n in In}})):e.proxy}const Nd=/(?:^|[-_])(\w)/g,Vd=e=>e.replace(Nd,t=>t.toUpperCase()).replace(/[-_]/g,"");function vl(e,t=!0){return K(e)?e.displayName||e.name:e.name||t&&e.__name}function yl(e,t,n=!1){let o=vl(t);if(!o&&t.__file){const s=t.__file.match(/([^/\\]+)\.\w+$/);s&&(o=s[1])}if(!o&&e&&e.parent){const s=r=>{for(const i in r)if(r[i]===t)return i};o=s(e.components||e.parent.type.components)||s(e.appContext.components)}return o?Vd(o):n?"App":"Anonymous"}function Ld(e){return K(e)&&"__vccOpts"in e}const be=(e,t)=>yf(e,t,Fn);function Md(e,t,n){const o=arguments.length;return o===2?pe(t)&&!Y(t)?Ln(t)?Te(e,null,[t]):Te(e,t):Te(e,null,t):(o>3?n=Array.prototype.slice.call(arguments,2):o===3&&Ln(n)&&(n=[n]),Te(e,t,n))}const $d="3.5.13";let er;const El=typeof window<"u"&&window.trustedTypes;if(El)try{er=El.createPolicy("vue",{createHTML:e=>e})}catch{}const bl=er?e=>er.createHTML(e):e=>e,Fd="http://www.w3.org/2000/svg",Ud="http://www.w3.org/1998/Math/MathML",Et=typeof document<"u"?document:null,wl=Et&&Et.createElement("template"),Bd={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,o)=>{const s=t==="svg"?Et.createElementNS(Fd,e):t==="mathml"?Et.createElementNS(Ud,e):n?Et.createElement(e,{is:n}):Et.createElement(e);return e==="select"&&o&&o.multiple!=null&&s.setAttribute("multiple",o.multiple),s},createText:e=>Et.createTextNode(e),createComment:e=>Et.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Et.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,o,s,r){const i=n?n.previousSibling:t.lastChild;if(s&&(s===r||s.nextSibling))for(;t.insertBefore(s.cloneNode(!0),n),!(s===r||!(s=s.nextSibling)););else{wl.innerHTML=bl(o==="svg"?`<svg>${e}</svg>`:o==="mathml"?`<math>${e}</math>`:e);const l=wl.content;if(o==="svg"||o==="mathml"){const a=l.firstChild;for(;a.firstChild;)l.appendChild(a.firstChild);l.removeChild(a)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Hd=Symbol("_vtc");function zd(e,t,n){const o=e[Hd];o&&(t=(t?[t,...o]:[...o]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Fo=Symbol("_vod"),Sl=Symbol("_vsh"),ot={beforeMount(e,{value:t},{transition:n}){e[Fo]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):Un(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:o}){!t!=!n&&(o?t?(o.beforeEnter(e),Un(e,!0),o.enter(e)):o.leave(e,()=>{Un(e,!1)}):Un(e,t))},beforeUnmount(e,{value:t}){Un(e,t)}};function Un(e,t){e.style.display=t?e[Fo]:"none",e[Sl]=!t}const jd=Symbol(""),Kd=/(^|;)\s*display\s*:/;function Wd(e,t,n){const o=e.style,s=me(n);let r=!1;if(n&&!s){if(t)if(me(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&Uo(o,l,"")}else for(const i in t)n[i]==null&&Uo(o,i,"");for(const i in n)i==="display"&&(r=!0),Uo(o,i,n[i])}else if(s){if(t!==n){const i=o[jd];i&&(n+=";"+i),o.cssText=n,r=Kd.test(n)}}else t&&e.removeAttribute("style");Fo in e&&(e[Fo]=r?o.display:"",e[Sl]&&(o.display="none"))}const Tl=/\s*!important$/;function Uo(e,t,n){if(Y(n))n.forEach(o=>Uo(e,t,o));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const o=Gd(e,t);Tl.test(n)?e.setProperty(wt(o),n.replace(Tl,""),"important"):e[o]=n}}const xl=["Webkit","Moz","ms"],tr={};function Gd(e,t){const n=tr[t];if(n)return n;let o=Ue(t);if(o!=="filter"&&o in e)return tr[t]=o;o=fo(o);for(let s=0;s<xl.length;s++){const r=xl[s]+o;if(r in e)return tr[t]=r}return t}const Ol="http://www.w3.org/1999/xlink";function Al(e,t,n,o,s,r=$c(t)){o&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Ol,t.slice(6,t.length)):e.setAttributeNS(Ol,t,n):n==null||r&&!ni(n)?e.removeAttribute(t):e.setAttribute(t,r?"":Bt(n)?String(n):n)}function Cl(e,t,n,o,s){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?bl(n):n);return}const r=e.tagName;if(t==="value"&&r!=="PROGRESS"&&!r.includes("-")){const l=r==="OPTION"?e.getAttribute("value")||"":e.value,a=n==null?e.type==="checkbox"?"on":"":String(n);(l!==a||!("_value"in e))&&(e.value=a),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=ni(n):n==null&&l==="string"?(n="",i=!0):l==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(s||t)}function Yd(e,t,n,o){e.addEventListener(t,n,o)}function qd(e,t,n,o){e.removeEventListener(t,n,o)}const Dl=Symbol("_vei");function Xd(e,t,n,o,s=null){const r=e[Dl]||(e[Dl]={}),i=r[t];if(o&&i)i.value=o;else{const[l,a]=Zd(t);if(o){const u=r[t]=ep(o,s);Yd(e,l,u,a)}else i&&(qd(e,l,i,a),r[t]=void 0)}}const Pl=/(?:Once|Passive|Capture)$/;function Zd(e){let t;if(Pl.test(e)){t={};let o;for(;o=e.match(Pl);)e=e.slice(0,e.length-o[0].length),t[o[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):wt(e.slice(2)),t]}let nr=0;const Jd=Promise.resolve(),Qd=()=>nr||(Jd.then(()=>nr=0),nr=Date.now());function ep(e,t){const n=o=>{if(!o._vts)o._vts=Date.now();else if(o._vts<=n.attached)return;Ze(tp(o,n.value),t,5,[o])};return n.value=e,n.attached=Qd(),n}function tp(e,t){if(Y(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(o=>s=>!s._stopped&&o&&o(s))}else return t}const Il=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,np=(e,t,n,o,s,r)=>{const i=s==="svg";t==="class"?zd(e,o,i):t==="style"?Wd(e,n,o):ao(t)?Es(t)||Xd(e,t,n,o,r):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):op(e,t,o,i))?(Cl(e,t,o),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Al(e,t,o,i,r,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!me(o))?Cl(e,Ue(t),o,r,t):(t==="true-value"?e._trueValue=o:t==="false-value"&&(e._falseValue=o),Al(e,t,o,i))};function op(e,t,n,o){if(o)return!!(t==="innerHTML"||t==="textContent"||t in e&&Il(t)&&K(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const s=e.tagName;if(s==="IMG"||s==="VIDEO"||s==="CANVAS"||s==="SOURCE")return!1}return Il(t)&&me(n)?!1:t in e}const sp=["ctrl","shift","alt","meta"],rp={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>sp.some(n=>e[`${n}Key`]&&!t.includes(n))},Dt=(e,t)=>{const n=e._withMods||(e._withMods={}),o=t.join(".");return n[o]||(n[o]=(s,...r)=>{for(let i=0;i<t.length;i++){const l=rp[t[i]];if(l&&l(s,t))return}return e(s,...r)})},ip={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},lp=(e,t)=>{const n=e._withKeys||(e._withKeys={}),o=t.join(".");return n[o]||(n[o]=s=>{if(!("key"in s))return;const r=wt(s.key);if(t.some(i=>i===r||ip[i]===r))return e(s)})},ap=we({patchProp:np},Bd);let kl;function up(){return kl||(kl=cd(ap))}const cp=(...e)=>{const t=up().createApp(...e),{mount:n}=t;return t.mount=o=>{const s=dp(o);if(!s)return;const r=t._component;!K(r)&&!r.render&&!r.template&&(r.template=s.innerHTML),s.nodeType===1&&(s.textContent="");const i=n(s,!1,fp(s));return s instanceof Element&&(s.removeAttribute("v-cloak"),s.setAttribute("data-v-app","")),i},t};function fp(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function dp(e){return me(e)?document.querySelector(e):e}var pp=Object.create,Rl=Object.defineProperty,hp=Object.getOwnPropertyDescriptor,or=Object.getOwnPropertyNames,_p=Object.getPrototypeOf,mp=Object.prototype.hasOwnProperty,gp=(e,t)=>function(){return e&&(t=(0,e[or(e)[0]])(e=0)),t},vp=(e,t)=>function(){return t||(0,e[or(e)[0]])((t={exports:{}}).exports,t),t.exports},yp=(e,t,n,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let s of or(t))!mp.call(e,s)&&s!==n&&Rl(e,s,{get:()=>t[s],enumerable:!(o=hp(t,s))||o.enumerable});return e},Ep=(e,t,n)=>(n=e!=null?pp(_p(e)):{},yp(Rl(n,"default",{value:e,enumerable:!0}),e)),Bn=gp({"../../node_modules/.pnpm/tsup@8.3.5_@microsoft+api-extractor@7.48.1_@types+node@22.10.5__jiti@2.4.2_postcss@8.4.49_tsx_s7k37zks4wtn7x2grzma6lrsfa/node_modules/tsup/assets/esm_shims.js"(){}}),bp=vp({"../../node_modules/.pnpm/rfdc@1.4.1/node_modules/rfdc/index.js"(e,t){Bn(),t.exports=o;function n(r){return r instanceof Buffer?Buffer.from(r):new r.constructor(r.buffer.slice(),r.byteOffset,r.length)}function o(r){if(r=r||{},r.circles)return s(r);const i=new Map;if(i.set(Date,c=>new Date(c)),i.set(Map,(c,h)=>new Map(a(Array.from(c),h))),i.set(Set,(c,h)=>new Set(a(Array.from(c),h))),r.constructorHandlers)for(const c of r.constructorHandlers)i.set(c[0],c[1]);let l=null;return r.proto?f:u;function a(c,h){const d=Object.keys(c),_=new Array(d.length);for(let v=0;v<d.length;v++){const g=d[v],y=c[g];typeof y!="object"||y===null?_[g]=y:y.constructor!==Object&&(l=i.get(y.constructor))?_[g]=l(y,h):ArrayBuffer.isView(y)?_[g]=n(y):_[g]=h(y)}return _}function u(c){if(typeof c!="object"||c===null)return c;if(Array.isArray(c))return a(c,u);if(c.constructor!==Object&&(l=i.get(c.constructor)))return l(c,u);const h={};for(const d in c){if(Object.hasOwnProperty.call(c,d)===!1)continue;const _=c[d];typeof _!="object"||_===null?h[d]=_:_.constructor!==Object&&(l=i.get(_.constructor))?h[d]=l(_,u):ArrayBuffer.isView(_)?h[d]=n(_):h[d]=u(_)}return h}function f(c){if(typeof c!="object"||c===null)return c;if(Array.isArray(c))return a(c,f);if(c.constructor!==Object&&(l=i.get(c.constructor)))return l(c,f);const h={};for(const d in c){const _=c[d];typeof _!="object"||_===null?h[d]=_:_.constructor!==Object&&(l=i.get(_.constructor))?h[d]=l(_,f):ArrayBuffer.isView(_)?h[d]=n(_):h[d]=f(_)}return h}}function s(r){const i=[],l=[],a=new Map;if(a.set(Date,d=>new Date(d)),a.set(Map,(d,_)=>new Map(f(Array.from(d),_))),a.set(Set,(d,_)=>new Set(f(Array.from(d),_))),r.constructorHandlers)for(const d of r.constructorHandlers)a.set(d[0],d[1]);let u=null;return r.proto?h:c;function f(d,_){const v=Object.keys(d),g=new Array(v.length);for(let y=0;y<v.length;y++){const T=v[y],P=d[T];if(typeof P!="object"||P===null)g[T]=P;else if(P.constructor!==Object&&(u=a.get(P.constructor)))g[T]=u(P,_);else if(ArrayBuffer.isView(P))g[T]=n(P);else{const b=i.indexOf(P);b!==-1?g[T]=l[b]:g[T]=_(P)}}return g}function c(d){if(typeof d!="object"||d===null)return d;if(Array.isArray(d))return f(d,c);if(d.constructor!==Object&&(u=a.get(d.constructor)))return u(d,c);const _={};i.push(d),l.push(_);for(const v in d){if(Object.hasOwnProperty.call(d,v)===!1)continue;const g=d[v];if(typeof g!="object"||g===null)_[v]=g;else if(g.constructor!==Object&&(u=a.get(g.constructor)))_[v]=u(g,c);else if(ArrayBuffer.isView(g))_[v]=n(g);else{const y=i.indexOf(g);y!==-1?_[v]=l[y]:_[v]=c(g)}}return i.pop(),l.pop(),_}function h(d){if(typeof d!="object"||d===null)return d;if(Array.isArray(d))return f(d,h);if(d.constructor!==Object&&(u=a.get(d.constructor)))return u(d,h);const _={};i.push(d),l.push(_);for(const v in d){const g=d[v];if(typeof g!="object"||g===null)_[v]=g;else if(g.constructor!==Object&&(u=a.get(g.constructor)))_[v]=u(g,h);else if(ArrayBuffer.isView(g))_[v]=n(g);else{const y=i.indexOf(g);y!==-1?_[v]=l[y]:_[v]=h(g)}}return i.pop(),l.pop(),_}}}});Bn(),Bn(),Bn();var Bo=typeof navigator<"u",O=typeof window<"u"?window:typeof globalThis<"u"?globalThis:typeof global<"u"?global:{};typeof O.chrome<"u"&&O.chrome.devtools,Bo&&(O.self,O.top);var Nl;typeof navigator<"u"&&((Nl=navigator.userAgent)==null||Nl.toLowerCase().includes("electron"));var wp=typeof window<"u"&&!!window.__NUXT__;Bn();var Sp=Ep(bp()),Tp=/(?:^|[-_/])(\w)/g,xp=/-(\w)/g,Op=/([a-z0-9])([A-Z])/g;function Vl(e,t){return t?t.toUpperCase():""}function Ll(e){return e&&`${e}`.replace(Tp,Vl)}function Ap(e){return e&&e.replace(xp,Vl)}function Cp(e){return e&&e.replace(Op,(t,n,o)=>`${n}-${o}`).toLowerCase()}function Dp(e,t){let n=e.replace(/^[a-z]:/i,"").replace(/\\/g,"/");n.endsWith(`index${t}`)&&(n=n.replace(`/index${t}`,t));const o=n.lastIndexOf("/"),s=n.substring(o+1);{const r=s.lastIndexOf(t);return s.substring(0,r)}}var Ml=(0,Sp.default)({circles:!0});const Pp={trailing:!0};function Pt(e,t=25,n={}){if(n={...Pp,...n},!Number.isFinite(t))throw new TypeError("Expected `wait` to be a finite number");let o,s,r=[],i,l;const a=(u,f)=>(i=Ip(e,u,f),i.finally(()=>{if(i=null,n.trailing&&l&&!s){const c=a(u,l);return l=null,c}}),i);return function(...u){return i?(n.trailing&&(l=u),i):new Promise(f=>{const c=!s&&n.leading;clearTimeout(s),s=setTimeout(()=>{s=null;const h=n.leading?o:a(this,u);for(const d of r)d(h);r=[]},t),c?(o=a(this,u),f(o)):r.push(f)})}}async function Ip(e,t,n){return await e.apply(t,n)}function sr(e,t={},n){for(const o in e){const s=e[o],r=n?`${n}:${o}`:o;typeof s=="object"&&s!==null?sr(s,t,r):typeof s=="function"&&(t[r]=s)}return t}const kp={run:e=>e()},Rp=()=>kp,$l=typeof console.createTask<"u"?console.createTask:Rp;function Np(e,t){const n=t.shift(),o=$l(n);return e.reduce((s,r)=>s.then(()=>o.run(()=>r(...t))),Promise.resolve())}function Vp(e,t){const n=t.shift(),o=$l(n);return Promise.all(e.map(s=>o.run(()=>s(...t))))}function rr(e,t){for(const n of[...e])n(t)}let Lp=class{constructor(){this._hooks={},this._before=void 0,this._after=void 0,this._deprecatedMessages=void 0,this._deprecatedHooks={},this.hook=this.hook.bind(this),this.callHook=this.callHook.bind(this),this.callHookWith=this.callHookWith.bind(this)}hook(t,n,o={}){if(!t||typeof n!="function")return()=>{};const s=t;let r;for(;this._deprecatedHooks[t];)r=this._deprecatedHooks[t],t=r.to;if(r&&!o.allowDeprecated){let i=r.message;i||(i=`${s} hook has been deprecated`+(r.to?`, please use ${r.to}`:"")),this._deprecatedMessages||(this._deprecatedMessages=new Set),this._deprecatedMessages.has(i)||(console.warn(i),this._deprecatedMessages.add(i))}if(!n.name)try{Object.defineProperty(n,"name",{get:()=>"_"+t.replace(/\W+/g,"_")+"_hook_cb",configurable:!0})}catch{}return this._hooks[t]=this._hooks[t]||[],this._hooks[t].push(n),()=>{n&&(this.removeHook(t,n),n=void 0)}}hookOnce(t,n){let o,s=(...r)=>(typeof o=="function"&&o(),o=void 0,s=void 0,n(...r));return o=this.hook(t,s),o}removeHook(t,n){if(this._hooks[t]){const o=this._hooks[t].indexOf(n);o!==-1&&this._hooks[t].splice(o,1),this._hooks[t].length===0&&delete this._hooks[t]}}deprecateHook(t,n){this._deprecatedHooks[t]=typeof n=="string"?{to:n}:n;const o=this._hooks[t]||[];delete this._hooks[t];for(const s of o)this.hook(t,s)}deprecateHooks(t){Object.assign(this._deprecatedHooks,t);for(const n in t)this.deprecateHook(n,t[n])}addHooks(t){const n=sr(t),o=Object.keys(n).map(s=>this.hook(s,n[s]));return()=>{for(const s of o.splice(0,o.length))s()}}removeHooks(t){const n=sr(t);for(const o in n)this.removeHook(o,n[o])}removeAllHooks(){for(const t in this._hooks)delete this._hooks[t]}callHook(t,...n){return n.unshift(t),this.callHookWith(Np,t,...n)}callHookParallel(t,...n){return n.unshift(t),this.callHookWith(Vp,t,...n)}callHookWith(t,n,...o){const s=this._before||this._after?{name:n,args:o,context:{}}:void 0;this._before&&rr(this._before,s);const r=t(n in this._hooks?[...this._hooks[n]]:[],o);return r instanceof Promise?r.finally(()=>{this._after&&s&&rr(this._after,s)}):(this._after&&s&&rr(this._after,s),r)}beforeEach(t){return this._before=this._before||[],this._before.push(t),()=>{if(this._before!==void 0){const n=this._before.indexOf(t);n!==-1&&this._before.splice(n,1)}}}afterEach(t){return this._after=this._after||[],this._after.push(t),()=>{if(this._after!==void 0){const n=this._after.indexOf(t);n!==-1&&this._after.splice(n,1)}}}};function Fl(){return new Lp}var Mp=Object.create,Ul=Object.defineProperty,$p=Object.getOwnPropertyDescriptor,ir=Object.getOwnPropertyNames,Fp=Object.getPrototypeOf,Up=Object.prototype.hasOwnProperty,Bp=(e,t)=>function(){return e&&(t=(0,e[ir(e)[0]])(e=0)),t},Bl=(e,t)=>function(){return t||(0,e[ir(e)[0]])((t={exports:{}}).exports,t),t.exports},Hp=(e,t,n,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let s of ir(t))!Up.call(e,s)&&s!==n&&Ul(e,s,{get:()=>t[s],enumerable:!(o=$p(t,s))||o.enumerable});return e},zp=(e,t,n)=>(n=e!=null?Mp(Fp(e)):{},Hp(Ul(n,"default",{value:e,enumerable:!0}),e)),w=Bp({"../../node_modules/.pnpm/tsup@8.3.5_@microsoft+api-extractor@7.48.1_@types+node@22.10.5__jiti@2.4.2_postcss@8.4.49_tsx_s7k37zks4wtn7x2grzma6lrsfa/node_modules/tsup/assets/esm_shims.js"(){}}),jp=Bl({"../../node_modules/.pnpm/speakingurl@14.0.1/node_modules/speakingurl/lib/speakingurl.js"(e,t){w(),function(n){var o={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"Ae",Å:"A",Æ:"AE",Ç:"C",È:"E",É:"E",Ê:"E",Ë:"E",Ì:"I",Í:"I",Î:"I",Ï:"I",Ð:"D",Ñ:"N",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"Oe",Ő:"O",Ø:"O",Ù:"U",Ú:"U",Û:"U",Ü:"Ue",Ű:"U",Ý:"Y",Þ:"TH",ß:"ss",à:"a",á:"a",â:"a",ã:"a",ä:"ae",å:"a",æ:"ae",ç:"c",è:"e",é:"e",ê:"e",ë:"e",ì:"i",í:"i",î:"i",ï:"i",ð:"d",ñ:"n",ò:"o",ó:"o",ô:"o",õ:"o",ö:"oe",ő:"o",ø:"o",ù:"u",ú:"u",û:"u",ü:"ue",ű:"u",ý:"y",þ:"th",ÿ:"y","ẞ":"SS",ا:"a",أ:"a",إ:"i",آ:"aa",ؤ:"u",ئ:"e",ء:"a",ب:"b",ت:"t",ث:"th",ج:"j",ح:"h",خ:"kh",د:"d",ذ:"th",ر:"r",ز:"z",س:"s",ش:"sh",ص:"s",ض:"dh",ط:"t",ظ:"z",ع:"a",غ:"gh",ف:"f",ق:"q",ك:"k",ل:"l",م:"m",ن:"n",ه:"h",و:"w",ي:"y",ى:"a",ة:"h",ﻻ:"la",ﻷ:"laa",ﻹ:"lai",ﻵ:"laa",گ:"g",چ:"ch",پ:"p",ژ:"zh",ک:"k",ی:"y","َ":"a","ً":"an","ِ":"e","ٍ":"en","ُ":"u","ٌ":"on","ْ":"","٠":"0","١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","۰":"0","۱":"1","۲":"2","۳":"3","۴":"4","۵":"5","۶":"6","۷":"7","۸":"8","۹":"9",က:"k",ခ:"kh",ဂ:"g",ဃ:"ga",င:"ng",စ:"s",ဆ:"sa",ဇ:"z","စျ":"za",ည:"ny",ဋ:"t",ဌ:"ta",ဍ:"d",ဎ:"da",ဏ:"na",တ:"t",ထ:"ta",ဒ:"d",ဓ:"da",န:"n",ပ:"p",ဖ:"pa",ဗ:"b",ဘ:"ba",မ:"m",ယ:"y",ရ:"ya",လ:"l",ဝ:"w",သ:"th",ဟ:"h",ဠ:"la",အ:"a","ြ":"y","ျ":"ya","ွ":"w","ြွ":"yw","ျွ":"ywa","ှ":"h",ဧ:"e","၏":"-e",ဣ:"i",ဤ:"-i",ဉ:"u",ဦ:"-u",ဩ:"aw","သြော":"aw",ဪ:"aw","၀":"0","၁":"1","၂":"2","၃":"3","၄":"4","၅":"5","၆":"6","၇":"7","၈":"8","၉":"9","္":"","့":"","း":"",č:"c",ď:"d",ě:"e",ň:"n",ř:"r",š:"s",ť:"t",ů:"u",ž:"z",Č:"C",Ď:"D",Ě:"E",Ň:"N",Ř:"R",Š:"S",Ť:"T",Ů:"U",Ž:"Z",ހ:"h",ށ:"sh",ނ:"n",ރ:"r",ބ:"b",ޅ:"lh",ކ:"k",އ:"a",ވ:"v",މ:"m",ފ:"f",ދ:"dh",ތ:"th",ލ:"l",ގ:"g",ޏ:"gn",ސ:"s",ޑ:"d",ޒ:"z",ޓ:"t",ޔ:"y",ޕ:"p",ޖ:"j",ޗ:"ch",ޘ:"tt",ޙ:"hh",ޚ:"kh",ޛ:"th",ޜ:"z",ޝ:"sh",ޞ:"s",ޟ:"d",ޠ:"t",ޡ:"z",ޢ:"a",ޣ:"gh",ޤ:"q",ޥ:"w","ަ":"a","ާ":"aa","ި":"i","ީ":"ee","ު":"u","ޫ":"oo","ެ":"e","ޭ":"ey","ޮ":"o","ޯ":"oa","ް":"",ა:"a",ბ:"b",გ:"g",დ:"d",ე:"e",ვ:"v",ზ:"z",თ:"t",ი:"i",კ:"k",ლ:"l",მ:"m",ნ:"n",ო:"o",პ:"p",ჟ:"zh",რ:"r",ს:"s",ტ:"t",უ:"u",ფ:"p",ქ:"k",ღ:"gh",ყ:"q",შ:"sh",ჩ:"ch",ც:"ts",ძ:"dz",წ:"ts",ჭ:"ch",ხ:"kh",ჯ:"j",ჰ:"h",α:"a",β:"v",γ:"g",δ:"d",ε:"e",ζ:"z",η:"i",θ:"th",ι:"i",κ:"k",λ:"l",μ:"m",ν:"n",ξ:"ks",ο:"o",π:"p",ρ:"r",σ:"s",τ:"t",υ:"y",φ:"f",χ:"x",ψ:"ps",ω:"o",ά:"a",έ:"e",ί:"i",ό:"o",ύ:"y",ή:"i",ώ:"o",ς:"s",ϊ:"i",ΰ:"y",ϋ:"y",ΐ:"i",Α:"A",Β:"B",Γ:"G",Δ:"D",Ε:"E",Ζ:"Z",Η:"I",Θ:"TH",Ι:"I",Κ:"K",Λ:"L",Μ:"M",Ν:"N",Ξ:"KS",Ο:"O",Π:"P",Ρ:"R",Σ:"S",Τ:"T",Υ:"Y",Φ:"F",Χ:"X",Ψ:"PS",Ω:"O",Ά:"A",Έ:"E",Ί:"I",Ό:"O",Ύ:"Y",Ή:"I",Ώ:"O",Ϊ:"I",Ϋ:"Y",ā:"a",ē:"e",ģ:"g",ī:"i",ķ:"k",ļ:"l",ņ:"n",ū:"u",Ā:"A",Ē:"E",Ģ:"G",Ī:"I",Ķ:"k",Ļ:"L",Ņ:"N",Ū:"U",Ќ:"Kj",ќ:"kj",Љ:"Lj",љ:"lj",Њ:"Nj",њ:"nj",Тс:"Ts",тс:"ts",ą:"a",ć:"c",ę:"e",ł:"l",ń:"n",ś:"s",ź:"z",ż:"z",Ą:"A",Ć:"C",Ę:"E",Ł:"L",Ń:"N",Ś:"S",Ź:"Z",Ż:"Z",Є:"Ye",І:"I",Ї:"Yi",Ґ:"G",є:"ye",і:"i",ї:"yi",ґ:"g",ă:"a",Ă:"A",ș:"s",Ș:"S",ț:"t",Ț:"T",ţ:"t",Ţ:"T",а:"a",б:"b",в:"v",г:"g",д:"d",е:"e",ё:"yo",ж:"zh",з:"z",и:"i",й:"i",к:"k",л:"l",м:"m",н:"n",о:"o",п:"p",р:"r",с:"s",т:"t",у:"u",ф:"f",х:"kh",ц:"c",ч:"ch",ш:"sh",щ:"sh",ъ:"",ы:"y",ь:"",э:"e",ю:"yu",я:"ya",А:"A",Б:"B",В:"V",Г:"G",Д:"D",Е:"E",Ё:"Yo",Ж:"Zh",З:"Z",И:"I",Й:"I",К:"K",Л:"L",М:"M",Н:"N",О:"O",П:"P",Р:"R",С:"S",Т:"T",У:"U",Ф:"F",Х:"Kh",Ц:"C",Ч:"Ch",Ш:"Sh",Щ:"Sh",Ъ:"",Ы:"Y",Ь:"",Э:"E",Ю:"Yu",Я:"Ya",ђ:"dj",ј:"j",ћ:"c",џ:"dz",Ђ:"Dj",Ј:"j",Ћ:"C",Џ:"Dz",ľ:"l",ĺ:"l",ŕ:"r",Ľ:"L",Ĺ:"L",Ŕ:"R",ş:"s",Ş:"S",ı:"i",İ:"I",ğ:"g",Ğ:"G",ả:"a",Ả:"A",ẳ:"a",Ẳ:"A",ẩ:"a",Ẩ:"A",đ:"d",Đ:"D",ẹ:"e",Ẹ:"E",ẽ:"e",Ẽ:"E",ẻ:"e",Ẻ:"E",ế:"e",Ế:"E",ề:"e",Ề:"E",ệ:"e",Ệ:"E",ễ:"e",Ễ:"E",ể:"e",Ể:"E",ỏ:"o",ọ:"o",Ọ:"o",ố:"o",Ố:"O",ồ:"o",Ồ:"O",ổ:"o",Ổ:"O",ộ:"o",Ộ:"O",ỗ:"o",Ỗ:"O",ơ:"o",Ơ:"O",ớ:"o",Ớ:"O",ờ:"o",Ờ:"O",ợ:"o",Ợ:"O",ỡ:"o",Ỡ:"O",Ở:"o",ở:"o",ị:"i",Ị:"I",ĩ:"i",Ĩ:"I",ỉ:"i",Ỉ:"i",ủ:"u",Ủ:"U",ụ:"u",Ụ:"U",ũ:"u",Ũ:"U",ư:"u",Ư:"U",ứ:"u",Ứ:"U",ừ:"u",Ừ:"U",ự:"u",Ự:"U",ữ:"u",Ữ:"U",ử:"u",Ử:"ư",ỷ:"y",Ỷ:"y",ỳ:"y",Ỳ:"Y",ỵ:"y",Ỵ:"Y",ỹ:"y",Ỹ:"Y",ạ:"a",Ạ:"A",ấ:"a",Ấ:"A",ầ:"a",Ầ:"A",ậ:"a",Ậ:"A",ẫ:"a",Ẫ:"A",ắ:"a",Ắ:"A",ằ:"a",Ằ:"A",ặ:"a",Ặ:"A",ẵ:"a",Ẵ:"A","⓪":"0","①":"1","②":"2","③":"3","④":"4","⑤":"5","⑥":"6","⑦":"7","⑧":"8","⑨":"9","⑩":"10","⑪":"11","⑫":"12","⑬":"13","⑭":"14","⑮":"15","⑯":"16","⑰":"17","⑱":"18","⑲":"18","⑳":"18","⓵":"1","⓶":"2","⓷":"3","⓸":"4","⓹":"5","⓺":"6","⓻":"7","⓼":"8","⓽":"9","⓾":"10","⓿":"0","⓫":"11","⓬":"12","⓭":"13","⓮":"14","⓯":"15","⓰":"16","⓱":"17","⓲":"18","⓳":"19","⓴":"20","Ⓐ":"A","Ⓑ":"B","Ⓒ":"C","Ⓓ":"D","Ⓔ":"E","Ⓕ":"F","Ⓖ":"G","Ⓗ":"H","Ⓘ":"I","Ⓙ":"J","Ⓚ":"K","Ⓛ":"L","Ⓜ":"M","Ⓝ":"N","Ⓞ":"O","Ⓟ":"P","Ⓠ":"Q","Ⓡ":"R","Ⓢ":"S","Ⓣ":"T","Ⓤ":"U","Ⓥ":"V","Ⓦ":"W","Ⓧ":"X","Ⓨ":"Y","Ⓩ":"Z","ⓐ":"a","ⓑ":"b","ⓒ":"c","ⓓ":"d","ⓔ":"e","ⓕ":"f","ⓖ":"g","ⓗ":"h","ⓘ":"i","ⓙ":"j","ⓚ":"k","ⓛ":"l","ⓜ":"m","ⓝ":"n","ⓞ":"o","ⓟ":"p","ⓠ":"q","ⓡ":"r","ⓢ":"s","ⓣ":"t","ⓤ":"u","ⓦ":"v","ⓥ":"w","ⓧ":"x","ⓨ":"y","ⓩ":"z","“":'"',"”":'"',"‘":"'","’":"'","∂":"d",ƒ:"f","™":"(TM)","©":"(C)",œ:"oe",Œ:"OE","®":"(R)","†":"+","℠":"(SM)","…":"...","˚":"o",º:"o",ª:"a","•":"*","၊":",","။":".",$:"USD","€":"EUR","₢":"BRN","₣":"FRF","£":"GBP","₤":"ITL","₦":"NGN","₧":"ESP","₩":"KRW","₪":"ILS","₫":"VND","₭":"LAK","₮":"MNT","₯":"GRD","₱":"ARS","₲":"PYG","₳":"ARA","₴":"UAH","₵":"GHS","¢":"cent","¥":"CNY",元:"CNY",円:"YEN","﷼":"IRR","₠":"EWE","฿":"THB","₨":"INR","₹":"INR","₰":"PF","₺":"TRY","؋":"AFN","₼":"AZN",лв:"BGN","៛":"KHR","₡":"CRC","₸":"KZT",ден:"MKD",zł:"PLN","₽":"RUB","₾":"GEL"},s=["်","ް"],r={"ာ":"a","ါ":"a","ေ":"e","ဲ":"e","ိ":"i","ီ":"i","ို":"o","ု":"u","ူ":"u","ေါင်":"aung","ော":"aw","ော်":"aw","ေါ":"aw","ေါ်":"aw","်":"်","က်":"et","ိုက်":"aik","ောက်":"auk","င်":"in","ိုင်":"aing","ောင်":"aung","စ်":"it","ည်":"i","တ်":"at","ိတ်":"eik","ုတ်":"ok","ွတ်":"ut","ေတ်":"it","ဒ်":"d","ိုဒ်":"ok","ုဒ်":"ait","န်":"an","ာန်":"an","ိန်":"ein","ုန်":"on","ွန်":"un","ပ်":"at","ိပ်":"eik","ုပ်":"ok","ွပ်":"ut","န်ုပ်":"nub","မ်":"an","ိမ်":"ein","ုမ်":"on","ွမ်":"un","ယ်":"e","ိုလ်":"ol","ဉ်":"in","ံ":"an","ိံ":"ein","ုံ":"on","ައް":"ah","ަށް":"ah"},i={en:{},az:{ç:"c",ə:"e",ğ:"g",ı:"i",ö:"o",ş:"s",ü:"u",Ç:"C",Ə:"E",Ğ:"G",İ:"I",Ö:"O",Ş:"S",Ü:"U"},cs:{č:"c",ď:"d",ě:"e",ň:"n",ř:"r",š:"s",ť:"t",ů:"u",ž:"z",Č:"C",Ď:"D",Ě:"E",Ň:"N",Ř:"R",Š:"S",Ť:"T",Ů:"U",Ž:"Z"},fi:{ä:"a",Ä:"A",ö:"o",Ö:"O"},hu:{ä:"a",Ä:"A",ö:"o",Ö:"O",ü:"u",Ü:"U",ű:"u",Ű:"U"},lt:{ą:"a",č:"c",ę:"e",ė:"e",į:"i",š:"s",ų:"u",ū:"u",ž:"z",Ą:"A",Č:"C",Ę:"E",Ė:"E",Į:"I",Š:"S",Ų:"U",Ū:"U"},lv:{ā:"a",č:"c",ē:"e",ģ:"g",ī:"i",ķ:"k",ļ:"l",ņ:"n",š:"s",ū:"u",ž:"z",Ā:"A",Č:"C",Ē:"E",Ģ:"G",Ī:"i",Ķ:"k",Ļ:"L",Ņ:"N",Š:"S",Ū:"u",Ž:"Z"},pl:{ą:"a",ć:"c",ę:"e",ł:"l",ń:"n",ó:"o",ś:"s",ź:"z",ż:"z",Ą:"A",Ć:"C",Ę:"e",Ł:"L",Ń:"N",Ó:"O",Ś:"S",Ź:"Z",Ż:"Z"},sv:{ä:"a",Ä:"A",ö:"o",Ö:"O"},sk:{ä:"a",Ä:"A"},sr:{љ:"lj",њ:"nj",Љ:"Lj",Њ:"Nj",đ:"dj",Đ:"Dj"},tr:{Ü:"U",Ö:"O",ü:"u",ö:"o"}},l={ar:{"∆":"delta","∞":"la-nihaya","♥":"hob","&":"wa","|":"aw","<":"aqal-men",">":"akbar-men","∑":"majmou","¤":"omla"},az:{},ca:{"∆":"delta","∞":"infinit","♥":"amor","&":"i","|":"o","<":"menys que",">":"mes que","∑":"suma dels","¤":"moneda"},cs:{"∆":"delta","∞":"nekonecno","♥":"laska","&":"a","|":"nebo","<":"mensi nez",">":"vetsi nez","∑":"soucet","¤":"mena"},de:{"∆":"delta","∞":"unendlich","♥":"Liebe","&":"und","|":"oder","<":"kleiner als",">":"groesser als","∑":"Summe von","¤":"Waehrung"},dv:{"∆":"delta","∞":"kolunulaa","♥":"loabi","&":"aai","|":"noonee","<":"ah vure kuda",">":"ah vure bodu","∑":"jumula","¤":"faisaa"},en:{"∆":"delta","∞":"infinity","♥":"love","&":"and","|":"or","<":"less than",">":"greater than","∑":"sum","¤":"currency"},es:{"∆":"delta","∞":"infinito","♥":"amor","&":"y","|":"u","<":"menos que",">":"mas que","∑":"suma de los","¤":"moneda"},fa:{"∆":"delta","∞":"bi-nahayat","♥":"eshgh","&":"va","|":"ya","<":"kamtar-az",">":"bishtar-az","∑":"majmooe","¤":"vahed"},fi:{"∆":"delta","∞":"aarettomyys","♥":"rakkaus","&":"ja","|":"tai","<":"pienempi kuin",">":"suurempi kuin","∑":"summa","¤":"valuutta"},fr:{"∆":"delta","∞":"infiniment","♥":"Amour","&":"et","|":"ou","<":"moins que",">":"superieure a","∑":"somme des","¤":"monnaie"},ge:{"∆":"delta","∞":"usasruloba","♥":"siqvaruli","&":"da","|":"an","<":"naklebi",">":"meti","∑":"jami","¤":"valuta"},gr:{},hu:{"∆":"delta","∞":"vegtelen","♥":"szerelem","&":"es","|":"vagy","<":"kisebb mint",">":"nagyobb mint","∑":"szumma","¤":"penznem"},it:{"∆":"delta","∞":"infinito","♥":"amore","&":"e","|":"o","<":"minore di",">":"maggiore di","∑":"somma","¤":"moneta"},lt:{"∆":"delta","∞":"begalybe","♥":"meile","&":"ir","|":"ar","<":"maziau nei",">":"daugiau nei","∑":"suma","¤":"valiuta"},lv:{"∆":"delta","∞":"bezgaliba","♥":"milestiba","&":"un","|":"vai","<":"mazak neka",">":"lielaks neka","∑":"summa","¤":"valuta"},my:{"∆":"kwahkhyaet","∞":"asaonasme","♥":"akhyait","&":"nhin","|":"tho","<":"ngethaw",">":"kyithaw","∑":"paungld","¤":"ngwekye"},mk:{},nl:{"∆":"delta","∞":"oneindig","♥":"liefde","&":"en","|":"of","<":"kleiner dan",">":"groter dan","∑":"som","¤":"valuta"},pl:{"∆":"delta","∞":"nieskonczonosc","♥":"milosc","&":"i","|":"lub","<":"mniejsze niz",">":"wieksze niz","∑":"suma","¤":"waluta"},pt:{"∆":"delta","∞":"infinito","♥":"amor","&":"e","|":"ou","<":"menor que",">":"maior que","∑":"soma","¤":"moeda"},ro:{"∆":"delta","∞":"infinit","♥":"dragoste","&":"si","|":"sau","<":"mai mic ca",">":"mai mare ca","∑":"suma","¤":"valuta"},ru:{"∆":"delta","∞":"beskonechno","♥":"lubov","&":"i","|":"ili","<":"menshe",">":"bolshe","∑":"summa","¤":"valjuta"},sk:{"∆":"delta","∞":"nekonecno","♥":"laska","&":"a","|":"alebo","<":"menej ako",">":"viac ako","∑":"sucet","¤":"mena"},sr:{},tr:{"∆":"delta","∞":"sonsuzluk","♥":"ask","&":"ve","|":"veya","<":"kucuktur",">":"buyuktur","∑":"toplam","¤":"para birimi"},uk:{"∆":"delta","∞":"bezkinechnist","♥":"lubov","&":"i","|":"abo","<":"menshe",">":"bilshe","∑":"suma","¤":"valjuta"},vn:{"∆":"delta","∞":"vo cuc","♥":"yeu","&":"va","|":"hoac","<":"nho hon",">":"lon hon","∑":"tong","¤":"tien te"}},a=[";","?",":","@","&","=","+","$",",","/"].join(""),u=[";","?",":","@","&","=","+","$",","].join(""),f=[".","!","~","*","'","(",")"].join(""),c=function(g,y){var T="-",P="",b="",I=!0,F={},U,z,G,D,N,$,j,J,ve,X,L,Z,ae,Me,xe="";if(typeof g!="string")return"";if(typeof y=="string"&&(T=y),j=l.en,J=i.en,typeof y=="object"){U=y.maintainCase||!1,F=y.custom&&typeof y.custom=="object"?y.custom:F,G=+y.truncate>1&&y.truncate||!1,D=y.uric||!1,N=y.uricNoSlash||!1,$=y.mark||!1,I=!(y.symbols===!1||y.lang===!1),T=y.separator||T,D&&(xe+=a),N&&(xe+=u),$&&(xe+=f),j=y.lang&&l[y.lang]&&I?l[y.lang]:I?l.en:{},J=y.lang&&i[y.lang]?i[y.lang]:y.lang===!1||y.lang===!0?{}:i.en,y.titleCase&&typeof y.titleCase.length=="number"&&Array.prototype.toString.call(y.titleCase)?(y.titleCase.forEach(function(_e){F[_e+""]=_e+""}),z=!0):z=!!y.titleCase,y.custom&&typeof y.custom.length=="number"&&Array.prototype.toString.call(y.custom)&&y.custom.forEach(function(_e){F[_e+""]=_e+""}),Object.keys(F).forEach(function(_e){var Ut;_e.length>1?Ut=new RegExp("\\b"+d(_e)+"\\b","gi"):Ut=new RegExp(d(_e),"gi"),g=g.replace(Ut,F[_e])});for(L in F)xe+=L}for(xe+=T,xe=d(xe),g=g.replace(/(^\s+|\s+$)/g,""),ae=!1,Me=!1,X=0,Z=g.length;X<Z;X++)L=g[X],_(L,F)?ae=!1:J[L]?(L=ae&&J[L].match(/[A-Za-z0-9]/)?" "+J[L]:J[L],ae=!1):L in o?(X+1<Z&&s.indexOf(g[X+1])>=0?(b+=L,L=""):Me===!0?(L=r[b]+o[L],b=""):L=ae&&o[L].match(/[A-Za-z0-9]/)?" "+o[L]:o[L],ae=!1,Me=!1):L in r?(b+=L,L="",X===Z-1&&(L=r[b]),Me=!0):j[L]&&!(D&&a.indexOf(L)!==-1)&&!(N&&u.indexOf(L)!==-1)?(L=ae||P.substr(-1).match(/[A-Za-z0-9]/)?T+j[L]:j[L],L+=g[X+1]!==void 0&&g[X+1].match(/[A-Za-z0-9]/)?T:"",ae=!0):(Me===!0?(L=r[b]+L,b="",Me=!1):ae&&(/[A-Za-z0-9]/.test(L)||P.substr(-1).match(/A-Za-z0-9]/))&&(L=" "+L),ae=!1),P+=L.replace(new RegExp("[^\\w\\s"+xe+"_-]","g"),T);return z&&(P=P.replace(/(\w)(\S*)/g,function(_e,Ut,gs){var so=Ut.toUpperCase()+(gs!==null?gs:"");return Object.keys(F).indexOf(so.toLowerCase())<0?so:so.toLowerCase()})),P=P.replace(/\s+/g,T).replace(new RegExp("\\"+T+"+","g"),T).replace(new RegExp("(^\\"+T+"+|\\"+T+"+$)","g"),""),G&&P.length>G&&(ve=P.charAt(G)===T,P=P.slice(0,G),ve||(P=P.slice(0,P.lastIndexOf(T)))),!U&&!z&&(P=P.toLowerCase()),P},h=function(g){return function(T){return c(T,g)}},d=function(g){return g.replace(/[-\\^$*+?.()|[\]{}\/]/g,"\\$&")},_=function(v,g){for(var y in g)if(g[y]===v)return!0};if(typeof t<"u"&&t.exports)t.exports=c,t.exports.createSlug=h;else if(typeof define<"u"&&define.amd)define([],function(){return c});else try{if(n.getSlug||n.createSlug)throw"speakingurl: globals exists /(getSlug|createSlug)/";n.getSlug=c,n.createSlug=h}catch{}}(e)}}),Kp=Bl({"../../node_modules/.pnpm/speakingurl@14.0.1/node_modules/speakingurl/index.js"(e,t){w(),t.exports=jp()}});w(),w(),w();function Wp(e){if(O.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__){e();return}Object.defineProperty(O,"__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__",{set(t){t&&e()},configurable:!0})}w(),w(),w(),w(),w();function Gp(e){var t;const n=e.name||e._componentTag||e.__VUE_DEVTOOLS_COMPONENT_GUSSED_NAME__||e.__name;return n==="index"&&((t=e.__file)!=null&&t.endsWith("index.vue"))?"":n}function Yp(e){const t=e.__file;if(t)return Ll(Dp(t,".vue"))}function Hl(e,t){return e.type.__VUE_DEVTOOLS_COMPONENT_GUSSED_NAME__=t,t}function Ge(e){if(e.__VUE_DEVTOOLS_NEXT_APP_RECORD__)return e.__VUE_DEVTOOLS_NEXT_APP_RECORD__;if(e.root)return e.appContext.app.__VUE_DEVTOOLS_NEXT_APP_RECORD__}async function lr(e){const{app:t,uid:n,instance:o}=e;try{if(o.__VUE_DEVTOOLS_NEXT_UID__)return o.__VUE_DEVTOOLS_NEXT_UID__;const s=await Ge(t);if(!s)return null;const r=s.rootInstance===o;return`${s.id}:${r?"root":n}`}catch{}}function ar(e){var t,n;const o=(t=e.subTree)==null?void 0:t.type,s=Ge(e);return s?((n=s==null?void 0:s.types)==null?void 0:n.Fragment)===o:!1}function ur(e){return e._isBeingDestroyed||e.isUnmounted}function st(e){var t,n,o;const s=Gp((e==null?void 0:e.type)||{});if(s)return s;if((e==null?void 0:e.root)===e)return"Root";for(const i in(n=(t=e.parent)==null?void 0:t.type)==null?void 0:n.components)if(e.parent.type.components[i]===(e==null?void 0:e.type))return Hl(e,i);for(const i in(o=e.appContext)==null?void 0:o.components)if(e.appContext.components[i]===(e==null?void 0:e.type))return Hl(e,i);const r=Yp((e==null?void 0:e.type)||{});return r||"Anonymous Component"}function cr(e){var t,n,o;const s=(o=(n=(t=e==null?void 0:e.appContext)==null?void 0:t.app)==null?void 0:n.__VUE_DEVTOOLS_NEXT_APP_RECORD_ID__)!=null?o:0,r=e===(e==null?void 0:e.root)?"root":e.uid;return`${s}:${r}`}function qp(e){return e==null?"":typeof e=="number"?e:typeof e=="string"?`'${e}'`:Array.isArray(e)?"Array":"Object"}function It(e){try{return e()}catch(t){return t}}function pn(e,t){return t=t||`${e.id}:root`,e.instanceMap.get(t)||e.instanceMap.get(":root")}function fr(e,t,n=!1){return n||typeof e=="object"&&e!==null?t in e:!1}function Xp(){const e={top:0,bottom:0,left:0,right:0,get width(){return e.right-e.left},get height(){return e.bottom-e.top}};return e}var Ho;function Zp(e){return Ho||(Ho=document.createRange()),Ho.selectNode(e),Ho.getBoundingClientRect()}function Jp(e){const t=Xp();if(!e.children)return t;for(let n=0,o=e.children.length;n<o;n++){const s=e.children[n];let r;if(s.component)r=Yt(s.component);else if(s.el){const i=s.el;i.nodeType===1||i.getBoundingClientRect?r=i.getBoundingClientRect():i.nodeType===3&&i.data.trim()&&(r=Zp(i))}r&&Qp(t,r)}return t}function Qp(e,t){return(!e.top||t.top<e.top)&&(e.top=t.top),(!e.bottom||t.bottom>e.bottom)&&(e.bottom=t.bottom),(!e.left||t.left<e.left)&&(e.left=t.left),(!e.right||t.right>e.right)&&(e.right=t.right),e}var zl={top:0,left:0,right:0,bottom:0,width:0,height:0};function Yt(e){const t=e.subTree.el;return typeof window>"u"?zl:ar(e)?Jp(e.subTree):(t==null?void 0:t.nodeType)===1?t==null?void 0:t.getBoundingClientRect():e.subTree.component?Yt(e.subTree.component):zl}w();function Hn(e){return ar(e)?eh(e.subTree):e.subTree?[e.subTree.el]:[]}function eh(e){if(!e.children)return[];const t=[];return e.children.forEach(n=>{n.component?t.push(...Hn(n.component)):n!=null&&n.el&&t.push(n.el)}),t}var jl="__vue-devtools-component-inspector__",Kl="__vue-devtools-component-inspector__card__",Wl="__vue-devtools-component-inspector__name__",Gl="__vue-devtools-component-inspector__indicator__",Yl={display:"block",zIndex:2147483640,position:"fixed",backgroundColor:"#42b88325",border:"1px solid #42b88350",borderRadius:"5px",transition:"all 0.1s ease-in",pointerEvents:"none"},th={fontFamily:"Arial, Helvetica, sans-serif",padding:"5px 8px",borderRadius:"4px",textAlign:"left",position:"absolute",left:0,color:"#e9e9e9",fontSize:"14px",fontWeight:600,lineHeight:"24px",backgroundColor:"#42b883",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1)"},nh={display:"inline-block",fontWeight:400,fontStyle:"normal",fontSize:"12px",opacity:.7};function hn(){return document.getElementById(jl)}function oh(){return document.getElementById(Kl)}function sh(){return document.getElementById(Gl)}function rh(){return document.getElementById(Wl)}function dr(e){return{left:`${Math.round(e.left*100)/100}px`,top:`${Math.round(e.top*100)/100}px`,width:`${Math.round(e.width*100)/100}px`,height:`${Math.round(e.height*100)/100}px`}}function pr(e){var t;const n=document.createElement("div");n.id=(t=e.elementId)!=null?t:jl,Object.assign(n.style,{...Yl,...dr(e.bounds),...e.style});const o=document.createElement("span");o.id=Kl,Object.assign(o.style,{...th,top:e.bounds.top<35?0:"-35px"});const s=document.createElement("span");s.id=Wl,s.innerHTML=`&lt;${e.name}&gt;&nbsp;&nbsp;`;const r=document.createElement("i");return r.id=Gl,r.innerHTML=`${Math.round(e.bounds.width*100)/100} x ${Math.round(e.bounds.height*100)/100}`,Object.assign(r.style,nh),o.appendChild(s),o.appendChild(r),n.appendChild(o),document.body.appendChild(n),n}function hr(e){const t=hn(),n=oh(),o=rh(),s=sh();t&&(Object.assign(t.style,{...Yl,...dr(e.bounds)}),Object.assign(n.style,{top:e.bounds.top<35?0:"-35px"}),o.innerHTML=`&lt;${e.name}&gt;&nbsp;&nbsp;`,s.innerHTML=`${Math.round(e.bounds.width*100)/100} x ${Math.round(e.bounds.height*100)/100}`)}function ih(e){const t=Yt(e);if(!t.width&&!t.height)return;const n=st(e);hn()?hr({bounds:t,name:n}):pr({bounds:t,name:n})}function ql(){const e=hn();e&&(e.style.display="none")}var _r=null;function mr(e){const t=e.target;if(t){const n=t.__vueParentComponent;if(n&&(_r=n,n.vnode.el)){const s=Yt(n),r=st(n);hn()?hr({bounds:s,name:r}):pr({bounds:s,name:r})}}}function lh(e,t){if(e.preventDefault(),e.stopPropagation(),_r){const n=cr(_r);t(n)}}var zo=null;function ah(){ql(),window.removeEventListener("mouseover",mr),window.removeEventListener("click",zo,!0),zo=null}function uh(){return window.addEventListener("mouseover",mr),new Promise(e=>{function t(n){n.preventDefault(),n.stopPropagation(),lh(n,o=>{window.removeEventListener("click",t,!0),zo=null,window.removeEventListener("mouseover",mr);const s=hn();s&&(s.style.display="none"),e(JSON.stringify({id:o}))})}zo=t,window.addEventListener("click",t,!0)})}function ch(e){const t=pn(se.value,e.id);if(t){const[n]=Hn(t);if(typeof n.scrollIntoView=="function")n.scrollIntoView({behavior:"smooth"});else{const o=Yt(t),s=document.createElement("div"),r={...dr(o),position:"absolute"};Object.assign(s.style,r),document.body.appendChild(s),s.scrollIntoView({behavior:"smooth"}),setTimeout(()=>{document.body.removeChild(s)},2e3)}setTimeout(()=>{const o=Yt(t);if(o.width||o.height){const s=st(t),r=hn();r?hr({...e,name:s,bounds:o}):pr({...e,name:s,bounds:o}),setTimeout(()=>{r&&(r.style.display="none")},1500)}},1200)}}w();var Xl,Zl;(Zl=(Xl=O).__VUE_DEVTOOLS_COMPONENT_INSPECTOR_ENABLED__)!=null||(Xl.__VUE_DEVTOOLS_COMPONENT_INSPECTOR_ENABLED__=!0);function fh(e){let t=0;const n=setInterval(()=>{O.__VUE_INSPECTOR__&&(clearInterval(n),t+=30,e()),t>=5e3&&clearInterval(n)},30)}function dh(){const e=O.__VUE_INSPECTOR__,t=e.openInEditor;e.openInEditor=async(...n)=>{e.disable(),t(...n)}}function ph(){return new Promise(e=>{function t(){dh(),e(O.__VUE_INSPECTOR__)}O.__VUE_INSPECTOR__?t():fh(()=>{t()})})}w(),w();function hh(e){return!!(e&&e.__v_isReadonly)}function Jl(e){return hh(e)?Jl(e.__v_raw):!!(e&&e.__v_isReactive)}function gr(e){return!!(e&&e.__v_isRef===!0)}function zn(e){const t=e&&e.__v_raw;return t?zn(t):e}var Ql=class{constructor(){this.refEditor=new _h}set(e,t,n,o){const s=Array.isArray(t)?t:t.split(".");for(;s.length>1;){const l=s.shift();e instanceof Map&&(e=e.get(l)),e instanceof Set?e=Array.from(e.values())[l]:e=e[l],this.refEditor.isRef(e)&&(e=this.refEditor.get(e))}const r=s[0],i=this.refEditor.get(e)[r];o?o(e,r,n):this.refEditor.isRef(i)?this.refEditor.set(i,n):e[r]=n}get(e,t){const n=Array.isArray(t)?t:t.split(".");for(let o=0;o<n.length;o++)if(e instanceof Map?e=e.get(n[o]):e=e[n[o]],this.refEditor.isRef(e)&&(e=this.refEditor.get(e)),!e)return;return e}has(e,t,n=!1){if(typeof e>"u")return!1;const o=Array.isArray(t)?t.slice():t.split("."),s=n?2:1;for(;e&&o.length>s;){const r=o.shift();e=e[r],this.refEditor.isRef(e)&&(e=this.refEditor.get(e))}return e!=null&&Object.prototype.hasOwnProperty.call(e,o[0])}createDefaultSetCallback(e){return(t,n,o)=>{if((e.remove||e.newKey)&&(Array.isArray(t)?t.splice(n,1):zn(t)instanceof Map?t.delete(n):zn(t)instanceof Set?t.delete(Array.from(t.values())[n]):Reflect.deleteProperty(t,n)),!e.remove){const s=t[e.newKey||n];this.refEditor.isRef(s)?this.refEditor.set(s,o):zn(t)instanceof Map?t.set(e.newKey||n,o):zn(t)instanceof Set?t.add(o):t[e.newKey||n]=o}}}},_h=class{set(e,t){if(gr(e))e.value=t;else{if(e instanceof Set&&Array.isArray(t)){e.clear(),t.forEach(s=>e.add(s));return}const n=Object.keys(t);if(e instanceof Map){const s=new Set(e.keys());n.forEach(r=>{e.set(r,Reflect.get(t,r)),s.delete(r)}),s.forEach(r=>e.delete(r));return}const o=new Set(Object.keys(e));n.forEach(s=>{Reflect.set(e,s,Reflect.get(t,s)),o.delete(s)}),o.forEach(s=>Reflect.deleteProperty(e,s))}}get(e){return gr(e)?e.value:e}isRef(e){return gr(e)||Jl(e)}};async function mh(e,t){const{path:n,nodeId:o,state:s,type:r}=e,i=pn(se.value,o);if(!i)return;const l=n.slice();let a;Object.keys(i.props).includes(n[0])?a=i.props:i.devtoolsRawSetupState&&Object.keys(i.devtoolsRawSetupState).includes(n[0])?a=i.devtoolsRawSetupState:i.data&&Object.keys(i.data).includes(n[0])?a=i.data:a=i.proxy,a&&l&&(s.type,t.set(a,l,s.value,t.createDefaultSetCallback(s)))}var gh=new Ql;async function vh(e){mh(e,gh)}w(),w(),w();var yh="__VUE_DEVTOOLS_KIT_TIMELINE_LAYERS_STATE__";function Eh(){if(!Bo||typeof localStorage>"u"||localStorage===null)return{recordingState:!1,mouseEventEnabled:!1,keyboardEventEnabled:!1,componentEventEnabled:!1,performanceEventEnabled:!1,selected:""};const e=localStorage.getItem(yh);return e?JSON.parse(e):{recordingState:!1,mouseEventEnabled:!1,keyboardEventEnabled:!1,componentEventEnabled:!1,performanceEventEnabled:!1,selected:""}}w(),w(),w();var ea,ta;(ta=(ea=O).__VUE_DEVTOOLS_KIT_TIMELINE_LAYERS)!=null||(ea.__VUE_DEVTOOLS_KIT_TIMELINE_LAYERS=[]);var bh=new Proxy(O.__VUE_DEVTOOLS_KIT_TIMELINE_LAYERS,{get(e,t,n){return Reflect.get(e,t,n)}});function wh(e,t){q.timelineLayersState[t.id]=!1,bh.push({...e,descriptorId:t.id,appRecord:Ge(t.app)})}var na,oa;(oa=(na=O).__VUE_DEVTOOLS_KIT_INSPECTOR__)!=null||(na.__VUE_DEVTOOLS_KIT_INSPECTOR__=[]);var vr=new Proxy(O.__VUE_DEVTOOLS_KIT_INSPECTOR__,{get(e,t,n){return Reflect.get(e,t,n)}}),sa=Pt(()=>{Ye.hooks.callHook("sendInspectorToClient",ra())});function Sh(e,t){var n,o;vr.push({options:e,descriptor:t,treeFilterPlaceholder:(n=e.treeFilterPlaceholder)!=null?n:"Search tree...",stateFilterPlaceholder:(o=e.stateFilterPlaceholder)!=null?o:"Search state...",treeFilter:"",selectedNodeId:"",appRecord:Ge(t.app)}),sa()}function ra(){return vr.filter(e=>e.descriptor.app===se.value.app).filter(e=>e.descriptor.id!=="components").map(e=>{var t;const n=e.descriptor,o=e.options;return{id:o.id,label:o.label,logo:n.logo,icon:`custom-ic-baseline-${(t=o==null?void 0:o.icon)==null?void 0:t.replace(/_/g,"-")}`,packageName:n.packageName,homepage:n.homepage,pluginId:n.id}})}function jo(e,t){return vr.find(n=>n.options.id===e&&(t?n.descriptor.app===t:!0))}function Th(){const e=Fl();e.hook("addInspector",({inspector:o,plugin:s})=>{Sh(o,s.descriptor)});const t=Pt(async({inspectorId:o,plugin:s})=>{var r;if(!o||!((r=s==null?void 0:s.descriptor)!=null&&r.app)||q.highPerfModeEnabled)return;const i=jo(o,s.descriptor.app),l={app:s.descriptor.app,inspectorId:o,filter:(i==null?void 0:i.treeFilter)||"",rootNodes:[]};await new Promise(a=>{e.callHookWith(async u=>{await Promise.all(u.map(f=>f(l))),a()},"getInspectorTree")}),e.callHookWith(async a=>{await Promise.all(a.map(u=>u({inspectorId:o,rootNodes:l.rootNodes})))},"sendInspectorTreeToClient")},120);e.hook("sendInspectorTree",t);const n=Pt(async({inspectorId:o,plugin:s})=>{var r;if(!o||!((r=s==null?void 0:s.descriptor)!=null&&r.app)||q.highPerfModeEnabled)return;const i=jo(o,s.descriptor.app),l={app:s.descriptor.app,inspectorId:o,nodeId:(i==null?void 0:i.selectedNodeId)||"",state:null},a={currentTab:`custom-inspector:${o}`};l.nodeId&&await new Promise(u=>{e.callHookWith(async f=>{await Promise.all(f.map(c=>c(l,a))),u()},"getInspectorState")}),e.callHookWith(async u=>{await Promise.all(u.map(f=>f({inspectorId:o,nodeId:l.nodeId,state:l.state})))},"sendInspectorStateToClient")},120);return e.hook("sendInspectorState",n),e.hook("customInspectorSelectNode",({inspectorId:o,nodeId:s,plugin:r})=>{const i=jo(o,r.descriptor.app);i&&(i.selectedNodeId=s)}),e.hook("timelineLayerAdded",({options:o,plugin:s})=>{wh(o,s.descriptor)}),e.hook("timelineEventAdded",({options:o,plugin:s})=>{var r;const i=["performance","component-event","keyboard","mouse"];q.highPerfModeEnabled||!((r=q.timelineLayersState)!=null&&r[s.descriptor.id])&&!i.includes(o.layerId)||e.callHookWith(async l=>{await Promise.all(l.map(a=>a(o)))},"sendTimelineEventToClient")}),e.hook("getComponentInstances",async({app:o})=>{const s=o.__VUE_DEVTOOLS_NEXT_APP_RECORD__;if(!s)return null;const r=s.id.toString();return[...s.instanceMap].filter(([l])=>l.split(":")[0]===r).map(([,l])=>l)}),e.hook("getComponentBounds",async({instance:o})=>Yt(o)),e.hook("getComponentName",({instance:o})=>st(o)),e.hook("componentHighlight",({uid:o})=>{const s=se.value.instanceMap.get(o);s&&ih(s)}),e.hook("componentUnhighlight",()=>{ql()}),e}var ia,la;(la=(ia=O).__VUE_DEVTOOLS_KIT_APP_RECORDS__)!=null||(ia.__VUE_DEVTOOLS_KIT_APP_RECORDS__=[]);var aa,ua;(ua=(aa=O).__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD__)!=null||(aa.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD__={});var ca,fa;(fa=(ca=O).__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD_ID__)!=null||(ca.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD_ID__="");var da,pa;(pa=(da=O).__VUE_DEVTOOLS_KIT_CUSTOM_TABS__)!=null||(da.__VUE_DEVTOOLS_KIT_CUSTOM_TABS__=[]);var ha,_a;(_a=(ha=O).__VUE_DEVTOOLS_KIT_CUSTOM_COMMANDS__)!=null||(ha.__VUE_DEVTOOLS_KIT_CUSTOM_COMMANDS__=[]);var rt="__VUE_DEVTOOLS_KIT_GLOBAL_STATE__";function xh(){return{connected:!1,clientConnected:!1,vitePluginDetected:!0,appRecords:[],activeAppRecordId:"",tabs:[],commands:[],highPerfModeEnabled:!0,devtoolsClientDetected:{},perfUniqueGroupId:0,timelineLayersState:Eh()}}var ma,ga;(ga=(ma=O)[rt])!=null||(ma[rt]=xh());var Oh=Pt(e=>{Ye.hooks.callHook("devtoolsStateUpdated",{state:e})}),Ah=Pt((e,t)=>{Ye.hooks.callHook("devtoolsConnectedUpdated",{state:e,oldState:t})}),kt=new Proxy(O.__VUE_DEVTOOLS_KIT_APP_RECORDS__,{get(e,t,n){return t==="value"?O.__VUE_DEVTOOLS_KIT_APP_RECORDS__:O.__VUE_DEVTOOLS_KIT_APP_RECORDS__[t]}}),Ch=e=>{O.__VUE_DEVTOOLS_KIT_APP_RECORDS__=[...O.__VUE_DEVTOOLS_KIT_APP_RECORDS__,e]},Dh=e=>{O.__VUE_DEVTOOLS_KIT_APP_RECORDS__=kt.value.filter(t=>t.app!==e)},se=new Proxy(O.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD__,{get(e,t,n){return t==="value"?O.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD__:t==="id"?O.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD_ID__:O.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD__[t]}});function yr(){Oh({...O[rt],appRecords:kt.value,activeAppRecordId:se.id,tabs:O.__VUE_DEVTOOLS_KIT_CUSTOM_TABS__,commands:O.__VUE_DEVTOOLS_KIT_CUSTOM_COMMANDS__})}function Er(e){O.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD__=e,yr()}function va(e){O.__VUE_DEVTOOLS_KIT_ACTIVE_APP_RECORD_ID__=e,yr()}var q=new Proxy(O[rt],{get(e,t){return t==="appRecords"?kt:t==="activeAppRecordId"?se.id:t==="tabs"?O.__VUE_DEVTOOLS_KIT_CUSTOM_TABS__:t==="commands"?O.__VUE_DEVTOOLS_KIT_CUSTOM_COMMANDS__:O[rt][t]},deleteProperty(e,t){return delete e[t],!0},set(e,t,n){return{...O[rt]},e[t]=n,O[rt][t]=n,!0}});function br(e){const t={...O[rt],appRecords:kt.value,activeAppRecordId:se.id};(t.connected!==e.connected&&e.connected||t.clientConnected!==e.clientConnected&&e.clientConnected)&&Ah(O[rt],t),Object.assign(O[rt],e),yr()}function Ph(e){return new Promise(t=>{q.connected&&(e(),t()),Ye.hooks.hook("devtoolsConnectedUpdated",({state:n})=>{n.connected&&(e(),t())})})}function Ih(e={}){var t,n,o;const{file:s,host:r,baseUrl:i=window.location.origin,line:l=0,column:a=0}=e;if(s){if(r==="chrome-extension"){const u=s.replace(/\\/g,"\\\\"),f=(n=(t=window.VUE_DEVTOOLS_CONFIG)==null?void 0:t.openInEditorHost)!=null?n:"/";fetch(`${f}__open-in-editor?file=${encodeURI(s)}`).then(c=>{if(!c.ok){const h=`Opening component ${u} failed`;console.log(`%c${h}`,"color:red")}})}else if(q.vitePluginDetected){const u=(o=O.__VUE_DEVTOOLS_OPEN_IN_EDITOR_BASE_URL__)!=null?o:i;O.__VUE_INSPECTOR__.openInEditor(u,s,l,a)}}}w(),w(),w(),w(),w();var ya,Ea;(Ea=(ya=O).__VUE_DEVTOOLS_KIT_PLUGIN_BUFFER__)!=null||(ya.__VUE_DEVTOOLS_KIT_PLUGIN_BUFFER__=[]);var jn=new Proxy(O.__VUE_DEVTOOLS_KIT_PLUGIN_BUFFER__,{get(e,t,n){return Reflect.get(e,t,n)}});function kh(e,t){jn.push([e,t])}function wr(e){const t={};return Object.keys(e).forEach(n=>{t[n]=e[n].defaultValue}),t}function Sr(e){return`__VUE_DEVTOOLS_NEXT_PLUGIN_SETTINGS__${e}__`}function Rh(e){var t,n,o;const s=(n=(t=jn.find(r=>{var i;return r[0].id===e&&!!((i=r[0])!=null&&i.settings)}))==null?void 0:t[0])!=null?n:null;return(o=s==null?void 0:s.settings)!=null?o:null}function ba(e,t){var n,o,s;const r=Sr(e);if(r){const i=localStorage.getItem(r);if(i)return JSON.parse(i)}if(e){const i=(o=(n=jn.find(l=>l[0].id===e))==null?void 0:n[0])!=null?o:null;return wr((s=i==null?void 0:i.settings)!=null?s:{})}return wr(t)}function wa(e,t){const n=Sr(e);localStorage.getItem(n)||localStorage.setItem(n,JSON.stringify(wr(t)))}function Nh(e,t,n){const o=Sr(e),s=localStorage.getItem(o),r=JSON.parse(s||"{}"),i={...r,[t]:n};localStorage.setItem(o,JSON.stringify(i)),Ye.hooks.callHookWith(l=>{l.forEach(a=>a({pluginId:e,key:t,oldValue:r[t],newValue:n,settings:i}))},"setPluginSettings")}w(),w(),w(),w(),w(),w(),w(),w(),w(),w(),w();var Sa,Ta,he=(Ta=(Sa=O).__VUE_DEVTOOLS_HOOK)!=null?Ta:Sa.__VUE_DEVTOOLS_HOOK=Fl(),Vh={vueAppInit(e){he.hook("app:init",e)},vueAppUnmount(e){he.hook("app:unmount",e)},vueAppConnected(e){he.hook("app:connected",e)},componentAdded(e){return he.hook("component:added",e)},componentEmit(e){return he.hook("component:emit",e)},componentUpdated(e){return he.hook("component:updated",e)},componentRemoved(e){return he.hook("component:removed",e)},setupDevtoolsPlugin(e){he.hook("devtools-plugin:setup",e)},perfStart(e){return he.hook("perf:start",e)},perfEnd(e){return he.hook("perf:end",e)}};function Lh(){return{id:"vue-devtools-next",devtoolsVersion:"7.0",enabled:!1,appRecords:[],apps:[],events:new Map,on(e,t){var n;return this.events.has(e)||this.events.set(e,[]),(n=this.events.get(e))==null||n.push(t),()=>this.off(e,t)},once(e,t){const n=(...o)=>{this.off(e,n),t(...o)};return this.on(e,n),[e,n]},off(e,t){if(this.events.has(e)){const n=this.events.get(e),o=n.indexOf(t);o!==-1&&n.splice(o,1)}},emit(e,...t){this.events.has(e)&&this.events.get(e).forEach(n=>n(...t))}}}function Mh(e){e.on("app:init",(t,n,o)=>{var s,r,i;(i=(r=(s=t==null?void 0:t._instance)==null?void 0:s.type)==null?void 0:r.devtools)!=null&&i.hide||he.callHook("app:init",t,n,o)}),e.on("app:unmount",t=>{he.callHook("app:unmount",t)}),e.on("component:added",async(t,n,o,s)=>{var r,i,l;(l=(i=(r=t==null?void 0:t._instance)==null?void 0:r.type)==null?void 0:i.devtools)!=null&&l.hide||q.highPerfModeEnabled||!t||typeof n!="number"&&!n||!s||he.callHook("component:added",t,n,o,s)}),e.on("component:updated",(t,n,o,s)=>{!t||typeof n!="number"&&!n||!s||q.highPerfModeEnabled||he.callHook("component:updated",t,n,o,s)}),e.on("component:removed",async(t,n,o,s)=>{!t||typeof n!="number"&&!n||!s||q.highPerfModeEnabled||he.callHook("component:removed",t,n,o,s)}),e.on("component:emit",async(t,n,o,s)=>{!t||!n||q.highPerfModeEnabled||he.callHook("component:emit",t,n,o,s)}),e.on("perf:start",(t,n,o,s,r)=>{!t||q.highPerfModeEnabled||he.callHook("perf:start",t,n,o,s,r)}),e.on("perf:end",(t,n,o,s,r)=>{!t||q.highPerfModeEnabled||he.callHook("perf:end",t,n,o,s,r)}),e.on("devtools-plugin:setup",(t,n,o)=>{(o==null?void 0:o.target)!=="legacy"&&he.callHook("devtools-plugin:setup",t,n)})}var He={on:Vh,setupDevToolsPlugin(e,t){return he.callHook("devtools-plugin:setup",e,t)}},$h=class{constructor({plugin:e,ctx:t}){this.hooks=t.hooks,this.plugin=e}get on(){return{visitComponentTree:e=>{this.hooks.hook("visitComponentTree",e)},inspectComponent:e=>{this.hooks.hook("inspectComponent",e)},editComponentState:e=>{this.hooks.hook("editComponentState",e)},getInspectorTree:e=>{this.hooks.hook("getInspectorTree",e)},getInspectorState:e=>{this.hooks.hook("getInspectorState",e)},editInspectorState:e=>{this.hooks.hook("editInspectorState",e)},inspectTimelineEvent:e=>{this.hooks.hook("inspectTimelineEvent",e)},timelineCleared:e=>{this.hooks.hook("timelineCleared",e)},setPluginSettings:e=>{this.hooks.hook("setPluginSettings",e)}}}notifyComponentUpdate(e){var t;if(q.highPerfModeEnabled)return;const n=ra().find(o=>o.packageName===this.plugin.descriptor.packageName);if(n!=null&&n.id){if(e){const o=[e.appContext.app,e.uid,(t=e.parent)==null?void 0:t.uid,e];he.callHook("component:updated",...o)}else he.callHook("component:updated");this.hooks.callHook("sendInspectorState",{inspectorId:n.id,plugin:this.plugin})}}addInspector(e){this.hooks.callHook("addInspector",{inspector:e,plugin:this.plugin}),this.plugin.descriptor.settings&&wa(e.id,this.plugin.descriptor.settings)}sendInspectorTree(e){q.highPerfModeEnabled||this.hooks.callHook("sendInspectorTree",{inspectorId:e,plugin:this.plugin})}sendInspectorState(e){q.highPerfModeEnabled||this.hooks.callHook("sendInspectorState",{inspectorId:e,plugin:this.plugin})}selectInspectorNode(e,t){this.hooks.callHook("customInspectorSelectNode",{inspectorId:e,nodeId:t,plugin:this.plugin})}visitComponentTree(e){return this.hooks.callHook("visitComponentTree",e)}now(){return q.highPerfModeEnabled?0:Date.now()}addTimelineLayer(e){this.hooks.callHook("timelineLayerAdded",{options:e,plugin:this.plugin})}addTimelineEvent(e){q.highPerfModeEnabled||this.hooks.callHook("timelineEventAdded",{options:e,plugin:this.plugin})}getSettings(e){return ba(e??this.plugin.descriptor.id,this.plugin.descriptor.settings)}getComponentInstances(e){return this.hooks.callHook("getComponentInstances",{app:e})}getComponentBounds(e){return this.hooks.callHook("getComponentBounds",{instance:e})}getComponentName(e){return this.hooks.callHook("getComponentName",{instance:e})}highlightElement(e){const t=e.__VUE_DEVTOOLS_NEXT_UID__;return this.hooks.callHook("componentHighlight",{uid:t})}unhighlightElement(){return this.hooks.callHook("componentUnhighlight")}},Fh=$h;w(),w(),w(),w();var Uh=new Set(["nextTick","defineComponent","defineAsyncComponent","defineCustomElement","ref","computed","reactive","readonly","watchEffect","watchPostEffect","watchSyncEffect","watch","isRef","unref","toRef","toRefs","isProxy","isReactive","isReadonly","shallowRef","triggerRef","customRef","shallowReactive","shallowReadonly","toRaw","markRaw","effectScope","getCurrentScope","onScopeDispose","onMounted","onUpdated","onUnmounted","onBeforeMount","onBeforeUpdate","onBeforeUnmount","onErrorCaptured","onRenderTracked","onRenderTriggered","onActivated","onDeactivated","onServerPrefetch","provide","inject","h","mergeProps","cloneVNode","isVNode","resolveComponent","resolveDirective","withDirectives","withModifiers"]),Bh=/^(?:function|class) (\w+)/,Hh="__vue_devtool_undefined__",zh="__vue_devtool_infinity__",jh="__vue_devtool_negative_infinity__",Kh="__vue_devtool_nan__";w(),w();function xa(e){return!!e.__v_isRef}function Wh(e){return xa(e)&&!!e.effect}function Gh(e){return!!e.__v_isReactive}function Yh(e){return!!e.__v_isReadonly}var qh={[Hh]:"undefined",[Kh]:"NaN",[zh]:"Infinity",[jh]:"-Infinity"};Object.entries(qh).reduce((e,[t,n])=>(e[n]=t,e),{});function Oa(e){if(Array.isArray(e))return e.map(n=>Oa(n)).join(" or ");if(e==null)return"null";const t=e.toString().match(Bh);return typeof e=="function"&&t&&t[1]||"any"}function Xh(e){try{return{ref:xa(e),computed:Wh(e),reactive:Gh(e),readonly:Yh(e)}}catch{return{ref:!1,computed:!1,reactive:!1,readonly:!1}}}function Zh(e){return e!=null&&e.__v_raw?e.__v_raw:e}function Ko(e,t,n){if(typeof t=="function"&&(t=t.options),!t)return e;const{mixins:o,extends:s}=t;s&&Ko(e,s),o&&o.forEach(r=>Ko(e,r));for(const r of["computed","inject"])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]?Object.assign(e[r],t[r]):e[r]=t[r]);return e}function Jh(e){const t=e==null?void 0:e.type;if(!t)return{};const{mixins:n,extends:o}=t,s=e.appContext.mixins;if(!s.length&&!n&&!o)return t;const r={};return s.forEach(i=>Ko(r,i)),Ko(r,t),r}function Qh(e){var t;const n=[],o=(t=e==null?void 0:e.type)==null?void 0:t.props;for(const s in e==null?void 0:e.props){const r=o?o[s]:null,i=Ap(s);n.push({type:"props",key:i,value:It(()=>e.props[s]),editable:!0,meta:r?{type:r.type?Oa(r.type):"any",required:!!r.required,...r.default?{default:r.default.toString()}:{}}:{type:"invalid"}})}return n}function e_(e){const t=e.type,n=t==null?void 0:t.props,o=t.vuex&&t.vuex.getters,s=t.computed,r={...e.data,...e.renderContext};return Object.keys(r).filter(i=>!(n&&i in n)&&!(o&&i in o)&&!(s&&i in s)).map(i=>({key:i,type:"data",value:It(()=>r[i]),editable:!0}))}function t_(e){const t=e.computed?"computed":e.ref?"ref":e.reactive?"reactive":null,n=t?`${t.charAt(0).toUpperCase()}${t.slice(1)}`:null;return{stateType:t,stateTypeName:n}}function n_(e){const t=e.devtoolsRawSetupState||{};return Object.keys(e.setupState).filter(n=>!Uh.has(n)&&n.split(/(?=[A-Z])/)[0]!=="use").map(n=>{var o,s,r,i;const l=It(()=>Zh(e.setupState[n])),a=l instanceof Error,u=t[n];let f,c=a||typeof l=="function"||fr(l,"render")&&typeof l.render=="function"||fr(l,"__asyncLoader")&&typeof l.__asyncLoader=="function"||typeof l=="object"&&l&&("setup"in l||"props"in l)||/^v[A-Z]/.test(n);if(u&&!a){const d=Xh(u),{stateType:_,stateTypeName:v}=t_(d),g=d.ref||d.computed||d.reactive,y=fr(u,"effect")?((s=(o=u.effect)==null?void 0:o.raw)==null?void 0:s.toString())||((i=(r=u.effect)==null?void 0:r.fn)==null?void 0:i.toString()):null;_&&(c=!1),f={..._?{stateType:_,stateTypeName:v}:{},...y?{raw:y}:{},editable:g&&!d.readonly}}return{key:n,value:l,type:c?"setup (other)":"setup",...f}})}function o_(e,t){const n=t,o=[],s=n.computed||{};for(const r in s){const i=s[r],l=typeof i=="function"&&i.vuex?"vuex bindings":"computed";o.push({type:l,key:r,value:It(()=>{var a;return(a=e==null?void 0:e.proxy)==null?void 0:a[r]}),editable:typeof i.set=="function"})}return o}function s_(e){return Object.keys(e.attrs).map(t=>({type:"attrs",key:t,value:It(()=>e.attrs[t])}))}function r_(e){return Reflect.ownKeys(e.provides).map(t=>({type:"provided",key:t.toString(),value:It(()=>e.provides[t])}))}function i_(e,t){if(!(t!=null&&t.inject))return[];let n=[],o;return Array.isArray(t.inject)?n=t.inject.map(s=>({key:s,originalKey:s})):n=Reflect.ownKeys(t.inject).map(s=>{const r=t.inject[s];let i;return typeof r=="string"||typeof r=="symbol"?i=r:(i=r.from,o=r.default),{key:s,originalKey:i}}),n.map(({key:s,originalKey:r})=>({type:"injected",key:r&&s!==r?`${r.toString()} ➞ ${s.toString()}`:s.toString(),value:It(()=>e.ctx.hasOwnProperty(s)?e.ctx[s]:e.provides.hasOwnProperty(r)?e.provides[r]:o)}))}function l_(e){return Object.keys(e.refs).map(t=>({type:"template refs",key:t,value:It(()=>e.refs[t])}))}function a_(e){var t,n;const o=e.type.emits,s=Array.isArray(o)?o:Object.keys(o??{}),r=Object.keys((n=(t=e==null?void 0:e.vnode)==null?void 0:t.props)!=null?n:{}),i=[];for(const l of r){const[a,...u]=l.split(/(?=[A-Z])/);if(a==="on"){const f=u.join("-").toLowerCase(),c=s.includes(f);i.push({type:"event listeners",key:f,value:{_custom:{displayText:c?"✅ Declared":"⚠️ Not declared",key:c?"✅ Declared":"⚠️ Not declared",value:c?"✅ Declared":"⚠️ Not declared",tooltipText:c?null:`The event <code>${f}</code> is not declared in the <code>emits</code> option. It will leak into the component's attributes (<code>$attrs</code>).`}}})}}return i}function u_(e){const t=Jh(e);return Qh(e).concat(e_(e),n_(e),o_(e,t),s_(e),r_(e),i_(e,t),l_(e),a_(e))}function c_(e){var t;const n=pn(se.value,e.instanceId),o=cr(n),s=st(n),r=(t=n==null?void 0:n.type)==null?void 0:t.__file,i=u_(n);return{id:o,name:s,file:r,state:i,instance:n}}w(),w();var f_=class{constructor(e){this.filter=e||""}isQualified(e){const t=st(e);return Ll(t).toLowerCase().includes(this.filter)||Cp(t).toLowerCase().includes(this.filter)}};function d_(e){return new f_(e)}var p_=class{constructor(e){this.captureIds=new Map;const{filterText:t="",maxDepth:n,recursively:o,api:s}=e;this.componentFilter=d_(t),this.maxDepth=n,this.recursively=o,this.api=s}getComponentTree(e){return this.captureIds=new Map,this.findQualifiedChildren(e,0)}getComponentParents(e){this.captureIds=new Map;const t=[];this.captureId(e);let n=e;for(;n=n.parent;)this.captureId(n),t.push(n);return t}captureId(e){if(!e)return null;const t=e.__VUE_DEVTOOLS_NEXT_UID__!=null?e.__VUE_DEVTOOLS_NEXT_UID__:cr(e);return e.__VUE_DEVTOOLS_NEXT_UID__=t,this.captureIds.has(t)?null:(this.captureIds.set(t,void 0),this.mark(e),t)}async capture(e,t){var n;if(!e)return null;const o=this.captureId(e),s=st(e),r=this.getInternalInstanceChildren(e.subTree).filter(c=>!ur(c)),i=this.getComponentParents(e)||[],l=!!e.isDeactivated||i.some(c=>c.isDeactivated),a={uid:e.uid,id:o,name:s,renderKey:qp(e.vnode?e.vnode.key:null),inactive:l,children:[],isFragment:ar(e),tags:typeof e.type!="function"?[]:[{label:"functional",textColor:5592405,backgroundColor:15658734}],autoOpen:this.recursively,file:e.type.__file||""};if((t<this.maxDepth||e.type.__isKeepAlive||i.some(c=>c.type.__isKeepAlive))&&(a.children=await Promise.all(r.map(c=>this.capture(c,t+1)).filter(Boolean))),this.isKeepAlive(e)){const c=this.getKeepAliveCachedInstances(e),h=r.map(d=>d.__VUE_DEVTOOLS_NEXT_UID__);for(const d of c)if(!h.includes(d.__VUE_DEVTOOLS_NEXT_UID__)){const _=await this.capture({...d,isDeactivated:!0},t+1);_&&a.children.push(_)}}const f=Hn(e)[0];if(f!=null&&f.parentElement){const c=e.parent,h=c?Hn(c):[];let d=f;const _=[];do _.push(Array.from(d.parentElement.childNodes).indexOf(d)),d=d.parentElement;while(d.parentElement&&h.length&&!h.includes(d));a.domOrder=_.reverse()}else a.domOrder=[-1];return(n=e.suspense)!=null&&n.suspenseKey&&(a.tags.push({label:e.suspense.suspenseKey,backgroundColor:14979812,textColor:16777215}),this.mark(e,!0)),this.api.visitComponentTree({treeNode:a,componentInstance:e,app:e.appContext.app,filter:this.componentFilter.filter}),a}async findQualifiedChildren(e,t){var n;if(this.componentFilter.isQualified(e)&&!((n=e.type.devtools)!=null&&n.hide))return[await this.capture(e,t)];if(e.subTree){const o=this.isKeepAlive(e)?this.getKeepAliveCachedInstances(e):this.getInternalInstanceChildren(e.subTree);return this.findQualifiedChildrenFromList(o,t)}else return[]}async findQualifiedChildrenFromList(e,t){return e=e.filter(n=>{var o;return!ur(n)&&!((o=n.type.devtools)!=null&&o.hide)}),this.componentFilter.filter?Array.prototype.concat.apply([],await Promise.all(e.map(n=>this.findQualifiedChildren(n,t)))):Promise.all(e.map(n=>this.capture(n,t)))}getInternalInstanceChildren(e,t=null){const n=[];if(e)if(e.component)t?n.push({...e.component,suspense:t}):n.push(e.component);else if(e.suspense){const o=e.suspense.isInFallback?"suspense fallback":"suspense default";n.push(...this.getInternalInstanceChildren(e.suspense.activeBranch,{...e.suspense,suspenseKey:o}))}else Array.isArray(e.children)&&e.children.forEach(o=>{o.component?t?n.push({...o.component,suspense:t}):n.push(o.component):n.push(...this.getInternalInstanceChildren(o,t))});return n.filter(o=>{var s;return!ur(o)&&!((s=o.type.devtools)!=null&&s.hide)})}mark(e,t=!1){const n=Ge(e).instanceMap;(t||!n.has(e.__VUE_DEVTOOLS_NEXT_UID__))&&(n.set(e.__VUE_DEVTOOLS_NEXT_UID__,e),se.value.instanceMap=n)}isKeepAlive(e){return e.type.__isKeepAlive&&e.__v_cache}getKeepAliveCachedInstances(e){return Array.from(e.__v_cache.values()).map(t=>t.component).filter(Boolean)}};w(),w();var Wo=new Map,Tr="performance";async function h_(e,t,n,o,s,r){const i=await Ge(t);if(!i)return;const l=st(o)||"Unknown Component",a=q.perfUniqueGroupId++,u=`${n}-${s}`;if(i.perfGroupIds.set(u,{groupId:a,time:r}),await e.addTimelineEvent({layerId:Tr,event:{time:Date.now(),data:{component:l,type:s,measure:"start"},title:l,subtitle:s,groupId:a}}),Wo.has(u)){const{app:f,uid:c,instance:h,type:d,time:_}=Wo.get(u);Wo.delete(u),await Aa(e,f,c,h,d,_)}}function Aa(e,t,n,o,s,r){const i=Ge(t);if(!i)return;const l=st(o)||"Unknown Component",a=`${n}-${s}`,u=i.perfGroupIds.get(a);if(u){const f=u.groupId,c=u.time,h=r-c;e.addTimelineEvent({layerId:Tr,event:{time:Date.now(),data:{component:l,type:s,measure:"end",duration:{_custom:{type:"Duration",value:h,display:`${h} ms`}}},title:l,subtitle:s,groupId:f}})}else Wo.set(a,{app:t,uid:n,instance:o,type:s,time:r})}var Ca="component-event";function __(e){Bo&&(e.addTimelineLayer({id:"mouse",label:"Mouse",color:10768815}),["mousedown","mouseup","click","dblclick"].forEach(t=>{!q.timelineLayersState.recordingState||!q.timelineLayersState.mouseEventEnabled||window.addEventListener(t,async n=>{await e.addTimelineEvent({layerId:"mouse",event:{time:Date.now(),data:{type:t,x:n.clientX,y:n.clientY},title:t}})},{capture:!0,passive:!0})}),e.addTimelineLayer({id:"keyboard",label:"Keyboard",color:8475055}),["keyup","keydown","keypress"].forEach(t=>{window.addEventListener(t,async n=>{!q.timelineLayersState.recordingState||!q.timelineLayersState.keyboardEventEnabled||await e.addTimelineEvent({layerId:"keyboard",event:{time:Date.now(),data:{type:t,key:n.key,ctrlKey:n.ctrlKey,shiftKey:n.shiftKey,altKey:n.altKey,metaKey:n.metaKey},title:n.key}})},{capture:!0,passive:!0})}),e.addTimelineLayer({id:Ca,label:"Component events",color:5226637}),He.on.componentEmit(async(t,n,o,s)=>{if(!q.timelineLayersState.recordingState||!q.timelineLayersState.componentEventEnabled)return;const r=await Ge(t);if(!r)return;const i=`${r.id}:${n.uid}`,l=st(n)||"Unknown Component";e.addTimelineEvent({layerId:Ca,event:{time:Date.now(),data:{component:{_custom:{type:"component-definition",display:l}},event:o,params:s},title:o,subtitle:`by ${l}`,meta:{componentId:i}}})}),e.addTimelineLayer({id:"performance",label:Tr,color:4307050}),He.on.perfStart((t,n,o,s,r)=>{!q.timelineLayersState.recordingState||!q.timelineLayersState.performanceEventEnabled||h_(e,t,n,o,s,r)}),He.on.perfEnd((t,n,o,s,r)=>{!q.timelineLayersState.recordingState||!q.timelineLayersState.performanceEventEnabled||Aa(e,t,n,o,s,r)}))}w();var m_=10,qt=[];function g_(e){if(typeof window>"u")return;const t=window;if(e&&(t.$vm=e,qt[0]!==e)){qt.length>=m_&&qt.pop();for(let n=qt.length;n>0;n--)t[`$vm${n}`]=qt[n]=qt[n-1];t.$vm0=qt[0]=e}}var Xt="components";function v_(e){return[{id:Xt,label:"Components",app:e},o=>{o.addInspector({id:Xt,label:"Components",treeFilterPlaceholder:"Search components"}),__(o),o.on.getInspectorTree(async i=>{if(i.app===e&&i.inspectorId===Xt){const l=pn(se.value,i.instanceId);if(l){const a=new p_({filterText:i.filter,maxDepth:100,recursively:!1,api:o});i.rootNodes=await a.getComponentTree(l)}}}),o.on.getInspectorState(async i=>{var l;if(i.app===e&&i.inspectorId===Xt){const a=c_({instanceId:i.nodeId}),u=a.instance,f=(l=a.instance)==null?void 0:l.appContext.app,c={componentInstance:u,app:f,instanceData:a};Ye.hooks.callHookWith(h=>{h.forEach(d=>d(c))},"inspectComponent"),i.state=a,g_(u)}}),o.on.editInspectorState(async i=>{i.app===e&&i.inspectorId===Xt&&(vh(i),await o.sendInspectorState("components"))});const s=Pt(()=>{o.sendInspectorTree(Xt)},120),r=Pt(()=>{o.sendInspectorState(Xt)},120);He.on.componentAdded(async(i,l,a,u)=>{var f,c,h;if(q.highPerfModeEnabled||(h=(c=(f=i==null?void 0:i._instance)==null?void 0:f.type)==null?void 0:c.devtools)!=null&&h.hide||!i||typeof l!="number"&&!l||!u)return;const d=await lr({app:i,uid:l,instance:u}),_=await Ge(i);u&&(u.__VUE_DEVTOOLS_NEXT_UID__==null&&(u.__VUE_DEVTOOLS_NEXT_UID__=d),_!=null&&_.instanceMap.has(d)||(_==null||_.instanceMap.set(d,u),se.value.id===(_==null?void 0:_.id)&&(se.value.instanceMap=_.instanceMap))),_&&s()}),He.on.componentUpdated(async(i,l,a,u)=>{var f,c,h;if(q.highPerfModeEnabled||(h=(c=(f=i==null?void 0:i._instance)==null?void 0:f.type)==null?void 0:c.devtools)!=null&&h.hide||!i||typeof l!="number"&&!l||!u)return;const d=await lr({app:i,uid:l,instance:u}),_=await Ge(i);u&&(u.__VUE_DEVTOOLS_NEXT_UID__==null&&(u.__VUE_DEVTOOLS_NEXT_UID__=d),_!=null&&_.instanceMap.has(d)||(_==null||_.instanceMap.set(d,u),se.value.id===(_==null?void 0:_.id)&&(se.value.instanceMap=_.instanceMap))),_&&(s(),r())}),He.on.componentRemoved(async(i,l,a,u)=>{var f,c,h;if(q.highPerfModeEnabled||(h=(c=(f=i==null?void 0:i._instance)==null?void 0:f.type)==null?void 0:c.devtools)!=null&&h.hide||!i||typeof l!="number"&&!l||!u)return;const d=await Ge(i);if(!d)return;const _=await lr({app:i,uid:l,instance:u});d==null||d.instanceMap.delete(_),se.value.id===(d==null?void 0:d.id)&&(se.value.instanceMap=d.instanceMap),s()})}]}var Da,Pa;(Pa=(Da=O).__VUE_DEVTOOLS_KIT__REGISTERED_PLUGIN_APPS__)!=null||(Da.__VUE_DEVTOOLS_KIT__REGISTERED_PLUGIN_APPS__=new Set);function y_(e,t){return He.setupDevToolsPlugin(e,t)}function Ia(e,t){const[n,o]=e;if(n.app!==t)return;const s=new Fh({plugin:{setupFn:o,descriptor:n},ctx:Ye});n.packageName==="vuex"&&s.on.editInspectorState(r=>{s.sendInspectorState(r.inspectorId)}),o(s)}function E_(e){O.__VUE_DEVTOOLS_KIT__REGISTERED_PLUGIN_APPS__.delete(e)}function xr(e,t){O.__VUE_DEVTOOLS_KIT__REGISTERED_PLUGIN_APPS__.has(e)||q.highPerfModeEnabled&&!(t!=null&&t.inspectingComponent)||(O.__VUE_DEVTOOLS_KIT__REGISTERED_PLUGIN_APPS__.add(e),jn.forEach(n=>{Ia(n,e)}))}w(),w();var Kn="__VUE_DEVTOOLS_ROUTER__",_n="__VUE_DEVTOOLS_ROUTER_INFO__",ka,Ra;(Ra=(ka=O)[_n])!=null||(ka[_n]={currentRoute:null,routes:[]});var Na,Va;(Va=(Na=O)[Kn])!=null||(Na[Kn]={}),new Proxy(O[_n],{get(e,t){return O[_n][t]}}),new Proxy(O[Kn],{get(e,t){if(t==="value")return O[Kn]}});function b_(e){const t=new Map;return((e==null?void 0:e.getRoutes())||[]).filter(n=>!t.has(n.path)&&t.set(n.path,1))}function Or(e){return e.map(t=>{let{path:n,name:o,children:s,meta:r}=t;return s!=null&&s.length&&(s=Or(s)),{path:n,name:o,children:s,meta:r}})}function w_(e){if(e){const{fullPath:t,hash:n,href:o,path:s,name:r,matched:i,params:l,query:a}=e;return{fullPath:t,hash:n,href:o,path:s,name:r,params:l,query:a,matched:Or(i)}}return e}function La(e,t){function n(){var o;const s=(o=e.app)==null?void 0:o.config.globalProperties.$router,r=w_(s==null?void 0:s.currentRoute.value),i=Or(b_(s)),l=console.warn;console.warn=()=>{},O[_n]={currentRoute:r?Ml(r):{},routes:Ml(i)},O[Kn]=s,console.warn=l}n(),He.on.componentUpdated(Pt(()=>{var o;((o=t.value)==null?void 0:o.app)===e.app&&(n(),!q.highPerfModeEnabled&&Ye.hooks.callHook("routerInfoUpdated",{state:O[_n]}))},200))}function S_(e){return{async getInspectorTree(t){const n={...t,app:se.value.app,rootNodes:[]};return await new Promise(o=>{e.callHookWith(async s=>{await Promise.all(s.map(r=>r(n))),o()},"getInspectorTree")}),n.rootNodes},async getInspectorState(t){const n={...t,app:se.value.app,state:null},o={currentTab:`custom-inspector:${t.inspectorId}`};return await new Promise(s=>{e.callHookWith(async r=>{await Promise.all(r.map(i=>i(n,o))),s()},"getInspectorState")}),n.state},editInspectorState(t){const n=new Ql,o={...t,app:se.value.app,set:(s,r=t.path,i=t.state.value,l)=>{n.set(s,r,i,l||n.createDefaultSetCallback(t.state))}};e.callHookWith(s=>{s.forEach(r=>r(o))},"editInspectorState")},sendInspectorState(t){const n=jo(t);e.callHook("sendInspectorState",{inspectorId:t,plugin:{descriptor:n.descriptor,setupFn:()=>({})}})},inspectComponentInspector(){return uh()},cancelInspectComponentInspector(){return ah()},getComponentRenderCode(t){const n=pn(se.value,t);if(n)return(n==null?void 0:n.type)instanceof Function?n.type.toString():n.render.toString()},scrollToComponent(t){return ch({id:t})},openInEditor:Ih,getVueInspector:ph,toggleApp(t,n){const o=kt.value.find(s=>s.id===t);o&&(va(t),Er(o),La(o,se),sa(),xr(o.app,n))},inspectDOM(t){const n=pn(se.value,t);if(n){const[o]=Hn(n);o&&(O.__VUE_DEVTOOLS_INSPECT_DOM_TARGET__=o)}},updatePluginSettings(t,n,o){Nh(t,n,o)},getPluginSettings(t){return{options:Rh(t),values:ba(t)}}}}w();var Ma,$a;($a=(Ma=O).__VUE_DEVTOOLS_ENV__)!=null||(Ma.__VUE_DEVTOOLS_ENV__={vitePluginDetected:!1});function T_(){return O.__VUE_DEVTOOLS_ENV__}var Fa=Th(),Ua,Ba;(Ba=(Ua=O).__VUE_DEVTOOLS_KIT_CONTEXT__)!=null||(Ua.__VUE_DEVTOOLS_KIT_CONTEXT__={hooks:Fa,get state(){return{...q,activeAppRecordId:se.id,activeAppRecord:se.value,appRecords:kt.value}},api:S_(Fa)});var Ye=O.__VUE_DEVTOOLS_KIT_CONTEXT__;w();var x_=zp(Kp()),Ha,za,Rt=(za=(Ha=O).__VUE_DEVTOOLS_NEXT_APP_RECORD_INFO__)!=null?za:Ha.__VUE_DEVTOOLS_NEXT_APP_RECORD_INFO__={id:0,appIds:new Set};function O_(e,t){var n;return((n=e==null?void 0:e._component)==null?void 0:n.name)||`App ${t}`}function A_(e){var t,n,o,s;if(e._instance)return e._instance;if((n=(t=e._container)==null?void 0:t._vnode)!=null&&n.component)return(s=(o=e._container)==null?void 0:o._vnode)==null?void 0:s.component}function C_(e){const t=e.__VUE_DEVTOOLS_NEXT_APP_RECORD_ID__;t!=null&&(Rt.appIds.delete(t),Rt.id--)}function D_(e,t){if(e.__VUE_DEVTOOLS_NEXT_APP_RECORD_ID__!=null)return e.__VUE_DEVTOOLS_NEXT_APP_RECORD_ID__;let n=t??(Rt.id++).toString();if(t&&Rt.appIds.has(n)){let o=1;for(;Rt.appIds.has(`${t}_${o}`);)o++;n=`${t}_${o}`}return Rt.appIds.add(n),e.__VUE_DEVTOOLS_NEXT_APP_RECORD_ID__=n,n}function P_(e,t){const n=A_(e);if(n){Rt.id++;const o=O_(e,Rt.id.toString()),r={id:D_(e,(0,x_.default)(o)),name:o,types:t,instanceMap:new Map,perfGroupIds:new Map,rootInstance:n};e.__VUE_DEVTOOLS_NEXT_APP_RECORD__=r;const i=`${r.id}:root`;return r.instanceMap.set(i,r.rootInstance),r.rootInstance.__VUE_DEVTOOLS_NEXT_UID__=i,r}else return{}}function I_(){var e;br({vitePluginDetected:T_().vitePluginDetected});const t=((e=O.__VUE_DEVTOOLS_GLOBAL_HOOK__)==null?void 0:e.id)==="vue-devtools-next";if(O.__VUE_DEVTOOLS_GLOBAL_HOOK__&&t)return;const n=Lh();if(O.__VUE_DEVTOOLS_HOOK_REPLAY__)try{O.__VUE_DEVTOOLS_HOOK_REPLAY__.forEach(o=>o(n)),O.__VUE_DEVTOOLS_HOOK_REPLAY__=[]}catch(o){console.error("[vue-devtools] Error during hook replay",o)}n.once("init",o=>{O.__VUE_DEVTOOLS_VUE2_APP_DETECTED__=!0,console.log("%c[_____Vue DevTools v7 log_____]","color: red; font-bold: 600; font-size: 16px;"),console.log("%cVue DevTools v7 detected in your Vue2 project. v7 only supports Vue3 and will not work.","font-bold: 500; font-size: 14px;"),console.log("%cThe legacy version that supports both Vue 2 and Vue 3 has been moved to %c https://chromewebstore.google.com/detail/vuejs-devtools/iaajmlceplecbljialhhkmedjlpdblhp","font-size: 14px;","text-decoration: underline; cursor: pointer;font-size: 14px;"),console.log("%cPlease install and enable only the legacy version for your Vue2 app.","font-bold: 500; font-size: 14px;"),console.log("%c[_____Vue DevTools v7 log_____]","color: red; font-bold: 600; font-size: 16px;")}),He.on.setupDevtoolsPlugin((o,s)=>{var r;kh(o,s);const{app:i}=(r=se)!=null?r:{};o.settings&&wa(o.id,o.settings),i&&Ia([o,s],i)}),Wp(()=>{jn.filter(([s])=>s.id!=="components").forEach(([s,r])=>{n.emit("devtools-plugin:setup",s,r,{target:"legacy"})})}),He.on.vueAppInit(async(o,s,r)=>{const l={...P_(o,r),app:o,version:s};Ch(l),kt.value.length===1&&(Er(l),va(l.id),La(l,se),xr(l.app)),y_(...v_(l.app)),br({connected:!0}),n.apps.push(o)}),He.on.vueAppUnmount(async o=>{const s=kt.value.filter(r=>r.app!==o);s.length===0&&br({connected:!1}),Dh(o),C_(o),se.value.app===o&&(Er(s[0]),Ye.hooks.callHook("sendActiveAppUpdatedToClient")),O.__VUE_DEVTOOLS_GLOBAL_HOOK__.apps.splice(O.__VUE_DEVTOOLS_GLOBAL_HOOK__.apps.indexOf(o),1),E_(o)}),Mh(n),O.__VUE_DEVTOOLS_GLOBAL_HOOK__?wp||Object.assign(__VUE_DEVTOOLS_GLOBAL_HOOK__,n):Object.defineProperty(O,"__VUE_DEVTOOLS_GLOBAL_HOOK__",{get(){return n}})}w();function k_(e){q.highPerfModeEnabled=e??!q.highPerfModeEnabled,!e&&se.value&&xr(se.value.app)}w(),w(),w();function R_(e){q.devtoolsClientDetected={...q.devtoolsClientDetected,...e};const t=Object.values(q.devtoolsClientDetected).some(Boolean);k_(!t)}var ja,Ka;(Ka=(ja=O).__VUE_DEVTOOLS_UPDATE_CLIENT_DETECTED__)!=null||(ja.__VUE_DEVTOOLS_UPDATE_CLIENT_DETECTED__=R_),w(),w(),w(),w(),w(),w(),w();var N_=class{constructor(){this.keyToValue=new Map,this.valueToKey=new Map}set(e,t){this.keyToValue.set(e,t),this.valueToKey.set(t,e)}getByKey(e){return this.keyToValue.get(e)}getByValue(e){return this.valueToKey.get(e)}clear(){this.keyToValue.clear(),this.valueToKey.clear()}},Wa=class{constructor(e){this.generateIdentifier=e,this.kv=new N_}register(e,t){this.kv.getByValue(e)||(t||(t=this.generateIdentifier(e)),this.kv.set(t,e))}clear(){this.kv.clear()}getIdentifier(e){return this.kv.getByValue(e)}getValue(e){return this.kv.getByKey(e)}},V_=class extends Wa{constructor(){super(e=>e.name),this.classToAllowedProps=new Map}register(e,t){typeof t=="object"?(t.allowProps&&this.classToAllowedProps.set(e,t.allowProps),super.register(e,t.identifier)):super.register(e,t)}getAllowedProps(e){return this.classToAllowedProps.get(e)}};w(),w();function L_(e){if("values"in Object)return Object.values(e);const t=[];for(const n in e)e.hasOwnProperty(n)&&t.push(e[n]);return t}function M_(e,t){const n=L_(e);if("find"in n)return n.find(t);const o=n;for(let s=0;s<o.length;s++){const r=o[s];if(t(r))return r}}function mn(e,t){Object.entries(e).forEach(([n,o])=>t(o,n))}function Go(e,t){return e.indexOf(t)!==-1}function Ga(e,t){for(let n=0;n<e.length;n++){const o=e[n];if(t(o))return o}}var $_=class{constructor(){this.transfomers={}}register(e){this.transfomers[e.name]=e}findApplicable(e){return M_(this.transfomers,t=>t.isApplicable(e))}findByName(e){return this.transfomers[e]}};w(),w();var F_=e=>Object.prototype.toString.call(e).slice(8,-1),Ya=e=>typeof e>"u",U_=e=>e===null,Wn=e=>typeof e!="object"||e===null||e===Object.prototype?!1:Object.getPrototypeOf(e)===null?!0:Object.getPrototypeOf(e)===Object.prototype,Ar=e=>Wn(e)&&Object.keys(e).length===0,Nt=e=>Array.isArray(e),B_=e=>typeof e=="string",H_=e=>typeof e=="number"&&!isNaN(e),z_=e=>typeof e=="boolean",j_=e=>e instanceof RegExp,Gn=e=>e instanceof Map,Yn=e=>e instanceof Set,qa=e=>F_(e)==="Symbol",K_=e=>e instanceof Date&&!isNaN(e.valueOf()),W_=e=>e instanceof Error,Xa=e=>typeof e=="number"&&isNaN(e),G_=e=>z_(e)||U_(e)||Ya(e)||H_(e)||B_(e)||qa(e),Y_=e=>typeof e=="bigint",q_=e=>e===1/0||e===-1/0,X_=e=>ArrayBuffer.isView(e)&&!(e instanceof DataView),Z_=e=>e instanceof URL;w();var Za=e=>e.replace(/\./g,"\\."),Cr=e=>e.map(String).map(Za).join("."),qn=e=>{const t=[];let n="";for(let s=0;s<e.length;s++){let r=e.charAt(s);if(r==="\\"&&e.charAt(s+1)==="."){n+=".",s++;continue}if(r==="."){t.push(n),n="";continue}n+=r}const o=n;return t.push(o),t};w();function it(e,t,n,o){return{isApplicable:e,annotation:t,transform:n,untransform:o}}var Ja=[it(Ya,"undefined",()=>null,()=>{}),it(Y_,"bigint",e=>e.toString(),e=>typeof BigInt<"u"?BigInt(e):(console.error("Please add a BigInt polyfill."),e)),it(K_,"Date",e=>e.toISOString(),e=>new Date(e)),it(W_,"Error",(e,t)=>{const n={name:e.name,message:e.message};return t.allowedErrorProps.forEach(o=>{n[o]=e[o]}),n},(e,t)=>{const n=new Error(e.message);return n.name=e.name,n.stack=e.stack,t.allowedErrorProps.forEach(o=>{n[o]=e[o]}),n}),it(j_,"regexp",e=>""+e,e=>{const t=e.slice(1,e.lastIndexOf("/")),n=e.slice(e.lastIndexOf("/")+1);return new RegExp(t,n)}),it(Yn,"set",e=>[...e.values()],e=>new Set(e)),it(Gn,"map",e=>[...e.entries()],e=>new Map(e)),it(e=>Xa(e)||q_(e),"number",e=>Xa(e)?"NaN":e>0?"Infinity":"-Infinity",Number),it(e=>e===0&&1/e===-1/0,"number",()=>"-0",Number),it(Z_,"URL",e=>e.toString(),e=>new URL(e))];function Yo(e,t,n,o){return{isApplicable:e,annotation:t,transform:n,untransform:o}}var Qa=Yo((e,t)=>qa(e)?!!t.symbolRegistry.getIdentifier(e):!1,(e,t)=>["symbol",t.symbolRegistry.getIdentifier(e)],e=>e.description,(e,t,n)=>{const o=n.symbolRegistry.getValue(t[1]);if(!o)throw new Error("Trying to deserialize unknown symbol");return o}),J_=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array,Uint8ClampedArray].reduce((e,t)=>(e[t.name]=t,e),{}),eu=Yo(X_,e=>["typed-array",e.constructor.name],e=>[...e],(e,t)=>{const n=J_[t[1]];if(!n)throw new Error("Trying to deserialize unknown typed array");return new n(e)});function tu(e,t){return e!=null&&e.constructor?!!t.classRegistry.getIdentifier(e.constructor):!1}var nu=Yo(tu,(e,t)=>["class",t.classRegistry.getIdentifier(e.constructor)],(e,t)=>{const n=t.classRegistry.getAllowedProps(e.constructor);if(!n)return{...e};const o={};return n.forEach(s=>{o[s]=e[s]}),o},(e,t,n)=>{const o=n.classRegistry.getValue(t[1]);if(!o)throw new Error(`Trying to deserialize unknown class '${t[1]}' - check https://github.com/blitz-js/superjson/issues/116#issuecomment-773996564`);return Object.assign(Object.create(o.prototype),e)}),ou=Yo((e,t)=>!!t.customTransformerRegistry.findApplicable(e),(e,t)=>["custom",t.customTransformerRegistry.findApplicable(e).name],(e,t)=>t.customTransformerRegistry.findApplicable(e).serialize(e),(e,t,n)=>{const o=n.customTransformerRegistry.findByName(t[1]);if(!o)throw new Error("Trying to deserialize unknown custom value");return o.deserialize(e)}),Q_=[nu,Qa,ou,eu],su=(e,t)=>{const n=Ga(Q_,s=>s.isApplicable(e,t));if(n)return{value:n.transform(e,t),type:n.annotation(e,t)};const o=Ga(Ja,s=>s.isApplicable(e,t));if(o)return{value:o.transform(e,t),type:o.annotation}},ru={};Ja.forEach(e=>{ru[e.annotation]=e});var em=(e,t,n)=>{if(Nt(t))switch(t[0]){case"symbol":return Qa.untransform(e,t,n);case"class":return nu.untransform(e,t,n);case"custom":return ou.untransform(e,t,n);case"typed-array":return eu.untransform(e,t,n);default:throw new Error("Unknown transformation: "+t)}else{const o=ru[t];if(!o)throw new Error("Unknown transformation: "+t);return o.untransform(e,n)}};w();var gn=(e,t)=>{if(t>e.size)throw new Error("index out of bounds");const n=e.keys();for(;t>0;)n.next(),t--;return n.next().value};function iu(e){if(Go(e,"__proto__"))throw new Error("__proto__ is not allowed as a property");if(Go(e,"prototype"))throw new Error("prototype is not allowed as a property");if(Go(e,"constructor"))throw new Error("constructor is not allowed as a property")}var tm=(e,t)=>{iu(t);for(let n=0;n<t.length;n++){const o=t[n];if(Yn(e))e=gn(e,+o);else if(Gn(e)){const s=+o,r=+t[++n]==0?"key":"value",i=gn(e,s);switch(r){case"key":e=i;break;case"value":e=e.get(i);break}}else e=e[o]}return e},Dr=(e,t,n)=>{if(iu(t),t.length===0)return n(e);let o=e;for(let r=0;r<t.length-1;r++){const i=t[r];if(Nt(o)){const l=+i;o=o[l]}else if(Wn(o))o=o[i];else if(Yn(o)){const l=+i;o=gn(o,l)}else if(Gn(o)){if(r===t.length-2)break;const a=+i,u=+t[++r]==0?"key":"value",f=gn(o,a);switch(u){case"key":o=f;break;case"value":o=o.get(f);break}}}const s=t[t.length-1];if(Nt(o)?o[+s]=n(o[+s]):Wn(o)&&(o[s]=n(o[s])),Yn(o)){const r=gn(o,+s),i=n(r);r!==i&&(o.delete(r),o.add(i))}if(Gn(o)){const r=+t[t.length-2],i=gn(o,r);switch(+s==0?"key":"value"){case"key":{const a=n(i);o.set(a,o.get(i)),a!==i&&o.delete(i);break}case"value":{o.set(i,n(o.get(i)));break}}}return e};function Pr(e,t,n=[]){if(!e)return;if(!Nt(e)){mn(e,(r,i)=>Pr(r,t,[...n,...qn(i)]));return}const[o,s]=e;s&&mn(s,(r,i)=>{Pr(r,t,[...n,...qn(i)])}),t(o,n)}function nm(e,t,n){return Pr(t,(o,s)=>{e=Dr(e,s,r=>em(r,o,n))}),e}function om(e,t){function n(o,s){const r=tm(e,qn(s));o.map(qn).forEach(i=>{e=Dr(e,i,()=>r)})}if(Nt(t)){const[o,s]=t;o.forEach(r=>{e=Dr(e,qn(r),()=>e)}),s&&mn(s,n)}else mn(t,n);return e}var sm=(e,t)=>Wn(e)||Nt(e)||Gn(e)||Yn(e)||tu(e,t);function rm(e,t,n){const o=n.get(e);o?o.push(t):n.set(e,[t])}function im(e,t){const n={};let o;return e.forEach(s=>{if(s.length<=1)return;t||(s=s.map(l=>l.map(String)).sort((l,a)=>l.length-a.length));const[r,...i]=s;r.length===0?o=i.map(Cr):n[Cr(r)]=i.map(Cr)}),o?Ar(n)?[o]:[o,n]:Ar(n)?void 0:n}var lu=(e,t,n,o,s=[],r=[],i=new Map)=>{var l;const a=G_(e);if(!a){rm(e,s,t);const _=i.get(e);if(_)return o?{transformedValue:null}:_}if(!sm(e,n)){const _=su(e,n),v=_?{transformedValue:_.value,annotations:[_.type]}:{transformedValue:e};return a||i.set(e,v),v}if(Go(r,e))return{transformedValue:null};const u=su(e,n),f=(l=u==null?void 0:u.value)!=null?l:e,c=Nt(f)?[]:{},h={};mn(f,(_,v)=>{if(v==="__proto__"||v==="constructor"||v==="prototype")throw new Error(`Detected property ${v}. This is a prototype pollution risk, please remove it from your object.`);const g=lu(_,t,n,o,[...s,v],[...r,e],i);c[v]=g.transformedValue,Nt(g.annotations)?h[v]=g.annotations:Wn(g.annotations)&&mn(g.annotations,(y,T)=>{h[Za(v)+"."+T]=y})});const d=Ar(h)?{transformedValue:c,annotations:u?[u.type]:void 0}:{transformedValue:c,annotations:u?[u.type,h]:h};return a||i.set(e,d),d};w(),w();function au(e){return Object.prototype.toString.call(e).slice(8,-1)}function uu(e){return au(e)==="Array"}function lm(e){if(au(e)!=="Object")return!1;const t=Object.getPrototypeOf(e);return!!t&&t.constructor===Object&&t===Object.prototype}function am(e,t,n,o,s){const r={}.propertyIsEnumerable.call(o,t)?"enumerable":"nonenumerable";r==="enumerable"&&(e[t]=n),s&&r==="nonenumerable"&&Object.defineProperty(e,t,{value:n,enumerable:!1,writable:!0,configurable:!0})}function Ir(e,t={}){if(uu(e))return e.map(s=>Ir(s,t));if(!lm(e))return e;const n=Object.getOwnPropertyNames(e),o=Object.getOwnPropertySymbols(e);return[...n,...o].reduce((s,r)=>{if(uu(t.props)&&!t.props.includes(r))return s;const i=e[r],l=Ir(i,t);return am(s,r,l,e,t.nonenumerable),s},{})}var ce=class{constructor({dedupe:e=!1}={}){this.classRegistry=new V_,this.symbolRegistry=new Wa(t=>{var n;return(n=t.description)!=null?n:""}),this.customTransformerRegistry=new $_,this.allowedErrorProps=[],this.dedupe=e}serialize(e){const t=new Map,n=lu(e,t,this,this.dedupe),o={json:n.transformedValue};n.annotations&&(o.meta={...o.meta,values:n.annotations});const s=im(t,this.dedupe);return s&&(o.meta={...o.meta,referentialEqualities:s}),o}deserialize(e){const{json:t,meta:n}=e;let o=Ir(t);return n!=null&&n.values&&(o=nm(o,n.values,this)),n!=null&&n.referentialEqualities&&(o=om(o,n.referentialEqualities)),o}stringify(e){return JSON.stringify(this.serialize(e))}parse(e){return this.deserialize(JSON.parse(e))}registerClass(e,t){this.classRegistry.register(e,t)}registerSymbol(e,t){this.symbolRegistry.register(e,t)}registerCustom(e,t){this.customTransformerRegistry.register({name:t,...e})}allowErrorProps(...e){this.allowedErrorProps.push(...e)}};ce.defaultInstance=new ce,ce.serialize=ce.defaultInstance.serialize.bind(ce.defaultInstance),ce.deserialize=ce.defaultInstance.deserialize.bind(ce.defaultInstance),ce.stringify=ce.defaultInstance.stringify.bind(ce.defaultInstance),ce.parse=ce.defaultInstance.parse.bind(ce.defaultInstance),ce.registerClass=ce.defaultInstance.registerClass.bind(ce.defaultInstance),ce.registerSymbol=ce.defaultInstance.registerSymbol.bind(ce.defaultInstance),ce.registerCustom=ce.defaultInstance.registerCustom.bind(ce.defaultInstance),ce.allowErrorProps=ce.defaultInstance.allowErrorProps.bind(ce.defaultInstance),w(),w(),w(),w(),w(),w(),w(),w(),w(),w(),w(),w(),w(),w();var um="iframe:server-context";function cm(e){O[um]=e}w(),w(),w(),w(),w(),w(),w(),w(),w();var cu,fu;(fu=(cu=O).__VUE_DEVTOOLS_KIT_MESSAGE_CHANNELS__)!=null||(cu.__VUE_DEVTOOLS_KIT_MESSAGE_CHANNELS__=[]);var du,pu;(pu=(du=O).__VUE_DEVTOOLS_KIT_RPC_CLIENT__)!=null||(du.__VUE_DEVTOOLS_KIT_RPC_CLIENT__=null);var hu,_u;(_u=(hu=O).__VUE_DEVTOOLS_KIT_RPC_SERVER__)!=null||(hu.__VUE_DEVTOOLS_KIT_RPC_SERVER__=null);var mu,gu;(gu=(mu=O).__VUE_DEVTOOLS_KIT_VITE_RPC_CLIENT__)!=null||(mu.__VUE_DEVTOOLS_KIT_VITE_RPC_CLIENT__=null);var vu,yu;(yu=(vu=O).__VUE_DEVTOOLS_KIT_VITE_RPC_SERVER__)!=null||(vu.__VUE_DEVTOOLS_KIT_VITE_RPC_SERVER__=null);var Eu,bu;(bu=(Eu=O).__VUE_DEVTOOLS_KIT_BROADCAST_RPC_SERVER__)!=null||(Eu.__VUE_DEVTOOLS_KIT_BROADCAST_RPC_SERVER__=null);function fm(){return O.__VUE_DEVTOOLS_KIT_RPC_CLIENT__}function wu(){return O.__VUE_DEVTOOLS_KIT_RPC_SERVER__}function dm(){return O.__VUE_DEVTOOLS_KIT_VITE_RPC_CLIENT__}w(),w(),w(),w(),w(),w(),w();var Su={hook:He,init:()=>{I_()},get ctx(){return Ye},get api(){return Ye.api}};function pm(){var e;return(e=O.__VUE_DEVTOOLS_CLIENT_URL__)!=null?e:(()=>{if(Bo){const t=document.querySelector("meta[name=__VUE_DEVTOOLS_CLIENT_URL__]");if(t)return t.getAttribute("content")}return""})()}function kr(e,t={},n){for(const o in e){const s=e[o],r=n?`${n}:${o}`:o;typeof s=="object"&&s!==null?kr(s,t,r):typeof s=="function"&&(t[r]=s)}return t}var hm={run:e=>e()},_m=()=>hm,Tu=typeof console.createTask<"u"?console.createTask:_m;function mm(e,t){const n=t.shift(),o=Tu(n);return e.reduce((s,r)=>s.then(()=>o.run(()=>r(...t))),Promise.resolve())}function gm(e,t){const n=t.shift(),o=Tu(n);return Promise.all(e.map(s=>o.run(()=>s(...t))))}function Rr(e,t){for(const n of[...e])n(t)}var vm=class{constructor(){this._hooks={},this._before=void 0,this._after=void 0,this._deprecatedMessages=void 0,this._deprecatedHooks={},this.hook=this.hook.bind(this),this.callHook=this.callHook.bind(this),this.callHookWith=this.callHookWith.bind(this)}hook(e,t,n={}){if(!e||typeof t!="function")return()=>{};const o=e;let s;for(;this._deprecatedHooks[e];)s=this._deprecatedHooks[e],e=s.to;if(s&&!n.allowDeprecated){let r=s.message;r||(r=`${o} hook has been deprecated`+(s.to?`, please use ${s.to}`:"")),this._deprecatedMessages||(this._deprecatedMessages=new Set),this._deprecatedMessages.has(r)||(console.warn(r),this._deprecatedMessages.add(r))}if(!t.name)try{Object.defineProperty(t,"name",{get:()=>"_"+e.replace(/\W+/g,"_")+"_hook_cb",configurable:!0})}catch{}return this._hooks[e]=this._hooks[e]||[],this._hooks[e].push(t),()=>{t&&(this.removeHook(e,t),t=void 0)}}hookOnce(e,t){let n,o=(...s)=>(typeof n=="function"&&n(),n=void 0,o=void 0,t(...s));return n=this.hook(e,o),n}removeHook(e,t){if(this._hooks[e]){const n=this._hooks[e].indexOf(t);n!==-1&&this._hooks[e].splice(n,1),this._hooks[e].length===0&&delete this._hooks[e]}}deprecateHook(e,t){this._deprecatedHooks[e]=typeof t=="string"?{to:t}:t;const n=this._hooks[e]||[];delete this._hooks[e];for(const o of n)this.hook(e,o)}deprecateHooks(e){Object.assign(this._deprecatedHooks,e);for(const t in e)this.deprecateHook(t,e[t])}addHooks(e){const t=kr(e),n=Object.keys(t).map(o=>this.hook(o,t[o]));return()=>{for(const o of n.splice(0,n.length))o()}}removeHooks(e){const t=kr(e);for(const n in t)this.removeHook(n,t[n])}removeAllHooks(){for(const e in this._hooks)delete this._hooks[e]}callHook(e,...t){return t.unshift(e),this.callHookWith(mm,e,...t)}callHookParallel(e,...t){return t.unshift(e),this.callHookWith(gm,e,...t)}callHookWith(e,t,...n){const o=this._before||this._after?{name:t,args:n,context:{}}:void 0;this._before&&Rr(this._before,o);const s=e(t in this._hooks?[...this._hooks[t]]:[],n);return s instanceof Promise?s.finally(()=>{this._after&&o&&Rr(this._after,o)}):(this._after&&o&&Rr(this._after,o),s)}beforeEach(e){return this._before=this._before||[],this._before.push(e),()=>{if(this._before!==void 0){const t=this._before.indexOf(e);t!==-1&&this._before.splice(t,1)}}}afterEach(e){return this._after=this._after||[],this._after.push(e),()=>{if(this._after!==void 0){const t=this._after.indexOf(e);t!==-1&&this._after.splice(t,1)}}}};function xu(){return new vm}xu(),new Proxy({value:{},functions:{}},{get(e,t){const n=fm();if(t==="value")return n;if(t==="functions")return n.$functions}});var Ou=new Proxy({value:{},functions:{}},{get(e,t){const n=wu();if(t==="value")return n;if(t==="functions")return n.functions}});function ym(e){let t=null;const n=120;function o(){Ou.value.clients.length>0&&(e(),clearTimeout(t))}t=setInterval(()=>{o()},n)}xu(),new Proxy({value:{},functions:{}},{get(e,t){const n=dm();if(t==="value")return n;if(t==="functions")return n==null?void 0:n.$functions}});const Em=["top","right","bottom","left"],Au=["start","end"],Cu=Em.reduce((e,t)=>e.concat(t,t+"-"+Au[0],t+"-"+Au[1]),[]),Xn=Math.min,Zt=Math.max,bm={left:"right",right:"left",bottom:"top",top:"bottom"},wm={start:"end",end:"start"};function Nr(e,t,n){return Zt(e,Xn(t,n))}function Jt(e,t){return typeof e=="function"?e(t):e}function lt(e){return e.split("-")[0]}function qe(e){return e.split("-")[1]}function Du(e){return e==="x"?"y":"x"}function Vr(e){return e==="y"?"height":"width"}function Qt(e){return["top","bottom"].includes(lt(e))?"y":"x"}function Lr(e){return Du(Qt(e))}function Pu(e,t,n){n===void 0&&(n=!1);const o=qe(e),s=Lr(e),r=Vr(s);let i=s==="x"?o===(n?"end":"start")?"right":"left":o==="start"?"bottom":"top";return t.reference[r]>t.floating[r]&&(i=Xo(i)),[i,Xo(i)]}function Sm(e){const t=Xo(e);return[qo(e),t,qo(t)]}function qo(e){return e.replace(/start|end/g,t=>wm[t])}function Tm(e,t,n){const o=["left","right"],s=["right","left"],r=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return n?t?s:o:t?o:s;case"left":case"right":return t?r:i;default:return[]}}function xm(e,t,n,o){const s=qe(e);let r=Tm(lt(e),n==="start",o);return s&&(r=r.map(i=>i+"-"+s),t&&(r=r.concat(r.map(qo)))),r}function Xo(e){return e.replace(/left|right|bottom|top/g,t=>bm[t])}function Om(e){return{top:0,right:0,bottom:0,left:0,...e}}function Iu(e){return typeof e!="number"?Om(e):{top:e,right:e,bottom:e,left:e}}function Zn(e){const{x:t,y:n,width:o,height:s}=e;return{width:o,height:s,top:n,left:t,right:t+o,bottom:n+s,x:t,y:n}}function ku(e,t,n){let{reference:o,floating:s}=e;const r=Qt(t),i=Lr(t),l=Vr(i),a=lt(t),u=r==="y",f=o.x+o.width/2-s.width/2,c=o.y+o.height/2-s.height/2,h=o[l]/2-s[l]/2;let d;switch(a){case"top":d={x:f,y:o.y-s.height};break;case"bottom":d={x:f,y:o.y+o.height};break;case"right":d={x:o.x+o.width,y:c};break;case"left":d={x:o.x-s.width,y:c};break;default:d={x:o.x,y:o.y}}switch(qe(t)){case"start":d[i]-=h*(n&&u?-1:1);break;case"end":d[i]+=h*(n&&u?-1:1);break}return d}const Am=async(e,t,n)=>{const{placement:o="bottom",strategy:s="absolute",middleware:r=[],platform:i}=n,l=r.filter(Boolean),a=await(i.isRTL==null?void 0:i.isRTL(t));let u=await i.getElementRects({reference:e,floating:t,strategy:s}),{x:f,y:c}=ku(u,o,a),h=o,d={},_=0;for(let v=0;v<l.length;v++){const{name:g,fn:y}=l[v],{x:T,y:P,data:b,reset:I}=await y({x:f,y:c,initialPlacement:o,placement:h,strategy:s,middlewareData:d,rects:u,platform:i,elements:{reference:e,floating:t}});f=T??f,c=P??c,d={...d,[g]:{...d[g],...b}},I&&_<=50&&(_++,typeof I=="object"&&(I.placement&&(h=I.placement),I.rects&&(u=I.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:s}):I.rects),{x:f,y:c}=ku(u,h,a)),v=-1)}return{x:f,y:c,placement:h,strategy:s,middlewareData:d}};async function Zo(e,t){var n;t===void 0&&(t={});const{x:o,y:s,platform:r,rects:i,elements:l,strategy:a}=e,{boundary:u="clippingAncestors",rootBoundary:f="viewport",elementContext:c="floating",altBoundary:h=!1,padding:d=0}=Jt(t,e),_=Iu(d),g=l[h?c==="floating"?"reference":"floating":c],y=Zn(await r.getClippingRect({element:(n=await(r.isElement==null?void 0:r.isElement(g)))==null||n?g:g.contextElement||await(r.getDocumentElement==null?void 0:r.getDocumentElement(l.floating)),boundary:u,rootBoundary:f,strategy:a})),T=c==="floating"?{x:o,y:s,width:i.floating.width,height:i.floating.height}:i.reference,P=await(r.getOffsetParent==null?void 0:r.getOffsetParent(l.floating)),b=await(r.isElement==null?void 0:r.isElement(P))?await(r.getScale==null?void 0:r.getScale(P))||{x:1,y:1}:{x:1,y:1},I=Zn(r.convertOffsetParentRelativeRectToViewportRelativeRect?await r.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:T,offsetParent:P,strategy:a}):T);return{top:(y.top-I.top+_.top)/b.y,bottom:(I.bottom-y.bottom+_.bottom)/b.y,left:(y.left-I.left+_.left)/b.x,right:(I.right-y.right+_.right)/b.x}}const Cm=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:o,placement:s,rects:r,platform:i,elements:l,middlewareData:a}=t,{element:u,padding:f=0}=Jt(e,t)||{};if(u==null)return{};const c=Iu(f),h={x:n,y:o},d=Lr(s),_=Vr(d),v=await i.getDimensions(u),g=d==="y",y=g?"top":"left",T=g?"bottom":"right",P=g?"clientHeight":"clientWidth",b=r.reference[_]+r.reference[d]-h[d]-r.floating[_],I=h[d]-r.reference[d],F=await(i.getOffsetParent==null?void 0:i.getOffsetParent(u));let U=F?F[P]:0;(!U||!await(i.isElement==null?void 0:i.isElement(F)))&&(U=l.floating[P]||r.floating[_]);const z=b/2-I/2,G=U/2-v[_]/2-1,D=Xn(c[y],G),N=Xn(c[T],G),$=D,j=U-v[_]-N,J=U/2-v[_]/2+z,ve=Nr($,J,j),X=!a.arrow&&qe(s)!=null&&J!==ve&&r.reference[_]/2-(J<$?D:N)-v[_]/2<0,L=X?J<$?J-$:J-j:0;return{[d]:h[d]+L,data:{[d]:ve,centerOffset:J-ve-L,...X&&{alignmentOffset:L}},reset:X}}});function Dm(e,t,n){return(e?[...n.filter(s=>qe(s)===e),...n.filter(s=>qe(s)!==e)]:n.filter(s=>lt(s)===s)).filter(s=>e?qe(s)===e||(t?qo(s)!==s:!1):!0)}const Pm=function(e){return e===void 0&&(e={}),{name:"autoPlacement",options:e,async fn(t){var n,o,s;const{rects:r,middlewareData:i,placement:l,platform:a,elements:u}=t,{crossAxis:f=!1,alignment:c,allowedPlacements:h=Cu,autoAlignment:d=!0,..._}=Jt(e,t),v=c!==void 0||h===Cu?Dm(c||null,d,h):h,g=await Zo(t,_),y=((n=i.autoPlacement)==null?void 0:n.index)||0,T=v[y];if(T==null)return{};const P=Pu(T,r,await(a.isRTL==null?void 0:a.isRTL(u.floating)));if(l!==T)return{reset:{placement:v[0]}};const b=[g[lt(T)],g[P[0]],g[P[1]]],I=[...((o=i.autoPlacement)==null?void 0:o.overflows)||[],{placement:T,overflows:b}],F=v[y+1];if(F)return{data:{index:y+1,overflows:I},reset:{placement:F}};const U=I.map(D=>{const N=qe(D.placement);return[D.placement,N&&f?D.overflows.slice(0,2).reduce(($,j)=>$+j,0):D.overflows[0],D.overflows]}).sort((D,N)=>D[1]-N[1]),G=((s=U.filter(D=>D[2].slice(0,qe(D[0])?2:3).every(N=>N<=0))[0])==null?void 0:s[0])||U[0][0];return G!==l?{data:{index:y+1,overflows:I},reset:{placement:G}}:{}}}},Im=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,o;const{placement:s,middlewareData:r,rects:i,initialPlacement:l,platform:a,elements:u}=t,{mainAxis:f=!0,crossAxis:c=!0,fallbackPlacements:h,fallbackStrategy:d="bestFit",fallbackAxisSideDirection:_="none",flipAlignment:v=!0,...g}=Jt(e,t);if((n=r.arrow)!=null&&n.alignmentOffset)return{};const y=lt(s),T=Qt(l),P=lt(l)===l,b=await(a.isRTL==null?void 0:a.isRTL(u.floating)),I=h||(P||!v?[Xo(l)]:Sm(l)),F=_!=="none";!h&&F&&I.push(...xm(l,v,_,b));const U=[l,...I],z=await Zo(t,g),G=[];let D=((o=r.flip)==null?void 0:o.overflows)||[];if(f&&G.push(z[y]),c){const J=Pu(s,i,b);G.push(z[J[0]],z[J[1]])}if(D=[...D,{placement:s,overflows:G}],!G.every(J=>J<=0)){var N,$;const J=(((N=r.flip)==null?void 0:N.index)||0)+1,ve=U[J];if(ve)return{data:{index:J,overflows:D},reset:{placement:ve}};let X=($=D.filter(L=>L.overflows[0]<=0).sort((L,Z)=>L.overflows[1]-Z.overflows[1])[0])==null?void 0:$.placement;if(!X)switch(d){case"bestFit":{var j;const L=(j=D.filter(Z=>{if(F){const ae=Qt(Z.placement);return ae===T||ae==="y"}return!0}).map(Z=>[Z.placement,Z.overflows.filter(ae=>ae>0).reduce((ae,Me)=>ae+Me,0)]).sort((Z,ae)=>Z[1]-ae[1])[0])==null?void 0:j[0];L&&(X=L);break}case"initialPlacement":X=l;break}if(s!==X)return{reset:{placement:X}}}return{}}}};async function km(e,t){const{placement:n,platform:o,elements:s}=e,r=await(o.isRTL==null?void 0:o.isRTL(s.floating)),i=lt(n),l=qe(n),a=Qt(n)==="y",u=["left","top"].includes(i)?-1:1,f=r&&a?-1:1,c=Jt(t,e);let{mainAxis:h,crossAxis:d,alignmentAxis:_}=typeof c=="number"?{mainAxis:c,crossAxis:0,alignmentAxis:null}:{mainAxis:c.mainAxis||0,crossAxis:c.crossAxis||0,alignmentAxis:c.alignmentAxis};return l&&typeof _=="number"&&(d=l==="end"?_*-1:_),a?{x:d*f,y:h*u}:{x:h*u,y:d*f}}const Rm=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,o;const{x:s,y:r,placement:i,middlewareData:l}=t,a=await km(t,e);return i===((n=l.offset)==null?void 0:n.placement)&&(o=l.arrow)!=null&&o.alignmentOffset?{}:{x:s+a.x,y:r+a.y,data:{...a,placement:i}}}}},Nm=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:o,placement:s}=t,{mainAxis:r=!0,crossAxis:i=!1,limiter:l={fn:g=>{let{x:y,y:T}=g;return{x:y,y:T}}},...a}=Jt(e,t),u={x:n,y:o},f=await Zo(t,a),c=Qt(lt(s)),h=Du(c);let d=u[h],_=u[c];if(r){const g=h==="y"?"top":"left",y=h==="y"?"bottom":"right",T=d+f[g],P=d-f[y];d=Nr(T,d,P)}if(i){const g=c==="y"?"top":"left",y=c==="y"?"bottom":"right",T=_+f[g],P=_-f[y];_=Nr(T,_,P)}const v=l.fn({...t,[h]:d,[c]:_});return{...v,data:{x:v.x-n,y:v.y-o,enabled:{[h]:r,[c]:i}}}}}},Vm=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,o;const{placement:s,rects:r,platform:i,elements:l}=t,{apply:a=()=>{},...u}=Jt(e,t),f=await Zo(t,u),c=lt(s),h=qe(s),d=Qt(s)==="y",{width:_,height:v}=r.floating;let g,y;c==="top"||c==="bottom"?(g=c,y=h===(await(i.isRTL==null?void 0:i.isRTL(l.floating))?"start":"end")?"left":"right"):(y=c,g=h==="end"?"top":"bottom");const T=v-f.top-f.bottom,P=_-f.left-f.right,b=Xn(v-f[g],T),I=Xn(_-f[y],P),F=!t.middlewareData.shift;let U=b,z=I;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(z=P),(o=t.middlewareData.shift)!=null&&o.enabled.y&&(U=T),F&&!h){const D=Zt(f.left,0),N=Zt(f.right,0),$=Zt(f.top,0),j=Zt(f.bottom,0);d?z=_-2*(D!==0||N!==0?D+N:Zt(f.left,f.right)):U=v-2*($!==0||j!==0?$+j:Zt(f.top,f.bottom))}await a({...t,availableWidth:z,availableHeight:U});const G=await i.getDimensions(l.floating);return _!==G.width||v!==G.height?{reset:{rects:!0}}:{}}}};function ze(e){var t;return((t=e.ownerDocument)==null?void 0:t.defaultView)||window}function at(e){return ze(e).getComputedStyle(e)}const Ru=Math.min,Jn=Math.max,Jo=Math.round;function Nu(e){const t=at(e);let n=parseFloat(t.width),o=parseFloat(t.height);const s=e.offsetWidth,r=e.offsetHeight,i=Jo(n)!==s||Jo(o)!==r;return i&&(n=s,o=r),{width:n,height:o,fallback:i}}function Vt(e){return Lu(e)?(e.nodeName||"").toLowerCase():""}let Qo;function Vu(){if(Qo)return Qo;const e=navigator.userAgentData;return e&&Array.isArray(e.brands)?(Qo=e.brands.map(t=>t.brand+"/"+t.version).join(" "),Qo):navigator.userAgent}function ut(e){return e instanceof ze(e).HTMLElement}function Lt(e){return e instanceof ze(e).Element}function Lu(e){return e instanceof ze(e).Node}function Mu(e){return typeof ShadowRoot>"u"?!1:e instanceof ze(e).ShadowRoot||e instanceof ShadowRoot}function es(e){const{overflow:t,overflowX:n,overflowY:o,display:s}=at(e);return/auto|scroll|overlay|hidden|clip/.test(t+o+n)&&!["inline","contents"].includes(s)}function Lm(e){return["table","td","th"].includes(Vt(e))}function Mr(e){const t=/firefox/i.test(Vu()),n=at(e),o=n.backdropFilter||n.WebkitBackdropFilter;return n.transform!=="none"||n.perspective!=="none"||!!o&&o!=="none"||t&&n.willChange==="filter"||t&&!!n.filter&&n.filter!=="none"||["transform","perspective"].some(s=>n.willChange.includes(s))||["paint","layout","strict","content"].some(s=>{const r=n.contain;return r!=null&&r.includes(s)})}function $u(){return!/^((?!chrome|android).)*safari/i.test(Vu())}function $r(e){return["html","body","#document"].includes(Vt(e))}function Fu(e){return Lt(e)?e:e.contextElement}const Uu={x:1,y:1};function vn(e){const t=Fu(e);if(!ut(t))return Uu;const n=t.getBoundingClientRect(),{width:o,height:s,fallback:r}=Nu(t);let i=(r?Jo(n.width):n.width)/o,l=(r?Jo(n.height):n.height)/s;return i&&Number.isFinite(i)||(i=1),l&&Number.isFinite(l)||(l=1),{x:i,y:l}}function Qn(e,t,n,o){var s,r;t===void 0&&(t=!1),n===void 0&&(n=!1);const i=e.getBoundingClientRect(),l=Fu(e);let a=Uu;t&&(o?Lt(o)&&(a=vn(o)):a=vn(e));const u=l?ze(l):window,f=!$u()&&n;let c=(i.left+(f&&((s=u.visualViewport)==null?void 0:s.offsetLeft)||0))/a.x,h=(i.top+(f&&((r=u.visualViewport)==null?void 0:r.offsetTop)||0))/a.y,d=i.width/a.x,_=i.height/a.y;if(l){const v=ze(l),g=o&&Lt(o)?ze(o):o;let y=v.frameElement;for(;y&&o&&g!==v;){const T=vn(y),P=y.getBoundingClientRect(),b=getComputedStyle(y);P.x+=(y.clientLeft+parseFloat(b.paddingLeft))*T.x,P.y+=(y.clientTop+parseFloat(b.paddingTop))*T.y,c*=T.x,h*=T.y,d*=T.x,_*=T.y,c+=P.x,h+=P.y,y=ze(y).frameElement}}return{width:d,height:_,top:h,right:c+d,bottom:h+_,left:c,x:c,y:h}}function Mt(e){return((Lu(e)?e.ownerDocument:e.document)||window.document).documentElement}function ts(e){return Lt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function Bu(e){return Qn(Mt(e)).left+ts(e).scrollLeft}function eo(e){if(Vt(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Mu(e)&&e.host||Mt(e);return Mu(t)?t.host:t}function Hu(e){const t=eo(e);return $r(t)?t.ownerDocument.body:ut(t)&&es(t)?t:Hu(t)}function ns(e,t){var n;t===void 0&&(t=[]);const o=Hu(e),s=o===((n=e.ownerDocument)==null?void 0:n.body),r=ze(o);return s?t.concat(r,r.visualViewport||[],es(o)?o:[]):t.concat(o,ns(o))}function zu(e,t,n){return t==="viewport"?Zn(function(o,s){const r=ze(o),i=Mt(o),l=r.visualViewport;let a=i.clientWidth,u=i.clientHeight,f=0,c=0;if(l){a=l.width,u=l.height;const h=$u();(h||!h&&s==="fixed")&&(f=l.offsetLeft,c=l.offsetTop)}return{width:a,height:u,x:f,y:c}}(e,n)):Lt(t)?Zn(function(o,s){const r=Qn(o,!0,s==="fixed"),i=r.top+o.clientTop,l=r.left+o.clientLeft,a=ut(o)?vn(o):{x:1,y:1};return{width:o.clientWidth*a.x,height:o.clientHeight*a.y,x:l*a.x,y:i*a.y}}(t,n)):Zn(function(o){const s=Mt(o),r=ts(o),i=o.ownerDocument.body,l=Jn(s.scrollWidth,s.clientWidth,i.scrollWidth,i.clientWidth),a=Jn(s.scrollHeight,s.clientHeight,i.scrollHeight,i.clientHeight);let u=-r.scrollLeft+Bu(o);const f=-r.scrollTop;return at(i).direction==="rtl"&&(u+=Jn(s.clientWidth,i.clientWidth)-l),{width:l,height:a,x:u,y:f}}(Mt(e)))}function ju(e){return ut(e)&&at(e).position!=="fixed"?e.offsetParent:null}function Ku(e){const t=ze(e);let n=ju(e);for(;n&&Lm(n)&&at(n).position==="static";)n=ju(n);return n&&(Vt(n)==="html"||Vt(n)==="body"&&at(n).position==="static"&&!Mr(n))?t:n||function(o){let s=eo(o);for(;ut(s)&&!$r(s);){if(Mr(s))return s;s=eo(s)}return null}(e)||t}function Mm(e,t,n){const o=ut(t),s=Mt(t),r=Qn(e,!0,n==="fixed",t);let i={scrollLeft:0,scrollTop:0};const l={x:0,y:0};if(o||!o&&n!=="fixed")if((Vt(t)!=="body"||es(s))&&(i=ts(t)),ut(t)){const a=Qn(t,!0);l.x=a.x+t.clientLeft,l.y=a.y+t.clientTop}else s&&(l.x=Bu(s));return{x:r.left+i.scrollLeft-l.x,y:r.top+i.scrollTop-l.y,width:r.width,height:r.height}}const $m={getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:o,strategy:s}=e;const r=n==="clippingAncestors"?function(u,f){const c=f.get(u);if(c)return c;let h=ns(u).filter(g=>Lt(g)&&Vt(g)!=="body"),d=null;const _=at(u).position==="fixed";let v=_?eo(u):u;for(;Lt(v)&&!$r(v);){const g=at(v),y=Mr(v);(_?y||d:y||g.position!=="static"||!d||!["absolute","fixed"].includes(d.position))?d=g:h=h.filter(T=>T!==v),v=eo(v)}return f.set(u,h),h}(t,this._c):[].concat(n),i=[...r,o],l=i[0],a=i.reduce((u,f)=>{const c=zu(t,f,s);return u.top=Jn(c.top,u.top),u.right=Ru(c.right,u.right),u.bottom=Ru(c.bottom,u.bottom),u.left=Jn(c.left,u.left),u},zu(t,l,s));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{rect:t,offsetParent:n,strategy:o}=e;const s=ut(n),r=Mt(n);if(n===r)return t;let i={scrollLeft:0,scrollTop:0},l={x:1,y:1};const a={x:0,y:0};if((s||!s&&o!=="fixed")&&((Vt(n)!=="body"||es(r))&&(i=ts(n)),ut(n))){const u=Qn(n);l=vn(n),a.x=u.x+n.clientLeft,a.y=u.y+n.clientTop}return{width:t.width*l.x,height:t.height*l.y,x:t.x*l.x-i.scrollLeft*l.x+a.x,y:t.y*l.y-i.scrollTop*l.y+a.y}},isElement:Lt,getDimensions:function(e){return ut(e)?Nu(e):e.getBoundingClientRect()},getOffsetParent:Ku,getDocumentElement:Mt,getScale:vn,async getElementRects(e){let{reference:t,floating:n,strategy:o}=e;const s=this.getOffsetParent||Ku,r=this.getDimensions;return{reference:Mm(t,await s(n),o),floating:{x:0,y:0,...await r(n)}}},getClientRects:e=>Array.from(e.getClientRects()),isRTL:e=>at(e).direction==="rtl"},Fm=(e,t,n)=>{const o=new Map,s={platform:$m,...n},r={...s.platform,_c:o};return Am(e,t,{...s,platform:r})},en={disabled:!1,distance:5,skidding:0,container:"body",boundary:void 0,instantMove:!1,disposeTimeout:150,popperTriggers:[],strategy:"absolute",preventOverflow:!0,flip:!0,shift:!0,overflowPadding:0,arrowPadding:0,arrowOverflow:!0,autoHideOnMousedown:!1,themes:{tooltip:{placement:"top",triggers:["hover","focus","touch"],hideTriggers:e=>[...e,"click"],delay:{show:200,hide:0},handleResize:!1,html:!1,loadingContent:"..."},dropdown:{placement:"bottom",triggers:["click"],delay:0,handleResize:!0,autoHide:!0},menu:{$extend:"dropdown",triggers:["hover","focus"],popperTriggers:["hover"],delay:{show:0,hide:400}}}};function Fr(e,t){let n=en.themes[e]||{},o;do o=n[t],typeof o>"u"?n.$extend?n=en.themes[n.$extend]||{}:(n=null,o=en[t]):n=null;while(n);return o}function Um(e){const t=[e];let n=en.themes[e]||{};do n.$extend&&!n.$resetCss?(t.push(n.$extend),n=en.themes[n.$extend]||{}):n=null;while(n);return t.map(o=>`v-popper--theme-${o}`)}function Wu(e){const t=[e];let n=en.themes[e]||{};do n.$extend?(t.push(n.$extend),n=en.themes[n.$extend]||{}):n=null;while(n);return t}let to=!1;if(typeof window<"u"){to=!1;try{const e=Object.defineProperty({},"passive",{get(){to=!0}});window.addEventListener("test",null,e)}catch{}}let Gu=!1;typeof window<"u"&&typeof navigator<"u"&&(Gu=/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream);const Bm=["auto","top","bottom","left","right"].reduce((e,t)=>e.concat([t,`${t}-start`,`${t}-end`]),[]),Yu={hover:"mouseenter",focus:"focus",click:"click",touch:"touchstart",pointer:"pointerdown"},qu={hover:"mouseleave",focus:"blur",click:"click",touch:"touchend",pointer:"pointerup"};function Xu(e,t){const n=e.indexOf(t);n!==-1&&e.splice(n,1)}function Ur(){return new Promise(e=>requestAnimationFrame(()=>{requestAnimationFrame(e)}))}const Xe=[];let tn=null;const Zu={};function Ju(e){let t=Zu[e];return t||(t=Zu[e]=[]),t}let Br=function(){};typeof window<"u"&&(Br=window.Element);function ee(e){return function(t){return Fr(t.theme,e)}}const Hr="__floating-vue__popper",Qu=()=>un({name:"VPopper",provide(){return{[Hr]:{parentPopper:this}}},inject:{[Hr]:{default:null}},props:{theme:{type:String,required:!0},targetNodes:{type:Function,required:!0},referenceNode:{type:Function,default:null},popperNode:{type:Function,required:!0},shown:{type:Boolean,default:!1},showGroup:{type:String,default:null},ariaId:{default:null},disabled:{type:Boolean,default:ee("disabled")},positioningDisabled:{type:Boolean,default:ee("positioningDisabled")},placement:{type:String,default:ee("placement"),validator:e=>Bm.includes(e)},delay:{type:[String,Number,Object],default:ee("delay")},distance:{type:[Number,String],default:ee("distance")},skidding:{type:[Number,String],default:ee("skidding")},triggers:{type:Array,default:ee("triggers")},showTriggers:{type:[Array,Function],default:ee("showTriggers")},hideTriggers:{type:[Array,Function],default:ee("hideTriggers")},popperTriggers:{type:Array,default:ee("popperTriggers")},popperShowTriggers:{type:[Array,Function],default:ee("popperShowTriggers")},popperHideTriggers:{type:[Array,Function],default:ee("popperHideTriggers")},container:{type:[String,Object,Br,Boolean],default:ee("container")},boundary:{type:[String,Br],default:ee("boundary")},strategy:{type:String,validator:e=>["absolute","fixed"].includes(e),default:ee("strategy")},autoHide:{type:[Boolean,Function],default:ee("autoHide")},handleResize:{type:Boolean,default:ee("handleResize")},instantMove:{type:Boolean,default:ee("instantMove")},eagerMount:{type:Boolean,default:ee("eagerMount")},popperClass:{type:[String,Array,Object],default:ee("popperClass")},computeTransformOrigin:{type:Boolean,default:ee("computeTransformOrigin")},autoMinSize:{type:Boolean,default:ee("autoMinSize")},autoSize:{type:[Boolean,String],default:ee("autoSize")},autoMaxSize:{type:Boolean,default:ee("autoMaxSize")},autoBoundaryMaxSize:{type:Boolean,default:ee("autoBoundaryMaxSize")},preventOverflow:{type:Boolean,default:ee("preventOverflow")},overflowPadding:{type:[Number,String],default:ee("overflowPadding")},arrowPadding:{type:[Number,String],default:ee("arrowPadding")},arrowOverflow:{type:Boolean,default:ee("arrowOverflow")},flip:{type:Boolean,default:ee("flip")},shift:{type:Boolean,default:ee("shift")},shiftCrossAxis:{type:Boolean,default:ee("shiftCrossAxis")},noAutoFocus:{type:Boolean,default:ee("noAutoFocus")},disposeTimeout:{type:Number,default:ee("disposeTimeout")}},emits:{show:()=>!0,hide:()=>!0,"update:shown":e=>!0,"apply-show":()=>!0,"apply-hide":()=>!0,"close-group":()=>!0,"close-directive":()=>!0,"auto-hide":()=>!0,resize:()=>!0},data(){return{isShown:!1,isMounted:!1,skipTransition:!1,classes:{showFrom:!1,showTo:!1,hideFrom:!1,hideTo:!0},result:{x:0,y:0,placement:"",strategy:this.strategy,arrow:{x:0,y:0,centerOffset:0},transformOrigin:null},randomId:`popper_${[Math.random(),Date.now()].map(e=>e.toString(36).substring(2,10)).join("_")}`,shownChildren:new Set,lastAutoHide:!0,pendingHide:!1,containsGlobalTarget:!1,isDisposed:!0,mouseDownContains:!1}},computed:{popperId(){return this.ariaId!=null?this.ariaId:this.randomId},shouldMountContent(){return this.eagerMount||this.isMounted},slotData(){return{popperId:this.popperId,isShown:this.isShown,shouldMountContent:this.shouldMountContent,skipTransition:this.skipTransition,autoHide:typeof this.autoHide=="function"?this.lastAutoHide:this.autoHide,show:this.show,hide:this.hide,handleResize:this.handleResize,onResize:this.onResize,classes:{...this.classes,popperClass:this.popperClass},result:this.positioningDisabled?null:this.result,attrs:this.$attrs}},parentPopper(){var e;return(e=this[Hr])==null?void 0:e.parentPopper},hasPopperShowTriggerHover(){var e,t;return((e=this.popperTriggers)==null?void 0:e.includes("hover"))||((t=this.popperShowTriggers)==null?void 0:t.includes("hover"))}},watch:{shown:"$_autoShowHide",disabled(e){e?this.dispose():this.init()},async container(){this.isShown&&(this.$_ensureTeleport(),await this.$_computePosition())},triggers:{handler:"$_refreshListeners",deep:!0},positioningDisabled:"$_refreshListeners",...["placement","distance","skidding","boundary","strategy","overflowPadding","arrowPadding","preventOverflow","shift","shiftCrossAxis","flip"].reduce((e,t)=>(e[t]="$_computePosition",e),{})},created(){this.autoMinSize&&console.warn('[floating-vue] `autoMinSize` option is deprecated. Use `autoSize="min"` instead.'),this.autoMaxSize&&console.warn("[floating-vue] `autoMaxSize` option is deprecated. Use `autoBoundaryMaxSize` instead.")},mounted(){this.init(),this.$_detachPopperNode()},activated(){this.$_autoShowHide()},deactivated(){this.hide()},beforeUnmount(){this.dispose()},methods:{show({event:e=null,skipDelay:t=!1,force:n=!1}={}){var o,s;(o=this.parentPopper)!=null&&o.lockedChild&&this.parentPopper.lockedChild!==this||(this.pendingHide=!1,(n||!this.disabled)&&(((s=this.parentPopper)==null?void 0:s.lockedChild)===this&&(this.parentPopper.lockedChild=null),this.$_scheduleShow(e,t),this.$emit("show"),this.$_showFrameLocked=!0,requestAnimationFrame(()=>{this.$_showFrameLocked=!1})),this.$emit("update:shown",!0))},hide({event:e=null,skipDelay:t=!1}={}){var n;if(!this.$_hideInProgress){if(this.shownChildren.size>0){this.pendingHide=!0;return}if(this.hasPopperShowTriggerHover&&this.$_isAimingPopper()){this.parentPopper&&(this.parentPopper.lockedChild=this,clearTimeout(this.parentPopper.lockedChildTimer),this.parentPopper.lockedChildTimer=setTimeout(()=>{this.parentPopper.lockedChild===this&&(this.parentPopper.lockedChild.hide({skipDelay:t}),this.parentPopper.lockedChild=null)},1e3));return}((n=this.parentPopper)==null?void 0:n.lockedChild)===this&&(this.parentPopper.lockedChild=null),this.pendingHide=!1,this.$_scheduleHide(e,t),this.$emit("hide"),this.$emit("update:shown",!1)}},init(){var e;this.isDisposed&&(this.isDisposed=!1,this.isMounted=!1,this.$_events=[],this.$_preventShow=!1,this.$_referenceNode=((e=this.referenceNode)==null?void 0:e.call(this))??this.$el,this.$_targetNodes=this.targetNodes().filter(t=>t.nodeType===t.ELEMENT_NODE),this.$_popperNode=this.popperNode(),this.$_innerNode=this.$_popperNode.querySelector(".v-popper__inner"),this.$_arrowNode=this.$_popperNode.querySelector(".v-popper__arrow-container"),this.$_swapTargetAttrs("title","data-original-title"),this.$_detachPopperNode(),this.triggers.length&&this.$_addEventListeners(),this.shown&&this.show())},dispose(){this.isDisposed||(this.isDisposed=!0,this.$_removeEventListeners(),this.hide({skipDelay:!0}),this.$_detachPopperNode(),this.isMounted=!1,this.isShown=!1,this.$_updateParentShownChildren(!1),this.$_swapTargetAttrs("data-original-title","title"))},async onResize(){this.isShown&&(await this.$_computePosition(),this.$emit("resize"))},async $_computePosition(){if(this.isDisposed||this.positioningDisabled)return;const e={strategy:this.strategy,middleware:[]};(this.distance||this.skidding)&&e.middleware.push(Rm({mainAxis:this.distance,crossAxis:this.skidding}));const t=this.placement.startsWith("auto");if(t?e.middleware.push(Pm({alignment:this.placement.split("-")[1]??""})):e.placement=this.placement,this.preventOverflow&&(this.shift&&e.middleware.push(Nm({padding:this.overflowPadding,boundary:this.boundary,crossAxis:this.shiftCrossAxis})),!t&&this.flip&&e.middleware.push(Im({padding:this.overflowPadding,boundary:this.boundary}))),e.middleware.push(Cm({element:this.$_arrowNode,padding:this.arrowPadding})),this.arrowOverflow&&e.middleware.push({name:"arrowOverflow",fn:({placement:o,rects:s,middlewareData:r})=>{let i;const{centerOffset:l}=r.arrow;return o.startsWith("top")||o.startsWith("bottom")?i=Math.abs(l)>s.reference.width/2:i=Math.abs(l)>s.reference.height/2,{data:{overflow:i}}}}),this.autoMinSize||this.autoSize){const o=this.autoSize?this.autoSize:this.autoMinSize?"min":null;e.middleware.push({name:"autoSize",fn:({rects:s,placement:r,middlewareData:i})=>{var l;if((l=i.autoSize)!=null&&l.skip)return{};let a,u;return r.startsWith("top")||r.startsWith("bottom")?a=s.reference.width:u=s.reference.height,this.$_innerNode.style[o==="min"?"minWidth":o==="max"?"maxWidth":"width"]=a!=null?`${a}px`:null,this.$_innerNode.style[o==="min"?"minHeight":o==="max"?"maxHeight":"height"]=u!=null?`${u}px`:null,{data:{skip:!0},reset:{rects:!0}}}})}(this.autoMaxSize||this.autoBoundaryMaxSize)&&(this.$_innerNode.style.maxWidth=null,this.$_innerNode.style.maxHeight=null,e.middleware.push(Vm({boundary:this.boundary,padding:this.overflowPadding,apply:({availableWidth:o,availableHeight:s})=>{this.$_innerNode.style.maxWidth=o!=null?`${o}px`:null,this.$_innerNode.style.maxHeight=s!=null?`${s}px`:null}})));const n=await Fm(this.$_referenceNode,this.$_popperNode,e);Object.assign(this.result,{x:n.x,y:n.y,placement:n.placement,strategy:n.strategy,arrow:{...n.middlewareData.arrow,...n.middlewareData.arrowOverflow}})},$_scheduleShow(e,t=!1){if(this.$_updateParentShownChildren(!0),this.$_hideInProgress=!1,clearTimeout(this.$_scheduleTimer),tn&&this.instantMove&&tn.instantMove&&tn!==this.parentPopper){tn.$_applyHide(!0),this.$_applyShow(!0);return}t?this.$_applyShow():this.$_scheduleTimer=setTimeout(this.$_applyShow.bind(this),this.$_computeDelay("show"))},$_scheduleHide(e,t=!1){if(this.shownChildren.size>0){this.pendingHide=!0;return}this.$_updateParentShownChildren(!1),this.$_hideInProgress=!0,clearTimeout(this.$_scheduleTimer),this.isShown&&(tn=this),t?this.$_applyHide():this.$_scheduleTimer=setTimeout(this.$_applyHide.bind(this),this.$_computeDelay("hide"))},$_computeDelay(e){const t=this.delay;return parseInt(t&&t[e]||t||0)},async $_applyShow(e=!1){clearTimeout(this.$_disposeTimer),clearTimeout(this.$_scheduleTimer),this.skipTransition=e,!this.isShown&&(this.$_ensureTeleport(),await Ur(),await this.$_computePosition(),await this.$_applyShowEffect(),this.positioningDisabled||this.$_registerEventListeners([...ns(this.$_referenceNode),...ns(this.$_popperNode)],"scroll",()=>{this.$_computePosition()}))},async $_applyShowEffect(){if(this.$_hideInProgress)return;if(this.computeTransformOrigin){const t=this.$_referenceNode.getBoundingClientRect(),n=this.$_popperNode.querySelector(".v-popper__wrapper"),o=n.parentNode.getBoundingClientRect(),s=t.x+t.width/2-(o.left+n.offsetLeft),r=t.y+t.height/2-(o.top+n.offsetTop);this.result.transformOrigin=`${s}px ${r}px`}this.isShown=!0,this.$_applyAttrsToTarget({"aria-describedby":this.popperId,"data-popper-shown":""});const e=this.showGroup;if(e){let t;for(let n=0;n<Xe.length;n++)t=Xe[n],t.showGroup!==e&&(t.hide(),t.$emit("close-group"))}Xe.push(this),document.body.classList.add("v-popper--some-open");for(const t of Wu(this.theme))Ju(t).push(this),document.body.classList.add(`v-popper--some-open--${t}`);this.$emit("apply-show"),this.classes.showFrom=!0,this.classes.showTo=!1,this.classes.hideFrom=!1,this.classes.hideTo=!1,await Ur(),this.classes.showFrom=!1,this.classes.showTo=!0,this.noAutoFocus||this.$_popperNode.focus()},async $_applyHide(e=!1){if(this.shownChildren.size>0){this.pendingHide=!0,this.$_hideInProgress=!1;return}if(clearTimeout(this.$_scheduleTimer),!this.isShown)return;this.skipTransition=e,Xu(Xe,this),Xe.length===0&&document.body.classList.remove("v-popper--some-open");for(const n of Wu(this.theme)){const o=Ju(n);Xu(o,this),o.length===0&&document.body.classList.remove(`v-popper--some-open--${n}`)}tn===this&&(tn=null),this.isShown=!1,this.$_applyAttrsToTarget({"aria-describedby":void 0,"data-popper-shown":void 0}),clearTimeout(this.$_disposeTimer);const t=this.disposeTimeout;t!==null&&(this.$_disposeTimer=setTimeout(()=>{this.$_popperNode&&(this.$_detachPopperNode(),this.isMounted=!1)},t)),this.$_removeEventListeners("scroll"),this.$emit("apply-hide"),this.classes.showFrom=!1,this.classes.showTo=!1,this.classes.hideFrom=!0,this.classes.hideTo=!1,await Ur(),this.classes.hideFrom=!1,this.classes.hideTo=!0},$_autoShowHide(){this.shown?this.show():this.hide()},$_ensureTeleport(){if(this.isDisposed)return;let e=this.container;if(typeof e=="string"?e=window.document.querySelector(e):e===!1&&(e=this.$_targetNodes[0].parentNode),!e)throw new Error("No container for popover: "+this.container);e.appendChild(this.$_popperNode),this.isMounted=!0},$_addEventListeners(){const e=n=>{this.isShown&&!this.$_hideInProgress||(n.usedByTooltip=!0,!this.$_preventShow&&this.show({event:n}))};this.$_registerTriggerListeners(this.$_targetNodes,Yu,this.triggers,this.showTriggers,e),this.$_registerTriggerListeners([this.$_popperNode],Yu,this.popperTriggers,this.popperShowTriggers,e);const t=n=>{n.usedByTooltip||this.hide({event:n})};this.$_registerTriggerListeners(this.$_targetNodes,qu,this.triggers,this.hideTriggers,t),this.$_registerTriggerListeners([this.$_popperNode],qu,this.popperTriggers,this.popperHideTriggers,t)},$_registerEventListeners(e,t,n){this.$_events.push({targetNodes:e,eventType:t,handler:n}),e.forEach(o=>o.addEventListener(t,n,to?{passive:!0}:void 0))},$_registerTriggerListeners(e,t,n,o,s){let r=n;o!=null&&(r=typeof o=="function"?o(r):o),r.forEach(i=>{const l=t[i];l&&this.$_registerEventListeners(e,l,s)})},$_removeEventListeners(e){const t=[];this.$_events.forEach(n=>{const{targetNodes:o,eventType:s,handler:r}=n;!e||e===s?o.forEach(i=>i.removeEventListener(s,r)):t.push(n)}),this.$_events=t},$_refreshListeners(){this.isDisposed||(this.$_removeEventListeners(),this.$_addEventListeners())},$_handleGlobalClose(e,t=!1){this.$_showFrameLocked||(this.hide({event:e}),e.closePopover?this.$emit("close-directive"):this.$emit("auto-hide"),t&&(this.$_preventShow=!0,setTimeout(()=>{this.$_preventShow=!1},300)))},$_detachPopperNode(){this.$_popperNode.parentNode&&this.$_popperNode.parentNode.removeChild(this.$_popperNode)},$_swapTargetAttrs(e,t){for(const n of this.$_targetNodes){const o=n.getAttribute(e);o&&(n.removeAttribute(e),n.setAttribute(t,o))}},$_applyAttrsToTarget(e){for(const t of this.$_targetNodes)for(const n in e){const o=e[n];o==null?t.removeAttribute(n):t.setAttribute(n,o)}},$_updateParentShownChildren(e){let t=this.parentPopper;for(;t;)e?t.shownChildren.add(this.randomId):(t.shownChildren.delete(this.randomId),t.pendingHide&&t.hide()),t=t.parentPopper},$_isAimingPopper(){const e=this.$_referenceNode.getBoundingClientRect();if(no>=e.left&&no<=e.right&&oo>=e.top&&oo<=e.bottom){const t=this.$_popperNode.getBoundingClientRect(),n=no-$t,o=oo-Ft,s=t.left+t.width/2-$t+(t.top+t.height/2)-Ft+t.width+t.height,r=$t+n*s,i=Ft+o*s;return os($t,Ft,r,i,t.left,t.top,t.left,t.bottom)||os($t,Ft,r,i,t.left,t.top,t.right,t.top)||os($t,Ft,r,i,t.right,t.top,t.right,t.bottom)||os($t,Ft,r,i,t.left,t.bottom,t.right,t.bottom)}return!1}},render(){return this.$slots.default(this.slotData)}});if(typeof document<"u"&&typeof window<"u"){if(Gu){const e=to?{passive:!0,capture:!0}:!0;document.addEventListener("touchstart",t=>ec(t),e),document.addEventListener("touchend",t=>tc(t,!0),e)}else window.addEventListener("mousedown",e=>ec(e),!0),window.addEventListener("click",e=>tc(e,!1),!0);window.addEventListener("resize",jm)}function ec(e,t){for(let n=0;n<Xe.length;n++){const o=Xe[n];try{o.mouseDownContains=o.popperNode().contains(e.target)}catch{}}}function tc(e,t){Hm(e,t)}function Hm(e,t){const n={};for(let o=Xe.length-1;o>=0;o--){const s=Xe[o];try{const r=s.containsGlobalTarget=s.mouseDownContains||s.popperNode().contains(e.target);s.pendingHide=!1,requestAnimationFrame(()=>{if(s.pendingHide=!1,!n[s.randomId]&&nc(s,r,e)){if(s.$_handleGlobalClose(e,t),!e.closeAllPopover&&e.closePopover&&r){let l=s.parentPopper;for(;l;)n[l.randomId]=!0,l=l.parentPopper;return}let i=s.parentPopper;for(;i&&nc(i,i.containsGlobalTarget,e);)i.$_handleGlobalClose(e,t),i=i.parentPopper}})}catch{}}}function nc(e,t,n){return n.closeAllPopover||n.closePopover&&t||zm(e,n)&&!t}function zm(e,t){if(typeof e.autoHide=="function"){const n=e.autoHide(t);return e.lastAutoHide=n,n}return e.autoHide}function jm(){for(let e=0;e<Xe.length;e++)Xe[e].$_computePosition()}let $t=0,Ft=0,no=0,oo=0;typeof window<"u"&&window.addEventListener("mousemove",e=>{$t=no,Ft=oo,no=e.clientX,oo=e.clientY},to?{passive:!0}:void 0);function os(e,t,n,o,s,r,i,l){const a=((i-s)*(t-r)-(l-r)*(e-s))/((l-r)*(n-e)-(i-s)*(o-t)),u=((n-e)*(t-r)-(o-t)*(e-s))/((l-r)*(n-e)-(i-s)*(o-t));return a>=0&&a<=1&&u>=0&&u<=1}const Km={extends:Qu()},zr=(e,t)=>{const n=e.__vccOpts||e;for(const[o,s]of t)n[o]=s;return n};function Wm(e,t,n,o,s,r){return Ie(),At("div",{ref:"reference",class:pt(["v-popper",{"v-popper--shown":e.slotData.isShown}])},[Io(e.$slots,"default",Mc(dl(e.slotData)))],2)}const Gm=zr(Km,[["render",Wm]]);function Ym(){var e=window.navigator.userAgent,t=e.indexOf("MSIE ");if(t>0)return parseInt(e.substring(t+5,e.indexOf(".",t)),10);var n=e.indexOf("Trident/");if(n>0){var o=e.indexOf("rv:");return parseInt(e.substring(o+3,e.indexOf(".",o)),10)}var s=e.indexOf("Edge/");return s>0?parseInt(e.substring(s+5,e.indexOf(".",s)),10):-1}let ss;function jr(){jr.init||(jr.init=!0,ss=Ym()!==-1)}var rs={name:"ResizeObserver",props:{emitOnMount:{type:Boolean,default:!1},ignoreWidth:{type:Boolean,default:!1},ignoreHeight:{type:Boolean,default:!1}},emits:["notify"],mounted(){jr(),xo(()=>{this._w=this.$el.offsetWidth,this._h=this.$el.offsetHeight,this.emitOnMount&&this.emitSize()});const e=document.createElement("object");this._resizeObject=e,e.setAttribute("aria-hidden","true"),e.setAttribute("tabindex",-1),e.onload=this.addResizeHandlers,e.type="text/html",ss&&this.$el.appendChild(e),e.data="about:blank",ss||this.$el.appendChild(e)},beforeUnmount(){this.removeResizeHandlers()},methods:{compareAndNotify(){(!this.ignoreWidth&&this._w!==this.$el.offsetWidth||!this.ignoreHeight&&this._h!==this.$el.offsetHeight)&&(this._w=this.$el.offsetWidth,this._h=this.$el.offsetHeight,this.emitSize())},emitSize(){this.$emit("notify",{width:this._w,height:this._h})},addResizeHandlers(){this._resizeObject.contentDocument.defaultView.addEventListener("resize",this.compareAndNotify),this.compareAndNotify()},removeResizeHandlers(){this._resizeObject&&this._resizeObject.onload&&(!ss&&this._resizeObject.contentDocument&&this._resizeObject.contentDocument.defaultView.removeEventListener("resize",this.compareAndNotify),this.$el.removeChild(this._resizeObject),this._resizeObject.onload=null,this._resizeObject=null)}}};const qm=kf();Pf("data-v-b329ee4c");const Xm={class:"resize-observer",tabindex:"-1"};If();const Zm=qm((e,t,n,o,s,r)=>(Ie(),fn("div",Xm)));rs.render=Zm,rs.__scopeId="data-v-b329ee4c",rs.__file="src/components/ResizeObserver.vue";const oc=(e="theme")=>({computed:{themeClass(){return Um(this[e])}}}),Jm=un({name:"VPopperContent",components:{ResizeObserver:rs},mixins:[oc()],props:{popperId:String,theme:String,shown:Boolean,mounted:Boolean,skipTransition:Boolean,autoHide:Boolean,handleResize:Boolean,classes:Object,result:Object},emits:["hide","resize"],methods:{toPx(e){return e!=null&&!isNaN(e)?`${e}px`:null}}}),Qm=["id","aria-hidden","tabindex","data-popper-placement"],eg={ref:"inner",class:"v-popper__inner"},tg=ie("div",{class:"v-popper__arrow-outer"},null,-1),ng=ie("div",{class:"v-popper__arrow-inner"},null,-1),og=[tg,ng];function sg(e,t,n,o,s,r){const i=Us("ResizeObserver");return Ie(),At("div",{id:e.popperId,ref:"popover",class:pt(["v-popper__popper",[e.themeClass,e.classes.popperClass,{"v-popper__popper--shown":e.shown,"v-popper__popper--hidden":!e.shown,"v-popper__popper--show-from":e.classes.showFrom,"v-popper__popper--show-to":e.classes.showTo,"v-popper__popper--hide-from":e.classes.hideFrom,"v-popper__popper--hide-to":e.classes.hideTo,"v-popper__popper--skip-transition":e.skipTransition,"v-popper__popper--arrow-overflow":e.result&&e.result.arrow.overflow,"v-popper__popper--no-positioning":!e.result}]]),style:Ne(e.result?{position:e.result.strategy,transform:`translate3d(${Math.round(e.result.x)}px,${Math.round(e.result.y)}px,0)`}:void 0),"aria-hidden":e.shown?"false":"true",tabindex:e.autoHide?0:void 0,"data-popper-placement":e.result?e.result.placement:void 0,onKeyup:t[2]||(t[2]=lp(l=>e.autoHide&&e.$emit("hide"),["esc"]))},[ie("div",{class:"v-popper__backdrop",onClick:t[0]||(t[0]=l=>e.autoHide&&e.$emit("hide"))}),ie("div",{class:"v-popper__wrapper",style:Ne(e.result?{transformOrigin:e.result.transformOrigin}:void 0)},[ie("div",eg,[e.mounted?(Ie(),At(Pe,{key:0},[ie("div",null,[Io(e.$slots,"default")]),e.handleResize?(Ie(),fn(i,{key:0,onNotify:t[1]||(t[1]=l=>e.$emit("resize",l))})):Lo("",!0)],64)):Lo("",!0)],512),ie("div",{ref:"arrow",class:"v-popper__arrow-container",style:Ne(e.result?{left:e.toPx(e.result.arrow.x),top:e.toPx(e.result.arrow.y)}:void 0)},og,4)],4)],46,Qm)}const sc=zr(Jm,[["render",sg]]),rc={methods:{show(...e){return this.$refs.popper.show(...e)},hide(...e){return this.$refs.popper.hide(...e)},dispose(...e){return this.$refs.popper.dispose(...e)},onResize(...e){return this.$refs.popper.onResize(...e)}}};let Kr=function(){};typeof window<"u"&&(Kr=window.Element);const rg=un({name:"VPopperWrapper",components:{Popper:Gm,PopperContent:sc},mixins:[rc,oc("finalTheme")],props:{theme:{type:String,default:null},referenceNode:{type:Function,default:null},shown:{type:Boolean,default:!1},showGroup:{type:String,default:null},ariaId:{default:null},disabled:{type:Boolean,default:void 0},positioningDisabled:{type:Boolean,default:void 0},placement:{type:String,default:void 0},delay:{type:[String,Number,Object],default:void 0},distance:{type:[Number,String],default:void 0},skidding:{type:[Number,String],default:void 0},triggers:{type:Array,default:void 0},showTriggers:{type:[Array,Function],default:void 0},hideTriggers:{type:[Array,Function],default:void 0},popperTriggers:{type:Array,default:void 0},popperShowTriggers:{type:[Array,Function],default:void 0},popperHideTriggers:{type:[Array,Function],default:void 0},container:{type:[String,Object,Kr,Boolean],default:void 0},boundary:{type:[String,Kr],default:void 0},strategy:{type:String,default:void 0},autoHide:{type:[Boolean,Function],default:void 0},handleResize:{type:Boolean,default:void 0},instantMove:{type:Boolean,default:void 0},eagerMount:{type:Boolean,default:void 0},popperClass:{type:[String,Array,Object],default:void 0},computeTransformOrigin:{type:Boolean,default:void 0},autoMinSize:{type:Boolean,default:void 0},autoSize:{type:[Boolean,String],default:void 0},autoMaxSize:{type:Boolean,default:void 0},autoBoundaryMaxSize:{type:Boolean,default:void 0},preventOverflow:{type:Boolean,default:void 0},overflowPadding:{type:[Number,String],default:void 0},arrowPadding:{type:[Number,String],default:void 0},arrowOverflow:{type:Boolean,default:void 0},flip:{type:Boolean,default:void 0},shift:{type:Boolean,default:void 0},shiftCrossAxis:{type:Boolean,default:void 0},noAutoFocus:{type:Boolean,default:void 0},disposeTimeout:{type:Number,default:void 0}},emits:{show:()=>!0,hide:()=>!0,"update:shown":e=>!0,"apply-show":()=>!0,"apply-hide":()=>!0,"close-group":()=>!0,"close-directive":()=>!0,"auto-hide":()=>!0,resize:()=>!0},computed:{finalTheme(){return this.theme??this.$options.vPopperTheme}},methods:{getTargetNodes(){return Array.from(this.$el.children).filter(e=>e!==this.$refs.popperContent.$el)}}});function ig(e,t,n,o,s,r){const i=Us("PopperContent"),l=Us("Popper");return Ie(),fn(l,pl({ref:"popper"},e.$props,{theme:e.finalTheme,"target-nodes":e.getTargetNodes,"popper-node":()=>e.$refs.popperContent.$el,class:[e.themeClass],onShow:t[0]||(t[0]=()=>e.$emit("show")),onHide:t[1]||(t[1]=()=>e.$emit("hide")),"onUpdate:shown":t[2]||(t[2]=a=>e.$emit("update:shown",a)),onApplyShow:t[3]||(t[3]=()=>e.$emit("apply-show")),onApplyHide:t[4]||(t[4]=()=>e.$emit("apply-hide")),onCloseGroup:t[5]||(t[5]=()=>e.$emit("close-group")),onCloseDirective:t[6]||(t[6]=()=>e.$emit("close-directive")),onAutoHide:t[7]||(t[7]=()=>e.$emit("auto-hide")),onResize:t[8]||(t[8]=()=>e.$emit("resize"))}),{default:Co(({popperId:a,isShown:u,shouldMountContent:f,skipTransition:c,autoHide:h,show:d,hide:_,handleResize:v,onResize:g,classes:y,result:T})=>[Io(e.$slots,"default",{shown:u,show:d,hide:_}),Te(i,{ref:"popperContent","popper-id":a,theme:e.finalTheme,shown:u,mounted:f,"skip-transition":c,"auto-hide":h,"handle-resize":v,classes:y,result:T,onHide:_,onResize:g},{default:Co(()=>[Io(e.$slots,"popper",{shown:u,hide:_})]),_:2},1032,["popper-id","theme","shown","mounted","skip-transition","auto-hide","handle-resize","classes","result","onHide","onResize"])]),_:3},16,["theme","target-nodes","popper-node","class"])}const Wr=zr(rg,[["render",ig]]);({...Wr},{...Wr}),{...Wr},Qu();function Gr(e){return oi()?(Bc(e),!0):!1}const Yr=new WeakMap,lg=(...e)=>{var t;const n=e[0],o=(t=Js())==null?void 0:t.proxy;if(o==null&&!ji())throw new Error("injectLocal must be called in setup");return o&&Yr.has(o)&&n in Yr.get(o)?Yr.get(o)[n]:Rn(...e)},ic=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const ag=e=>e!=null,ug=Object.prototype.toString,cg=e=>ug.call(e)==="[object Object]",is=()=>{};function lc(e,t){function n(...o){return new Promise((s,r)=>{Promise.resolve(e(()=>t.apply(this,o),{fn:t,thisArg:this,args:o})).then(s).catch(r)})}return n}const ac=e=>e();function fg(e,t={}){let n,o,s=is;const r=l=>{clearTimeout(l),s(),s=is};return l=>{const a=Be(e),u=Be(t.maxWait);return n&&r(n),a<=0||u!==void 0&&u<=0?(o&&(r(o),o=null),Promise.resolve(l())):new Promise((f,c)=>{s=t.rejectOnCancel?c:f,u&&!o&&(o=setTimeout(()=>{n&&r(n),o=null,f(l())},u)),n=setTimeout(()=>{o&&r(o),o=null,f(l())},a)})}}function dg(e=ac){const t=de(!0);function n(){t.value=!1}function o(){t.value=!0}const s=(...r)=>{t.value&&e(...r)};return{isActive:On(t),pause:n,resume:o,eventFilter:s}}function uc(e){return e.endsWith("rem")?Number.parseFloat(e)*16:Number.parseFloat(e)}function pg(e){return Js()}function qr(e){return Array.isArray(e)?e:[e]}function hg(...e){if(e.length!==1)return mf(...e);const t=e[0];return typeof t=="function"?On(pf(()=>({get:t,set:is}))):de(t)}function _g(e,t=200,n={}){return lc(fg(t,n),e)}function mg(e,t,n={}){const{eventFilter:o=ac,...s}=n;return et(e,lc(o,t),s)}function gg(e,t,n={}){const{eventFilter:o,...s}=n,{eventFilter:r,pause:i,resume:l,isActive:a}=dg(o);return{stop:mg(e,t,{...s,eventFilter:r}),pause:i,resume:l,isActive:a}}function Xr(e,t=!0,n){pg()?Pn(e,n):t?e():xo(e)}const bt=ic?window:void 0;function ls(e){var t;const n=Be(e);return(t=n==null?void 0:n.$el)!=null?t:n}function ke(...e){let t,n,o,s;if(typeof e[0]=="string"||Array.isArray(e[0])?([n,o,s]=e,t=bt):[t,n,o,s]=e,!t)return is;n=qr(n),o=qr(o);const r=[],i=()=>{r.forEach(f=>f()),r.length=0},l=(f,c,h,d)=>(f.addEventListener(c,h,d),()=>f.removeEventListener(c,h,d)),a=et(()=>[ls(t),Be(s)],([f,c])=>{if(i(),!f)return;const h=cg(c)?{...c}:c;r.push(...n.flatMap(d=>o.map(_=>l(f,d,_,h))))},{immediate:!0,flush:"post"}),u=()=>{a(),i()};return Gr(u),u}function vg(){const e=de(!1),t=Js();return t&&Pn(()=>{e.value=!0},t),e}function cc(e){const t=vg();return be(()=>(t.value,!!e()))}function yg(e,t,n={}){const{window:o=bt,...s}=n;let r;const i=cc(()=>o&&"MutationObserver"in o),l=()=>{r&&(r.disconnect(),r=void 0)},a=be(()=>{const h=Be(e),d=qr(h).map(ls).filter(ag);return new Set(d)}),u=et(()=>a.value,h=>{l(),i.value&&h.size&&(r=new MutationObserver(t),h.forEach(d=>r.observe(d,s)))},{immediate:!0,flush:"post"}),f=()=>r==null?void 0:r.takeRecords(),c=()=>{u(),l()};return Gr(c),{isSupported:i,stop:c,takeRecords:f}}const Eg=Symbol("vueuse-ssr-width");function bg(){const e=ji()?lg(Eg,null):null;return typeof e=="number"?e:void 0}function fc(e,t={}){const{window:n=bt,ssrWidth:o=bg()}=t,s=cc(()=>n&&"matchMedia"in n&&typeof n.matchMedia=="function"),r=de(typeof o=="number");let i;const l=de(!1),a=c=>{l.value=c.matches},u=()=>{i&&("removeEventListener"in i?i.removeEventListener("change",a):i.removeListener(a))},f=Ys(()=>{if(r.value){r.value=!s.value;const c=Be(e).split(",");l.value=c.some(h=>{const d=h.includes("not all"),_=h.match(/\(\s*min-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/),v=h.match(/\(\s*max-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/);let g=!!(_||v);return _&&g&&(g=o>=uc(_[1])),v&&g&&(g=o<=uc(v[1])),d?!g:g});return}s.value&&(u(),i=n.matchMedia(Be(e)),"addEventListener"in i?i.addEventListener("change",a):i.addListener(a),l.value=i.matches)});return Gr(()=>{f(),u(),i=void 0}),be(()=>l.value)}const as=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},us="__vueuse_ssr_handlers__",wg=Sg();function Sg(){return us in as||(as[us]=as[us]||{}),as[us]}function dc(e,t){return wg[e]||t}function Tg(e){return fc("(prefers-color-scheme: dark)",e)}function xg(e){return e==null?"any":e instanceof Set?"set":e instanceof Map?"map":e instanceof Date?"date":typeof e=="boolean"?"boolean":typeof e=="string"?"string":typeof e=="object"?"object":Number.isNaN(e)?"any":"number"}const Og={boolean:{read:e=>e==="true",write:e=>String(e)},object:{read:e=>JSON.parse(e),write:e=>JSON.stringify(e)},number:{read:e=>Number.parseFloat(e),write:e=>String(e)},any:{read:e=>e,write:e=>String(e)},string:{read:e=>e,write:e=>String(e)},map:{read:e=>new Map(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e.entries()))},set:{read:e=>new Set(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e))},date:{read:e=>new Date(e),write:e=>e.toISOString()}},pc="vueuse-storage";function hc(e,t,n,o={}){var s;const{flush:r="pre",deep:i=!0,listenToStorageChanges:l=!0,writeDefaults:a=!0,mergeDefaults:u=!1,shallow:f,window:c=bt,eventFilter:h,onError:d=D=>{console.error(D)},initOnMounted:_}=o,v=(f?wi:de)(typeof t=="function"?t():t);if(!n)try{n=dc("getDefaultStorage",()=>{var D;return(D=bt)==null?void 0:D.localStorage})()}catch(D){d(D)}if(!n)return v;const g=Be(t),y=xg(g),T=(s=o.serializer)!=null?s:Og[y],{pause:P,resume:b}=gg(v,()=>F(v.value),{flush:r,deep:i,eventFilter:h});c&&l&&Xr(()=>{n instanceof Storage?ke(c,"storage",z):ke(c,pc,G),_&&z()}),_||z();function I(D,N){if(c){const $={key:e,oldValue:D,newValue:N,storageArea:n};c.dispatchEvent(n instanceof Storage?new StorageEvent("storage",$):new CustomEvent(pc,{detail:$}))}}function F(D){try{const N=n.getItem(e);if(D==null)I(N,null),n.removeItem(e);else{const $=T.write(D);N!==$&&(n.setItem(e,$),I(N,$))}}catch(N){d(N)}}function U(D){const N=D?D.newValue:n.getItem(e);if(N==null)return a&&g!=null&&n.setItem(e,T.write(g)),g;if(!D&&u){const $=T.read(N);return typeof u=="function"?u($,g):y==="object"&&!Array.isArray($)?{...g,...$}:$}else return typeof N!="string"?N:T.read(N)}function z(D){if(!(D&&D.storageArea!==n)){if(D&&D.key==null){v.value=g;return}if(!(D&&D.key!==e)){P();try{(D==null?void 0:D.newValue)!==T.write(v.value)&&(v.value=U(D))}catch(N){d(N)}finally{D?xo(b):b()}}}}function G(D){z(D.detail)}return v}const Ag="*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}";function Cg(e={}){const{selector:t="html",attribute:n="class",initialValue:o="auto",window:s=bt,storage:r,storageKey:i="vueuse-color-scheme",listenToStorageChanges:l=!0,storageRef:a,emitAuto:u,disableTransition:f=!0}=e,c={auto:"",light:"light",dark:"dark",...e.modes||{}},h=Tg({window:s}),d=be(()=>h.value?"dark":"light"),_=a||(i==null?hg(o):hc(i,o,r,{window:s,listenToStorageChanges:l})),v=be(()=>_.value==="auto"?d.value:_.value),g=dc("updateHTMLAttrs",(b,I,F)=>{const U=typeof b=="string"?s==null?void 0:s.document.querySelector(b):ls(b);if(!U)return;const z=new Set,G=new Set;let D=null;if(I==="class"){const $=F.split(/\s/g);Object.values(c).flatMap(j=>(j||"").split(/\s/g)).filter(Boolean).forEach(j=>{$.includes(j)?z.add(j):G.add(j)})}else D={key:I,value:F};if(z.size===0&&G.size===0&&D===null)return;let N;f&&(N=s.document.createElement("style"),N.appendChild(document.createTextNode(Ag)),s.document.head.appendChild(N));for(const $ of z)U.classList.add($);for(const $ of G)U.classList.remove($);D&&U.setAttribute(D.key,D.value),f&&(s.getComputedStyle(N).opacity,document.head.removeChild(N))});function y(b){var I;g(t,n,(I=c[b])!=null?I:b)}function T(b){e.onChanged?e.onChanged(b,y):y(b)}et(v,T,{flush:"post",immediate:!0}),Xr(()=>T(v.value));const P=be({get(){return u?_.value:v.value},set(b){_.value=b}});return Object.assign(P,{store:_,system:d,state:v})}function cs(e,t,n={}){const{window:o=bt,initialValue:s,observe:r=!1}=n,i=de(s),l=be(()=>{var u;return ls(t)||((u=o==null?void 0:o.document)==null?void 0:u.documentElement)});function a(){var u;const f=Be(e),c=Be(l);if(c&&o&&f){const h=(u=o.getComputedStyle(c).getPropertyValue(f))==null?void 0:u.trim();i.value=h||s}}return r&&yg(l,a,{attributeFilter:["style","class"],window:o}),et([l,()=>Be(e)],(u,f)=>{f[0]&&f[1]&&f[0].style.removeProperty(f[1]),a()},{immediate:!0}),et(i,u=>{var f;const c=Be(e);(f=l.value)!=null&&f.style&&c&&(u==null?l.value.style.removeProperty(c):l.value.style.setProperty(c,u))}),i}function Dg(e,t,n={}){const{window:o=bt}=n;return hc(e,t,o==null?void 0:o.localStorage,n)}const _c="--vueuse-safe-area-top",mc="--vueuse-safe-area-right",gc="--vueuse-safe-area-bottom",vc="--vueuse-safe-area-left";function Pg(){const e=de(""),t=de(""),n=de(""),o=de("");if(ic){const r=cs(_c),i=cs(mc),l=cs(gc),a=cs(vc);r.value="env(safe-area-inset-top, 0px)",i.value="env(safe-area-inset-right, 0px)",l.value="env(safe-area-inset-bottom, 0px)",a.value="env(safe-area-inset-left, 0px)",s(),ke("resize",_g(s))}function s(){e.value=fs(_c),t.value=fs(mc),n.value=fs(gc),o.value=fs(vc)}return{top:e,right:t,bottom:n,left:o,update:s}}function fs(e){return getComputedStyle(document.documentElement).getPropertyValue(e)}function Ig(e={}){const{window:t=bt,initialWidth:n=Number.POSITIVE_INFINITY,initialHeight:o=Number.POSITIVE_INFINITY,listenOrientation:s=!0,includeScrollbar:r=!0,type:i="inner"}=e,l=de(n),a=de(o),u=()=>{if(t)if(i==="outer")l.value=t.outerWidth,a.value=t.outerHeight;else if(i==="visual"&&t.visualViewport){const{width:f,height:c,scale:h}=t.visualViewport;l.value=Math.round(f*h),a.value=Math.round(c*h)}else r?(l.value=t.innerWidth,a.value=t.innerHeight):(l.value=t.document.documentElement.clientWidth,a.value=t.document.documentElement.clientHeight)};if(u(),Xr(u),ke("resize",u,{passive:!0}),t&&i==="visual"&&t.visualViewport&&ke(t.visualViewport,"resize",u,{passive:!0}),s){const f=fc("(orientation: portrait)");et(f,()=>u())}return{width:l,height:a}}wi();const kg="__vue-devtools-theme__";function Rg(e={}){const t=Cg({...e,storageKey:kg});return{colorMode:t,isDark:be(()=>t.value==="dark")}}function Ng(e,t){const n=de();function o(){return n.value||(n.value=document.createElement("iframe"),n.value.id="vue-devtools-iframe",n.value.src=e,n.value.setAttribute("data-v-inspector-ignore","true"),n.value.onload=t),n.value}return{getIframe:o,iframe:n}}const Zr=Dg("__vue-devtools-frame-state__",{width:80,height:60,top:0,left:50,open:!1,route:"/",position:"bottom",isFirstVisit:!0,closeOnOutsideClick:!1,minimizePanelInactive:5e3,preferShowFloatingPanel:!0,reduceMotion:!1});function ds(){function e(t){Zr.value={...Zr.value,...t}}return{state:On(Zr),updateState:e}}function Vg(){const{state:e,updateState:t}=ds(),n=be({get(){return e.value.open},set(r){t({open:r})}}),o=(r,i)=>{n.value=i??!n.value},s=()=>{n.value&&(n.value=!1)};return Pn(()=>{ke(window,"keydown",r=>{r.code==="KeyD"&&r.altKey&&r.shiftKey&&o()})}),{panelVisible:n,togglePanelVisible:o,closePanel:s}}function ps(e,t,n){return Math.min(Math.max(e,t),n)}const Lg=()=>navigator.userAgent.includes("Safari")&&!navigator.userAgent.includes("Chrome");function hs(e){return typeof e=="string"?e.endsWith("px")?+e.slice(0,-2):+e:e}function yc(e){return e<5?0:e>95?100:Math.abs(e-50)<2?50:e}function Mg(e){const{width:t,height:n}=Ig(),{state:o,updateState:s}=ds(),r=de(!1),i=de(!1),l=sn({x:0,y:0}),a=sn({x:0,y:0}),u=sn({left:10,top:10,right:10,bottom:10});let f=null;const c=Pg();Ys(()=>{u.left=hs(c.left.value)+10,u.top=hs(c.top.value)+10,u.right=hs(c.right.value)+10,u.bottom=hs(c.bottom.value)+10});const h=b=>{i.value=!0;const{left:I,top:F,width:U,height:z}=e.value.getBoundingClientRect();l.x=b.clientX-I-U/2,l.y=b.clientY-F-z/2},d=()=>{r.value=!0,!(o.value.minimizePanelInactive<0)&&(f&&clearTimeout(f),f=setTimeout(()=>{r.value=!1},+o.value.minimizePanelInactive||0))};Pn(()=>{d()}),ke("pointerup",()=>{i.value=!1}),ke("pointerleave",()=>{i.value=!1}),ke("pointermove",b=>{if(!i.value)return;const I=t.value/2,F=n.value/2,U=b.clientX-l.x,z=b.clientY-l.y;a.x=U,a.y=z;const G=Math.atan2(z-F,U-I),D=70,N=Math.atan2(0-F+D,0-I),$=Math.atan2(0-F+D,t.value-I),j=Math.atan2(n.value-D-F,0-I),J=Math.atan2(n.value-D-F,t.value-I);s({position:G>=N&&G<=$?"top":G>=$&&G<=J?"right":G>=J&&G<=j?"bottom":"left",left:yc(U/t.value*100),top:yc(z/n.value*100)})});const _=be(()=>o.value.position==="left"||o.value.position==="right"),v=be(()=>{if(o.value.minimizePanelInactive<0)return!1;if(o.value.minimizePanelInactive===0)return!0;const b="ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0;return!i.value&&!o.value.open&&!r.value&&!b&&o.value.minimizePanelInactive}),g=be(()=>{var z,G;const b=(((z=e.value)==null?void 0:z.clientWidth)||0)/2,I=(((G=e.value)==null?void 0:G.clientHeight)||0)/2,F=o.value.left*t.value/100,U=o.value.top*n.value/100;switch(o.value.position){case"top":return{left:ps(F,b+u.left,t.value-b-u.right),top:u.top+I};case"right":return{left:t.value-u.right-I,top:ps(U,b+u.top,n.value-b-u.bottom)};case"left":return{left:u.left+I,top:ps(U,b+u.top,n.value-b-u.bottom)};case"bottom":default:return{left:ps(F,b+u.left,t.value-b-u.right),top:n.value-u.bottom-I}}}),y=be(()=>({left:`${g.value.left}px`,top:`${g.value.top}px`})),T=be(()=>{var X;a.x,a.y;const b=(((X=e.value)==null?void 0:X.clientHeight)||0)/2,I={left:u.left+b,top:u.top+b,right:u.right+b,bottom:u.bottom+b},F=I.left+I.right,U=I.top+I.bottom,z=t.value-F,G=n.value-U,D={zIndex:-1,pointerEvents:i.value?"none":"auto",width:`min(${o.value.width}vw, calc(100vw - ${F}px))`,height:`min(${o.value.height}vh, calc(100vh - ${U}px))`},N=g.value,$=Math.min(z,o.value.width*t.value/100),j=Math.min(G,o.value.height*n.value/100),J=(N==null?void 0:N.left)||0,ve=(N==null?void 0:N.top)||0;switch(o.value.position){case"top":case"bottom":D.left=0,D.transform="translate(-50%, 0)",J-I.left<$/2?D.left=`${$/2-J+I.left}px`:t.value-J-I.right<$/2&&(D.left=`${t.value-J-$/2-I.right}px`);break;case"right":case"left":D.top=0,D.transform="translate(0, -50%)",ve-I.top<j/2?D.top=`${j/2-ve+I.top}px`:n.value-ve-I.bottom<j/2&&(D.top=`${n.value-ve-j/2-I.bottom}px`);break}switch(o.value.position){case"top":D.top=0;break;case"right":D.right=0;break;case"left":D.left=0;break;case"bottom":default:D.bottom=0;break}return D}),P=be(()=>{const b={transform:_.value?`translate(${v.value?`calc(-50% ${o.value.position==="right"?"+":"-"} 15px)`:"-50%"}, -50%) rotate(90deg)`:`translate(-50%, ${v.value?`calc(-50% ${o.value.position==="top"?"-":"+"} 15px)`:"-50%"})`};if(v.value)switch(o.value.position){case"top":case"right":b.borderTopLeftRadius="0",b.borderTopRightRadius="0";break;case"bottom":case"left":b.borderBottomLeftRadius="0",b.borderBottomRightRadius="0";break}return i.value&&(b.transition="none !important"),b});return{isHidden:v,isDragging:i,isVertical:_,anchorStyle:y,iframeStyle:T,panelStyle:P,onPointerDown:h,bringUp:d}}const _s=20,ms=100,$g=un({__name:"FrameBox",props:{isDragging:{type:Boolean},client:{},viewMode:{}},setup(e){const t=e,{state:n,updateState:o}=ds(),s=de(),r=de(!1);ym(()=>{Ou.functions.on("update-client-state",l=>{l&&o({minimizePanelInactive:l.minimizePanelInteractive,closeOnOutsideClick:l.closeOnOutsideClick,preferShowFloatingPanel:l.showFloatingPanel,reduceMotion:l.reduceMotion})})}),Ys(()=>{if(s.value&&n.value.open){const l=t.client.getIFrame();l.style.pointerEvents=r.value||t.isDragging?"none":"auto",Array.from(s.value.children).every(a=>a!==l)&&s.value.appendChild(l)}}),ke(window,"keydown",l=>{}),ke(window,"mousedown",l=>{if(!n.value.closeOnOutsideClick||!n.value.open||r.value)return;l.composedPath().find(u=>{var c;const f=u;return Array.from(f.classList||[]).some(h=>h.startsWith("vue-devtools"))||((c=f.tagName)==null?void 0:c.toLowerCase())==="iframe"})||o({open:!1})}),ke(window,"mousemove",l=>{if(!r.value||!n.value.open)return;const u=t.client.getIFrame().getBoundingClientRect();if(r.value.right){const c=Math.abs(l.clientX-((u==null?void 0:u.left)||0))/window.innerWidth*100;o({width:Math.min(ms,Math.max(_s,c))})}else if(r.value.left){const c=Math.abs(((u==null?void 0:u.right)||0)-l.clientX)/window.innerWidth*100;o({width:Math.min(ms,Math.max(_s,c))})}if(r.value.top){const c=Math.abs(((u==null?void 0:u.bottom)||0)-l.clientY)/window.innerHeight*100;o({height:Math.min(ms,Math.max(_s,c))})}else if(r.value.bottom){const c=Math.abs(l.clientY-((u==null?void 0:u.top)||0))/window.innerHeight*100;o({height:Math.min(ms,Math.max(_s,c))})}}),ke(window,"mouseup",()=>{r.value=!1}),ke(window,"mouseleave",()=>{r.value=!1});const i=be(()=>t.viewMode==="xs"?"view-mode-xs":t.viewMode==="fullscreen"?"view-mode-fullscreen":"");return(l,a)=>Qe((Ie(),At("div",{ref_key:"container",ref:s,class:pt(["vue-devtools-frame",i.value])},[Qe(ie("div",{class:"vue-devtools-resize vue-devtools-resize--horizontal",style:{top:0},onMousedown:a[0]||(a[0]=Dt(()=>r.value={top:!0},["prevent"]))},null,544),[[ot,Q(n).position!=="top"]]),Qe(ie("div",{class:"vue-devtools-resize vue-devtools-resize--horizontal",style:{bottom:0},onMousedown:a[1]||(a[1]=Dt(()=>r.value={bottom:!0},["prevent"]))},null,544),[[ot,Q(n).position!=="bottom"]]),Qe(ie("div",{class:"vue-devtools-resize vue-devtools-resize--vertical",style:{left:0},onMousedown:a[2]||(a[2]=Dt(()=>r.value={left:!0},["prevent"]))},null,544),[[ot,Q(n).position!=="left"]]),Qe(ie("div",{class:"vue-devtools-resize vue-devtools-resize--vertical",style:{right:0},onMousedown:a[3]||(a[3]=Dt(()=>r.value={right:!0},["prevent"]))},null,544),[[ot,Q(n).position!=="right"]]),Qe(ie("div",{class:"vue-devtools-resize vue-devtools-resize-corner",style:{top:0,left:0,cursor:"nwse-resize"},onMousedown:a[4]||(a[4]=Dt(()=>r.value={top:!0,left:!0},["prevent"]))},null,544),[[ot,Q(n).position!=="top"&&Q(n).position!=="left"]]),Qe(ie("div",{class:"vue-devtools-resize vue-devtools-resize-corner",style:{top:0,right:0,cursor:"nesw-resize"},onMousedown:a[5]||(a[5]=Dt(()=>r.value={top:!0,right:!0},["prevent"]))},null,544),[[ot,Q(n).position!=="top"&&Q(n).position!=="right"]]),Qe(ie("div",{class:"vue-devtools-resize vue-devtools-resize-corner",style:{bottom:0,left:0,cursor:"nesw-resize"},onMousedown:a[6]||(a[6]=Dt(()=>r.value={bottom:!0,left:!0},["prevent"]))},null,544),[[ot,Q(n).position!=="bottom"&&Q(n).position!=="left"]]),Qe(ie("div",{class:"vue-devtools-resize vue-devtools-resize-corner",style:{bottom:0,right:0,cursor:"nwse-resize"},onMousedown:a[7]||(a[7]=Dt(()=>r.value={bottom:!0,right:!0},["prevent"]))},null,544),[[ot,Q(n).position!=="bottom"&&Q(n).position!=="right"]])],2)),[[ot,Q(n).open]])}}),Ec=(e,t)=>{const n=e.__vccOpts||e;for(const[o,s]of t)n[o]=s;return n},Fg=Ec($g,[["__scopeId","data-v-399f5059"]]),Ug=Ec(un({__name:"App",setup(e){const t=de(),n=de(),{colorMode:o}=Rg({selector:t}),s=de({viewMode:"default"}),r=be(()=>{const N=o.value==="dark";return{"--vue-devtools-widget-bg":N?"#121212":"#ffffff","--vue-devtools-widget-fg":N?"#F5F5F5":"#111","--vue-devtools-widget-border":N?"#3336":"#efefef","--vue-devtools-widget-shadow":N?"rgba(0,0,0,0.3)":"rgba(128,128,128,0.1)"}}),{onPointerDown:i,bringUp:l,anchorStyle:a,iframeStyle:u,isDragging:f,isVertical:c,isHidden:h,panelStyle:d}=Mg(n),{togglePanelVisible:_,closePanel:v,panelVisible:g}=Vg(),y=pm(),T=de(!0);O.__VUE_DEVTOOLS_TOGGLE_OVERLAY__=N=>{T.value=N};const{updateState:P,state:b}=ds();function I(N,$=50,j=200){return new Promise(J=>{var ve;(ve=N==null?void 0:N.contentWindow)==null||ve.postMessage("__VUE_DEVTOOLS_CREATE_CLIENT__","*"),window.addEventListener("message",X=>{X.data==="__VUE_DEVTOOLS_CLIENT_READY__"&&J()})})}const F=de();Ph(()=>{wu().functions.on("toggle-panel",($=!g)=>{_(void 0,$)}),Su.ctx.api.getVueInspector().then($=>{F.value=$;let j=g.value;F.value.onEnabled=()=>{j=g.value,_(void 0,!1)},F.value.onDisabled=()=>{_(void 0,j)}})}),addEventListener("keyup",N=>{var $,j,J;(($=N.key)==null?void 0:$.toLowerCase())==="escape"&&((j=F.value)!=null&&j.enabled)&&((J=F.value)==null||J.disable())});const U=be(()=>!!F.value);function z(){F.value.enable()}const{iframe:G,getIframe:D}=Ng(y,async()=>{const N=D();cm(N),await I(N)});return(N,$)=>Qe((Ie(),At("div",{ref_key:"anchorEle",ref:t,class:pt(["vue-devtools__anchor",{"vue-devtools__anchor--vertical":Q(c),"vue-devtools__anchor--hide":Q(h),fullscreen:s.value.viewMode==="fullscreen","reduce-motion":Q(b).reduceMotion}]),style:Ne([Q(a),r.value]),onMousemove:$[2]||($[2]=(...j)=>Q(l)&&Q(l)(...j))},[Q(Lg)()?Lo("",!0):(Ie(),At("div",{key:0,class:"vue-devtools__anchor--glowing",style:Ne(Q(f)?"opacity: 0.6 !important":"")},null,4)),ie("div",{ref_key:"panelEle",ref:n,class:"vue-devtools__panel",style:Ne(Q(d)),onPointerdown:$[1]||($[1]=(...j)=>Q(i)&&Q(i)(...j))},[ie("div",{class:"vue-devtools__anchor-btn panel-entry-btn",title:"Toggle Vue DevTools","aria-label":"Toggle devtools panel",style:Ne(Q(g)?"":"filter:saturate(0)"),onClick:$[0]||($[0]=(...j)=>Q(_)&&Q(_)(...j))},$[3]||($[3]=[ie("svg",{viewBox:"0 0 256 198",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[ie("path",{fill:"#41B883",d:"M204.8 0H256L128 220.8L0 0h97.92L128 51.2L157.44 0h47.36Z"}),ie("path",{fill:"#41B883",d:"m0 0l128 220.8L256 0h-51.2L128 132.48L50.56 0H0Z"}),ie("path",{fill:"#35495E",d:"M50.56 0L128 133.12L204.8 0h-47.36L128 51.2L97.92 0H50.56Z"})],-1)]),4),Q(Su).ctx.state.vitePluginDetected&&U.value?(Ie(),At(Pe,{key:0},[$[5]||($[5]=ie("div",{class:"vue-devtools__panel-content vue-devtools__panel-divider"},null,-1)),ie("div",{class:pt(["vue-devtools__anchor-btn vue-devtools__panel-content vue-devtools__inspector-button",{active:U.value}]),title:"Toggle Component Inspector",onClick:z},[(Ie(),At("svg",{xmlns:"http://www.w3.org/2000/svg",style:Ne([{height:"1.1em",width:"1.1em",opacity:"0.5"},U.value?"opacity:1;color:#00dc82;":""]),viewBox:"0 0 24 24"},$[4]||($[4]=[ie("g",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2"},[ie("circle",{cx:"12",cy:"12",r:".5",fill:"currentColor"}),ie("path",{d:"M5 12a7 7 0 1 0 14 0a7 7 0 1 0-14 0m7-9v2m-9 7h2m7 7v2m7-9h2"})],-1)]),4))],2)],64)):Lo("",!0)],36),Te(Fg,{style:Ne(Q(u)),"is-dragging":Q(f),client:{close:Q(v),getIFrame:Q(D)},"view-mode":s.value.viewMode},null,8,["style","is-dragging","client","view-mode"])],38)),[[ot,Q(b).preferShowFloatingPanel?T.value:Q(g)]])}}),[["__scopeId","data-v-640ec535"]]);function Bg(e){const t="__vue-devtools-container__",n=document.createElement("div");n.setAttribute("id",t),n.setAttribute("data-v-inspector-ignore","true"),document.getElementsByTagName("body")[0].appendChild(n),cp({render:()=>Md(e),devtools:{hide:!0}}).mount(n)}Bg(Ug)})();
